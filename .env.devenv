AWS_ENDPOINT=http://127.0.0.1:9000
MYSQL_HOST=*************
MYSQL_PASSWORD=root

RABBITMQ_HOST=127.0.0.1
REDIS_HOST=127.0.0.1

BACKEND_BASE_URL=http://127.0.0.1:8081/api/v1/
BACKEND_INFO_URL=http://127.0.0.1:8081/api/v1/client/info
DATABUS_SERVER_BASE_URL=http://127.0.0.1:8625
NEST_GRPC_ADDRESS=static://127.0.0.1:3334
ROOM_GRPC_URL=127.0.0.1:3334
SOCKET_DOMAIN=http://127.0.0.1:3333/socket
SOCKET_GRPC_URL=127.0.0.1:3007
SOCKET_URL=http://127.0.0.1:3002

ASSETS_URL=http://127.0.0.1:9000/assets

API_PROXY=

API_DOCS_ENABLED=true
ENABLE_SWAGGER=true
OSS_HOST=http://127.0.0.1:9000/assets
QNY1=http://127.0.0.1:9000/assets/
QNY2=http://127.0.0.1:9000/assets/
QNY3=http://127.0.0.1:9000/assets/
IS_EXTERNAL_VERSION= 
HIDE_SIGNUP=true