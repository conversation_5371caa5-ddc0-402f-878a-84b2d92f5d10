PROFILES_ACTIVE=docker
IMAGE_PULL_POLICY=always
#IMAGE_PULL_POLICY=always
IMAGE_REGISTRY=swr.cn-north-4.myhuaweicloud.com

IMAGE_BACKEND_SERVER=szkz/backend-server:2.1.1
IMAGE_GATEWAY=szkz/openresty:latest
IMAGE_INIT_DB=szkz/init-db:latest
IMAGE_ROOM_SERVER=szkz/room-server:2.1.1
IMAGE_WEB_SERVER=apitable/web-server:2.1.1

IMAGE_DATABUS_SERVER=apitable/databus-server:latest
IMAGE_IMAGEPROXY_SERVER=szkz/imageproxy-server:latest
IMAGE_INIT_APPDATA=szkz/init-appdata:latest

IMAGE_MINIO=szkz/minio:RELEASE.2023-01-25T00-19-54Z
IMAGE_MYSQL=mysql:8.0.32
IMAGE_RABBITMQ=rabbitmq:3.11.9-management
IMAGE_REDIS=redis:7.0.8

DEFAULT_LOCALE=zh_CN
SYSTEM_CONFIGURATION_DEFAULT_LANGUAGE=zh_CN
NGINX_HTTPS_PORT=443
NGINX_HTTP_PORT=80

### SERVER
BACKEND_BASE_URL=http://127.0.0.1:8081/api/v1/
DATABUS_SERVER_BASE_URL=http://127.0.0.1:8625
NEST_GRPC_URL=127.0.0.1:3334
ROOM_GRPC_URL=127.0.0.1:3334
SOCKET_GRPC_URL=127.0.0.1:3007
API_DOCS_ENABLED=false

### NEST CONST
OSS_CACHE_TYPE=minio
OSS_HOST=assets
OSS_TYPE=QNY1

### MINIO
MINIO_ACCESS_KEY=apitable
MINIO_SECRET_KEY=apitable@com

### MYSQL
DATABASE_TABLE_PREFIX=apitable_
MYSQL_DATABASE=apitable
MYSQL_HOST= *************
MYSQL_PASSWORD=DolphinTable2024
MYSQL_PORT=3306
MYSQL_ROOT_PASSWORD=DolphinTable2024
MYSQL_USERNAME=root

### REDIS
REDIS_DB=5
REDIS_HOST=************
REDIS_PASSWORD=Idcquan123!@#qweASD
REDIS_PORT=9736

### RabbitMQ
RABBITMQ_HOST=*************
RABBITMQ_PASSWORD=apitable@com
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=apitable
RABBITMQ_VHOST=/

TIMEZONE=Asia/Singapore
DEFAULT_TIME_ZONE=Asia/Shanghai

ENV=apitable

API_PROXY=http://127.0.0.1:8081
BACKEND_INFO_URL=http://127.0.0.1:8081/api/v1/client/info
PUBLIC_URL=
TEMPLATE_PATH=./static/web_build/index.html
USE_CUSTOM_PUBLIC_FILES=true

NEST_GRPC_ADDRESS=static://127.0.0.1:3334

### apitable const
CALLBACK_DOMAIN=
CORS_ORIGINS=*
SERVER_DOMAIN=
SOCKET_DOMAIN=http://127.0.0.1:3333/socket

SKIP_REGISTER_VALIDATE=true
TEMPLATE_SPACE=spcNTxlv8Drra

### apitable starter
SOCKET_RECONNECTION_ATTEMPTS=10
SOCKET_RECONNECTION_DELAY=500
SOCKET_TIMEOUT=5000
SOCKET_URL=http://127.0.0.1:3002

SMS_ENABLED=false

MAIL_ENABLED=true
MAIL_HOST=smtp.exmail.qq.com
MAIL_PASSWORD=aB123456!
MAIL_PORT=465
MAIL_SSL_ENABLE=true
MAIL_TYPE=smtp
MAIL_USERNAME=<EMAIL>

OSS_CLIENT_TYPE=aws
OSS_ENABLED=true

AWS_ACCESS_KEY=apitable
AWS_ACCESS_SECRET=apitable@com
AWS_ENDPOINT=http://www.fintechquan.cn:9000
AWS_REGION=us-east-1

HUAWEICLOUD_OBS_ACCESS_KEY=apitable
HUAWEICLOUD_OBS_ENDPOINT=obs.cn-south-1.myhuaweicloud.com
HUAWEICLOUD_OBS_SECRET_KEY=apitable@com

ASSETS_BUCKET=assets
ASSETS_URL=assets
OSS_BUCKET_NAME=assets

DATA_PATH=.

USE_NATIVE_MODULE=false
EMAIL_PERSONAL=DolphinTable
FEISHU_AUTHPATH=http://e.bj1.cc
FEISHU_RETURNURL=/workbench
TABLE_ICP=''

SERVER_MAX_RECORD_COUNT=200000
MAX_ROW_COUNT=300000
LOG_LEVEL=error
LOGGING_MAX_FILE_SIZE=20m
LOGGER_MAX_HISTORY_DAYS=5d
feishuRedirectUri=http://e.bj1.cc/api/v1/oauth/callback
feishuReturnUrl=http://e.bj1.cc/history
SESSION_TIMEOUT=30d
LICENSE_PATH=/app/license.dat