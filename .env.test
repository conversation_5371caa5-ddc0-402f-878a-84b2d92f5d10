IMAGE_PULL_POLICY=always
IMAGE_REGISTRY=swr.cn-north-4.myhuaweicloud.com

IMAGE_BACKEND_SERVER=szkz/backend-server:3.0.0
IMAGE_GATEWAY=szkz/openresty:latest
IMAGE_INIT_DB=szkz/init-db:latest
IMAGE_ROOM_SERVER=szkz/room-server:3.0.0
IMAGE_WEB_SERVER=apitable/web-server:3.0.0

IMAGE_DATABUS_SERVER=apitable/databus-server:latest
IMAGE_IMAGEPROXY_SERVER=szkz/imageproxy-server:latest
IMAGE_INIT_APPDATA=szkz/init-appdata:latest

IMAGE_MINIO=szkz/minio:RELEASE.2023-01-25T00-19-54Z
IMAGE_MYSQL=swr.cn-north-4.myhuaweicloud.com/szkz/mysql:8.0.32
IMAGE_RABBITMQ=swr.cn-north-4.myhuaweicloud.com/szkz/rabbitmq:3.11.9-management
IMAGE_REDIS=swr.cn-north-4.myhuaweicloud.com/szkz/redis:7.2.0

DEFAULT_LOCALE=zh_CN
SYSTEM_CONFIGURATION_DEFAULT_LANGUAGE=zh_CN

NGINX_HTTPS_PORT=8443
NGINX_HTTP_PORT=8082

### SERVER
BACKEND_BASE_URL=http://backend-server:8081/api/v1/
DATABUS_SERVER_BASE_URL=http://databus-server:8625
BACKEND_GRPC_URL=backend-server:8083
ROOM_GRPC_URL=room-server:3334
SOCKET_GRPC_URL=room-server:3007
SESSION_TIMEOUT=30d

### NEST CONST
OSS_CACHE_TYPE=minio
OSS_HOST=assets
OSS_TYPE=QNY1

### MINIO
MINIO_ENDPOINT=http://minio:9000
MINIO_ACCESS_KEY=apitable
MINIO_SECRET_KEY=apitable@com

### MYSQL
DATABASE_TABLE_PREFIX=kz_
MYSQL_DATABASE=apitable
MYSQL_HOST=mysql
MYSQL_PASSWORD=ROOTcloudbest
MYSQL_PORT=3306
MYSQL_ROOT_PASSWORD=ROOTcloudbest
MYSQL_USERNAME=root

### init-db
DB_HOST=apitable-mysql
DB_NAME=apitable
DB_PASSWORD=apitable@com
DB_PORT=3306
DB_USERNAME=root

### REDIS
REDIS_DB=1
REDIS_HOST=redis
REDIS_PASSWORD=apitable@com
REDIS_PORT=6379


### RabbitMQ
RABBITMQ_HOST=rabbitmq
RABBITMQ_PASSWORD=apitable@com
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=apitable
RABBITMQ_VHOST=/

TIMEZONE=Asia/Shanghai
DEFAULT_TIME_ZONE=Asia/Shanghai
ENV=apitable

API_PROXY=http://backend-server:8081
BACKEND_INFO_URL=http://backend-server:8081/api/v1/client/info
PUBLIC_URL=
TEMPLATE_PATH=./static/web_build/index.html
USE_CUSTOM_PUBLIC_FILES=true

NEST_GRPC_ADDRESS=static://room-server:3334

### apitable const
CALLBACK_DOMAIN=fintechquan.cn
CORS_ORIGINS=*
SERVER_DOMAIN=http://www.fintechquan.cn
SOCKET_DOMAIN=http://room-server:3333/socket
DOMAIN_NAME=fintechquan.cn
COOKIES_HTTP_ONLY=false
COOKIES_SAME_SITE=Lax


SKIP_REGISTER_VALIDATE=true
TEMPLATE_SPACE=spcNTxlv8Drra

### apitable starter
SOCKET_RECONNECTION_ATTEMPTS=10
SOCKET_RECONNECTION_DELAY=500
SOCKET_TIMEOUT=5000
SOCKET_URL=http://room-server:3002

SMS_ENABLED=false
SMS_LOCAL_TYPE=TENCENT
MAIL_ENABLED=true
MAIL_HOST=smtp.exmail.qq.com
MAIL_PASSWORD=aB123456!
MAIL_PORT=465
MAIL_SSL_ENABLE=true
MAIL_TYPE=smtp
MAIL_USERNAME=<EMAIL>

OSS_CLIENT_TYPE=minio
OSS_ENABLED=true

AWS_ACCESS_KEY=apitable
AWS_ACCESS_SECRET=apitable@com
AWS_ENDPOINT=https://www.fintechquan.cn
AWS_REGION=us-east-1

HUAWEICLOUD_OBS_ACCESS_KEY=apitable
HUAWEICLOUD_OBS_ENDPOINT=obs.cn-south-1.myhuaweicloud.com
HUAWEICLOUD_OBS_SECRET_KEY=apitable@com

ASSETS_BUCKET=assets
ASSETS_URL=assets
OSS_BUCKET_NAME=assets
DATA_PATH=.

USE_NATIVE_MODULE=false
EMAIL_PERSONAL=DolphinTable
#YWEBSOCKET=ws://*************:1234
YWEBSOCKET=ws://yjs.fintechquan.cn

feishuRedirectUri=http://www.fintechquan.cn/api/v1/oauth/callback
feishuReturnUrl=http://www.fintechquan.cn/history
TABLE_ICP='京ICP备**********号-6'
LICENSE_PATH=/app/license.dat
SKIP_USAGE_VERIFICATION=false
SUPER_ADMINISTRATORS=1834098172792672257,1834098052814606337,1834789946167136257,1834096824730783746,1823978234056806402
seat=3
fileNodeNums=-1
rowsPerSheet=-1
mirrorNums=-1
totalRows=-1
widgetNums=-1
formNums=-1
fieldPermissionNums=-1
nodePermissionNums=-1
capacitySize=25600000
API_GRAYLOG_URL=http://www.fintechquan.cn:12201
LICENSE_CRON=0 0/5 * * * *


###主应用配置
WUJIEAPP_URL=http://wujie.fintechquan.cn
WUJIEAPP_VISIBLE_DOC=false
WUJIEAPP_VISIBLE_EXCEL=true
WUJIEAPP_VISIBLE_MINDMAP=true


###协同
# Hocuspocus server configuration
HOCUSPOCUS_PORT=1234

# MinIO 配置
# MinIO S3 configuration
S3_ENDPOINT=minio
S3_PORT=9000
S3_SSL=false
S3_ACCESS_KEY=apitable
S3_SECRET_KEY=apitable@com
S3_BUCKET=hocuspocus-docs

# 快照配置
HISTORY_BUCKET=hocuspocus-docs-history
SNAPSHOT_INTERVAL=300000  # 快照间隔（毫秒），默认5分钟

# Redis配置
REDIS_URL=redis://:apitable%40com@redis:6379/13

# HTTP API 服务器配置
HTTP_API_PORT=3001

ALIYUN_MAIL_ACCESS_KEY_ID=LTAI5tC22R662kvP83xvWhUg
ALIYUN_MAIL_ACCESS_KEY_SECRET=******************************
ALIYUN_MAIL_ACCOUNT_NAME=<EMAIL>

CASE_TEMPLATE_ID=tplCbp5TkMVwL