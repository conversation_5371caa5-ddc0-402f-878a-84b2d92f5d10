# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
.idea
__pycache__
*.pyc

.data
data
# logs
# .logs
/logs
*.log

# Ignore Gradle build output directory
**/.gradle
**/build
**/bin

packages/core/src/config/api_tip_config.*origin.json
packages/core/src/config/emojis.*origin.json
packages/core/src/config/system_config.*origin.json
packages/l10n/gen
packages/l10n/gen/**
packages/i18n-lang/src/config/strings.origin.json
packages/datasheet/.env.origin
/blueprint-templates

# dependencies
**/node_modules
/.pnp
.pnp.js

# testing
/packages/**/coverage

# production
/packages/*/build

# misc
.DS_Store
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

dist/

scripts/system_config/java/*.java
scripts/design_token/__pycache__

# yarn cache
.yarn/cache/
.yarn/build-state.yml
.yarn/install-state.gz
.yarn/unplugged

# cypress
/packages/cypress/cypress/videos/*
/packages/cypress/cypress/screenshots/*

# datasheet
/packages/datasheet/.next
/packages/datasheet/out
/packages/datasheet/web_build
/packages/datasheet/settings/custom
/packages/datasheet/settings/public
/packages/datasheet/.env.development
# /packages/datasheet/.env.local

# init-db
/init-db/bin

# typescript
/packages/**/tsconfig.tsbuildinfo
/packages/datasheet/web_server/static/web_build/widget-stage/


# $HOME dependencies generated by docker-compose.devenv.yaml
/.yarn/berry
/.cache
/.npm
/backend-server/?
# backend proto generated class
/backend-server/**/generated

# eslint results
eslint-results.sarif
/backend-server/application/src/main/resources/sysconfig/i18n/exception/messages.properties.origin
/backend-server/application/src/main/resources/sysconfig/i18n/exception/messages_zh_HK.properties
/backend-server/application/src/main/resources/sysconfig/notification.origin.json
/packages/datasheet/.env.development.origin
/packages/i18n-lang/src/config/strings.en-US.origin.json
/packages/i18n-lang/src/config/language.manifest.origin.json
/packages/room-native-api/target/**
/packages/room-native-api/Cargo.lock

# openapi generated
/backend-server/shared/starters/databus/.openapi-generator/

.nx
# .env

/scripts/enterprise


packages/widget-cb
packages/widget-pub

pnpm-lock.yaml


/backend-server/.vscode
/backend-server/application/generated-docs

/generated-docs

# specstory
**/.specstory
**/fixLogs
**/useJson