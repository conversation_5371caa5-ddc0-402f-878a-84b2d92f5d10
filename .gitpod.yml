# List the start-up tasks. Learn more https://www.gitpod.io/docs/config-start-tasks/
image:
  file: packaging/.gitpod.Dockerfile

tasks:
  - name: install dependency
    init: |
      npm install -g pnpm
      make install
    command: |
      echo "Happy Coding"
  - name: start APITable
    command: |
      make up
      echo "Welcome to APITable"

# List the ports to expose. Learn more https://www.gitpod.io/docs/config-ports/
ports:
  - port: 80
    onOpen: open-preview
