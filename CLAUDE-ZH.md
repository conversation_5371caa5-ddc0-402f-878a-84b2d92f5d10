# CLAUDE-ZH.md

本文件为Claude Code (claude.ai/code) 在处理此代码库时提供指导。

## 项目概览

APITable是一个开源的视觉数据库和协作平台，具有实时协作、API优先设计和类似电子表格的界面。它使用TypeScript（React/NextJS前端）和Java（Spring Boot后端）构建。

## 关键开发命令

### 前端开发
```bash
# 启动主数据表应用
pnpm run start:datasheet
# 或简写
pnpm run sd

# 以私有部署模式启动数据表
pnpm run sd:private

# 启动房间服务器（实时协作）
pnpm run start:room-server
# 或简写
pnpm run sr

# 启动组件故事书
pnpm run start:components
# 或简写
pnpm run sss

# 启动小部件SDK开发
pnpm run start:widget-sdk
```

### 构建命令
```bash
# 构建所有包
pnpm run build

# 专门构建Web应用
pnpm run build:web

# 为生产环境构建数据表
pnpm run build:dst

# 构建房间服务器
pnpm run build:room-server

# 为私有部署构建
pnpm run build:dst:private
```

### 测试命令
```bash
# 运行所有测试
pnpm run test

# 测试特定包
pnpm run test:core
pnpm run test:datasheet
pnpm run test:nest
pnpm run test:widget-sdk

# 运行带覆盖率的测试
pnpm run test:core:cov
pnpm run test:ut:room:cov
```

### 代码质量
```bash
# 检查所有包的代码规范
pnpm run lint

# 检查特定包的代码规范
pnpm run lint:datasheet

# 修复代码规范问题
pnpm run lint:fix

# 检查代码格式
pnpm run prettier:check

# 修复代码格式
pnpm run prettier:fix

# 检查样式（CSS/LESS）
pnpm run stylelint:datasheet
```

### 后端开发
```bash
# 在开发环境中启动后端服务器
cd backend-server && ./gradlew bootRun

# 构建后端
cd backend-server && ./gradlew build

# 运行后端测试
cd backend-server && ./gradlew test
```

## 架构概览

### Monorepo结构
项目使用由pnpm管理的monorepo：

- `packages/datasheet/` - 主React应用（NextJS）
- `packages/room-server/` - 实时协作服务器（NestJS）
- `packages/core/` - 核心业务逻辑和状态管理
- `packages/components/` - 共享UI组件库
- `packages/widget-sdk/` - 小部件开发SDK
- `packages/api-client/` - 生成的API客户端
- `packages/icons/` - 图标库
- `packages/i18n-lang/` - 国际化
- `backend-server/` - Java Spring Boot后端

### 关键技术
- **前端**: React 18, NextJS, TypeScript, Canvas渲染引擎
- **后端**: Java, Spring Boot, NestJS（用于房间服务器）
- **实时**: WebSocket, 操作转换（OT）算法
- **构建**: Nx工作空间, pnpm工作空间
- **样式**: LESS, Tailwind CSS

### 实时协作
应用实现了操作转换（OT）算法用于实时协作：
- 变更被跟踪为操作/命令
- 房间服务器管理冲突解决
- Canvas渲染引擎提供流畅的UI性能

## 开发工作流

### 开始使用
1. 使用Node.js 16.15.0和pnpm 8.6.12（如package.json engines中指定）
2. 安装依赖: `pnpm install`
3. 根据需要同时启动开发服务器

### 代码约定
- **React组件**: PascalCase（例如，`DataSheet`, `FieldEditor`）
- **TypeScript接口**: PascalCase带`I`前缀（例如，`IDataSheet`, `IField`）
- **函数**: camelCase（例如，`formatField`, `validateData`）
- **常量**: UPPER_SNAKE_CASE（例如，`MAX_FIELD_COUNT`）
- **Java类**: PascalCase（例如，`DataSheetService`）
- **Java方法**: camelCase（例如，`getFieldValue`）

### 性能考虑
- 大数据集（10万+行）的Canvas渲染
- 实时同步的操作转换算法
- 核心包中的内存高效状态管理
- 数据表包中的优化包分割

### API设计
- 具有完整CRUD操作的RESTful API
- 用于实时更新的WebSocket
- API优先设计 - 所有功能可通过API访问
- `api-client`包中生成的TypeScript客户端

## 小部件开发
小部件扩展APITable功能：
- 使用`widget-sdk`包进行开发
- 遵循现有小部件中的小部件开发模式
- 使用`start:widget-sdk`命令进行测试

## 重要文件
- `nx.json` - Nx工作空间配置
- `pnpm-workspace.yaml` - pnpm工作空间定义
- `package.json` - 根包，包含统一脚本
- `common-tsconfig.json` - 共享TypeScript配置
- `commitlint.config.js` - 提交消息标准

## 测试
- 使用Jest进行单元测试
- 使用Cypress进行E2E测试（在`packages/cypress/`中）
- 使用Storybook进行组件测试
- 在backend-server中进行API测试

## 部署
- 支持多阶段构建的Docker
- 可用于演示的一体化镜像
- 通过`build:dst:private`进行生产部署
- 在`.env`文件中进行环境配置

## Cursor规则集成
项目包含特定的变更管理规则：
- 协作优先的开发方法
- 优先考虑性能导向的变更
- 维护API兼容性
- 文档与代码变更同步
- 渐进式实施策略 