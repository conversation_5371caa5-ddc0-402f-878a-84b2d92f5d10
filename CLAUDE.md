# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

APITable is an open-source visual database and collaboration platform featuring real-time collaboration, API-first design, and a spreadsheet-like interface. It's built with TypeScript (React/NextJS frontend) and Java (Spring Boot backend).

## Key Development Commands

### Frontend Development
```bash
# Start main datasheet application
pnpm run start:datasheet
# or shorthand
pnpm run sd

# Start datasheet in private deployment mode
pnpm run sd:private

# Start room server (real-time collaboration)
pnpm run start:room-server
# or shorthand
pnpm run sr

# Start components storybook
pnpm run start:components
# or shorthand
pnpm run sss

# Start widget SDK development
pnpm run start:widget-sdk
```

### Build Commands
```bash
# Build all packages
pnpm run build

# Build web application specifically
pnpm run build:web

# Build datasheet for production
pnpm run build:dst

# Build room server
pnpm run build:room-server

# Build for private deployment
pnpm run build:dst:private
```

### Testing Commands
```bash
# Run all tests
pnpm run test

# Test specific packages
pnpm run test:core
pnpm run test:datasheet
pnpm run test:nest
pnpm run test:widget-sdk

# Run tests with coverage
pnpm run test:core:cov
pnpm run test:ut:room:cov
```

### Code Quality
```bash
# Lint all packages
pnpm run lint

# Lint specific package
pnpm run lint:datasheet

# Fix linting issues
pnpm run lint:fix

# Check code formatting
pnpm run prettier:check

# Fix code formatting
pnpm run prettier:fix

# Check styles (CSS/LESS)
pnpm run stylelint:datasheet
```

### Backend Development
```bash
# Start backend server in development
cd backend-server && ./gradlew bootRun

# Build backend
cd backend-server && ./gradlew build

# Run backend tests
cd backend-server && ./gradlew test
```

## Architecture Overview

### Monorepo Structure
The project uses a monorepo with packages managed by pnpm:

- `packages/datasheet/` - Main React application (NextJS)
- `packages/room-server/` - Real-time collaboration server (NestJS)
- `packages/core/` - Core business logic and state management
- `packages/components/` - Shared UI component library
- `packages/widget-sdk/` - Widget development SDK
- `packages/api-client/` - Generated API client
- `packages/icons/` - Icon library
- `packages/i18n-lang/` - Internationalization
- `backend-server/` - Java Spring Boot backend

### Key Technologies
- **Frontend**: React 18, NextJS, TypeScript, Canvas rendering engine
- **Backend**: Java, Spring Boot, NestJS (for room server)
- **Real-time**: WebSocket, Operational Transformation (OT) algorithm
- **Build**: Nx workspace, pnpm workspaces
- **Styling**: LESS, Tailwind CSS

### Real-time Collaboration
The application implements Operational Transformation (OT) for real-time collaboration:
- Changes are tracked as operations/commands
- Room server manages conflict resolution
- Canvas rendering engine provides smooth UI performance

## Development Workflow

### Getting Started
1. Use Node.js 16.15.0 and pnpm 8.6.12 (as specified in package.json engines)
2. Install dependencies: `pnpm install`
3. Start development servers concurrently as needed

### Code Conventions
- **React Components**: PascalCase (e.g., `DataSheet`, `FieldEditor`)
- **TypeScript Interfaces**: PascalCase with `I` prefix (e.g., `IDataSheet`, `IField`)
- **Functions**: camelCase (e.g., `formatField`, `validateData`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_FIELD_COUNT`)
- **Java Classes**: PascalCase (e.g., `DataSheetService`)
- **Java Methods**: camelCase (e.g., `getFieldValue`)

### Performance Considerations
- Canvas rendering for large datasets (100k+ rows)
- Operational Transformation algorithm for real-time sync
- Memory-efficient state management in core package
- Optimized bundle splitting in datasheet package

### API Design
- RESTful APIs with full CRUD operations
- WebSocket for real-time updates
- API-first design - all features accessible via API
- Generated TypeScript client in `api-client` package

## Widget Development
Widgets extend APITable functionality:
- Use `widget-sdk` package for development
- Follow widget development patterns in existing widgets
- Test with `start:widget-sdk` command

## Important Files
- `nx.json` - Nx workspace configuration
- `pnpm-workspace.yaml` - pnpm workspace definition
- `package.json` - Root package with unified scripts
- `common-tsconfig.json` - Shared TypeScript configuration
- `commitlint.config.js` - Commit message standards

## Testing
- Unit tests with Jest
- E2E tests with Cypress (in `packages/cypress/`)
- Component testing with Storybook
- API testing in backend-server

## Deployment
- Docker support with multi-stage builds
- All-in-one image available for demos
- Production deployment via `build:dst:private`
- Environment configuration in `.env` files

## Cursor Rules Integration
The project includes specific change management rules:
- Collaborative-first development approach
- Performance-oriented changes prioritized
- API compatibility maintained
- Documentation synchronized with code changes
- Progressive implementation strategy