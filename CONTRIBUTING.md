# Contributing to APITable

Welcome, and thank you for your interest in contributing to APITable!

There are many ways in which you can contribute, beyond writing code. 

The goal of this document is to provide a high-level overview of how you can get involved.

For new contributors, please take a look at issues with a tag called [Good first issue](https://github.com/apitable/apitable/issues?q=is%3Aopen+is%3Aissue+label%3A%22good+first+issue%22).

## Providing Feedback

<div>
    <!-- Discord -->
    <a target="_blank" href="https://discord.gg/TwNb9nfdBU">
        <img src="https://img.shields.io/discord/1016320471010115666?label=discord&logo=discord&style=social" />
    </a>
    <!-- Twitter -->
    <a target="_blank" href="https://twitter.com/apitable_com">
        <img src="https://img.shields.io/twitter/follow/apitable_com?label=Twitter&style=social" />
    </a>
</div>

We look forward to receiving your comments and feedback.
Join [APITable Discord](https://discord.gg/TwNb9nfdBU) and follow [APITable on Twitter](https://twitter.com/apitable_com) to leave some feedback.



## Reporting Issues

Have you identified any bugs in APITable?
Have a feature idea?

Thank you for report new issue!

Do a search in [APITable open issues](https://github.com/apitable/apitable/issues) to see if the issue or feature request has already been filed.

If you cannot find an existing issue that describes your bug or feature, create a new issue.



### Security issues
If you believe you've found a security vulnerability, please read our [security policy](./SECURITY.md) for more details.


## Answering questions

You can help us answer questions through the links below.

- [APITable Discord](https://discord.gg/TwNb9nfdBU)
- [Stack Overflow #apitable](https://stackoverflow.com/questions/tagged/apitable)
- [APITable on Twitter](https://twitter.com/apitable_com)
- [APITable open issues](https://github.com/apitable/apitable/issues)

## Write Documentation, Tweet, Blog

You can write documentation into the [/docs](./docs) directory.

You can write tweets or blogs that share APITable to your friends.


## Contributing Code

See [Developer Guide](./docs/contribute/developer-guide.md) to set up your development environment.


## Localization & Translation 

Get into our [Crowdin Translation Project](https://crowdin.com/project/apitablecode/invite?h=f48bc26f9eb188dcd92d5eb4a66f2c1f1555185) to help us translate.


## Thank You!

For more information, please contact us at <<EMAIL>>. 

