# APITable licenses

Here are the main licenses of note.

## APITable Open Source Edition

##### [GNU Affero General Public License](./LICENSE) (AGPL)

If you remove the “Powered by APITable” logo on embedded APITable page, you’d then need to release your own embedding application under the AGPL. To avoid this, you can instead purchase an Enterprise Edition package.

##### [Contributor License Agreement](https://cla-assistant.io/apitable/apitable) (CLA)

You must sign a [Contribution License Agreement (CLA)](https://cla-assistant.io/apitable/apitable) before your PR will be merged. This a one-time requirement for APITable on GitHub. You can read more about [Contribution License Agreements](https://en.wikipedia.org/wiki/Contributor_License_Agreement) on Wikipedia.

However, you don't have to do this up-front; you can simply clone, fork, and submit your pull-request as usual.

Signing the CLA might sound scary but it's actually very simple and can be done in less than a minute.


## APITable Embedding License

##### [Advanced embedding license](./licenses/LICENSE-EMBEDDING)

Our Advanced Embedding License allows you to remove the “Powered by APITable” logo on embedded on embedded APITable page without making the embedding application subject to the AGPL.

For an overview of Enterprise features, contact <<EMAIL>>, or check out our Enterprise Guide.