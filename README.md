<p align="center">
    <a href="https://aitable.ai" target="_blank">
        <img src="docs/static/cover.png" alt="APITable Cover Image" />
    </a>
</p>

<p align="center">
    <!-- Gitpod -->
    <a target="_blank" href="https://gitpod.io/#https://github.com/apitable/apitable">
        <img src="https://img.shields.io/badge/gitpod-devenv-orange" alt="APITable Gitpod Development Environment" />
    </a>
    <!-- NodeJS -->
    <img src="https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white" alt="TypeScript Language, NestJS Framework" />
    <!-- Java -->
    <img src="https://img.shields.io/badge/Java-ED8B00?logo=spring&logoColor=white" alt="Java Language, Spring Framework" />
    <!-- hub.docker.com-->
    <a target="_blank" href="#installation">
        <img src="https://img.shields.io/docker/pulls/apitable/init-db" />
    </a>
    <!-- Github Release Latest -->
    <a target="_blank" href="https://github.com/apitable/apitable/releases/latest">
        <img src="https://img.shields.io/github/v/release/apitable/apitable" />
    </a>
    <!-- Deploy to DO -->
    <a target="_blank" href="https://cloud.digitalocean.com/apps/new?repo=https://github.com/apitable/apitable/tree/develop&refcode=3fe758500293">
        <img src="https://img.shields.io/badge/DigitalOcean-deploy-5364e9" alt="Deploy to DO" />
    </a>
    <br />
    <!-- LICENSE -->
    <a target="_blank" href="https://github.com/apitable/apitable/blob/main/LICENSE">
        <img src="https://img.shields.io/badge/LICENSE-AGPL--3.0-ff69b4" alt="APITable License Badge AGPL" />
    </a>
    <!-- Discord -->
    <a target="_blank" href="https://discord.gg/zYWYTHXR4f">
        <img src="https://img.shields.io/discord/1016320471010115666?label=discord&logo=discord&style=social" />
    </a>
    <!-- Twitter -->
    <a target="_blank" href="https://twitter.com/apitable_com">
        <img src="https://img.shields.io/twitter/follow/apitable_com?label=Twitter&style=social" />
    </a>
    <!-- Github Action Build-->
    <a target="_blank" href="https://github.com/apitable/apitable/actions/workflows/build.yaml">
        <img src="https://github.com/apitable/apitable/actions/workflows/build.yaml/badge.svg" />
    </a>
    <!-- Better Uptime-->
    <a target="_blank" href="https://apitable.betteruptime.com/">
        <img src="https://betteruptime.com/status-badges/v1/monitor/a1a9.svg" />
    </a>
    <br>
    <!-- Deploy to Dome-->
    <a target="_blank" href="https://app.trydome.io/signup?package=apitable">
        <img src="https://trydome.io/dome-badge.svg" />
    </a>
</p>

<p align="center">
  English
  | 
  <a href="docs/readme/fr-FR/README.md">Français</a>
  | 
  <a href="docs/readme/es-ES/README.md">Español</a>
  | 
  <a href="docs/readme/de-DE/README.md">Deutsch</a>
  | 
  <a href="docs/readme/zh-CN/README.md">简体中文</a>
  | 
  <a href="docs/readme/zh-HK/README.md">繁體中文</a>
  | 
  <a href="docs/readme/ja-JP/README.md">日本語</a>
</p>

## ✨ Quick Start

If you just want to try out APITable[^info], use our cloud-hosted AI version at [aitable.ai](https://aitable.ai).

If you want to demo this APITable open-source project, click here for [⚡️Gitpod Online Demo](https://gitpod.io/#https://github.com/apitable/apitable).

If you want to try the self-hosted APITable, [🚀 one-click deploy with Dome here](https://app.trydome.io/signup?package=apitable)

If you want to install APITable in your local or cloud computing environment, see [💾 Installation](#installation)

If you want to set up your local development environment, read our [🧑‍💻 Developer Guide](./docs/contribute/developer-guide.md)

Join [Discord](https://discord.gg/TwNb9nfdBU) or [Twitter](https://twitter.com/apitable_com) to keep in touch.
## 🔥 Features

<table>
  
  <tr>
    <th>
      <a href="#">Realtime Collaboration</a>
    </th>
    <th>
      <a href="#">Automatic Form</a>
    </th>

  </tr>

   <tr>
    <td width="50%">
      <a href="#">
        <img src="docs/static/feature-realtime.gif" />
      </a>
    </td>
    <td width="50%">
        <a href="#">
            <img src="docs/static/feature-form.gif" />
        </a>
    </td>
  </tr>

  <tr>
    <th>
      <a href="#">API-first Panel</a>
    </th>
    <th>
      <a href="#">Unlimited cross-table links</a>
    </th>
</tr>

 <tr>
    <td width="50%">
        <a href="#">
            <img src="docs/static/feature-api-first-panel.gif" />
        </a>
    </td>
    <td width="50%">
      <a href="#">
        <img src="docs/static/feature-unlimited-cross-table-links.gif" />
      </a>
    </td>
 </tr>

 <tr>
    <th>
      <a href="#">Powerful Rows/Columns Permissions</a>
    </th>
    <th>
      <a href="#">Embed</a>
    </th>
  </tr>

 <tr>
    <td width="50%">
        <a href="#">
            <img src="docs/static/feature-permissions.gif" />
        </a>
    </td>
    <td width="50%">
        <a href="#">
            <img src="docs/static/feature-embed.gif" />
        </a>
    </td>
  </tr>

</table>

APITable provides a range of amazing features, from the personal to the enterprise.

- Advanced technology stack and open-source
  - `Realtime collaboration` allows multiple users to edit together in real time, or simultaneously with the `Operational Transformation (OT)` Algorithm.
  - Extremely smooth, user-friendly, super-fast database-spreadsheet interface in `<canvas> Rendering Engine`.
  - Database native architecture: Changeset / Operation / Action / Snapshot and so on.
  - **100k+** data rows with real-time collaboration.
  - Full-stack API access, from `Data` to `Metadata`.
  - One-direction / Bi-direction Table Link and `Infinite Cross Links`
  - Community-friendly programming languages and framework, TypeScript ([NextJS](https://nextjs.org/) + [NestJS](https://nestjs.com/)) and Java ([Spring Boot](https://spring.io/projects/spring-boot)).
- Beautiful and Rich Database-Spreadsheet UI
  - `CRUD`: Create, Read, Update, Delete the Tables, Columns, and Rows
  - `Fields Operations`: sort, filter, grouping, hide/unhide, height setting.
  - `Space based`: Use separated workspaces in place of App/Base-based structure, make unlimited tables link together possible.
  - `Dark mode` and theme customization available.
  - `7 View Types`: Grid View (Datasheet) / Gallery View / Mindmap View / Kanban View / Full-Feature Gantt View / Calendar View
  - One-click API Panel
- Batteries included
  - Built-in 10+ official templates.
  - Robot Automation and customization available.
  - BI dashboard
  - One-click auto-generated form
  - Shareable and embeddable page.
  - Multi-language support.
  - Integration with n8n.io / Zapier / Appsmith... and more.
- Excellent extensibility
  - Extensible `Widget System` with over 20 officials open-source widgets.
  - Customizable Graph & Chart & Dashboard
  - Customizable Data Column Types
  - Customizable Formulas
  - Customizable Automation Robot Actions.
- Enterprise-grade permissions
  - `Mirror`, turn a View into a mirror to implement Row Permission.
  - Activate `Column Permission` through a very simple operation.
  - Folders / Sub-Folders / Files Permission.
  - Tree structure folders and customizable node (file);
  - Team Management & Organization Structure.
- Enterprise features:
  - SAML
  - Single-Sign-On (SSO)
  - Audit
  - Database Auto Backup
  - Data Exporter
  - Watermark
- ....

With extensible widgets and plugins, you can add more features.

## 💥 Use Cases

Why you must know APITable for your next software?

- As super management software
  - Flexible Project Management & Tasks / Issues Management.
  - Marketing Lead Management.
  - Most flexible and connectable CRM.
  - Flexible Business Intelligence (BI).
  - People-Friendly Forms and Surveys
  - Flexible ERP.
  - Low-code and no-code platform.
  - ...and more, APITable puts 1000 softwares in your pocket.
- As a visual database infrastructure
  - **Embed** APITable into your own software UIs.
  - Visual Database with REST API.
  - Admin dashboard.
  - Central configuration management.
  - All-in-one enterprise database that **connect all** your software.
  - ...and more, APITable connects everything.
- Also, it is open source and extensible

## 💞 API-oriented

#### API UI Panel

Clicking the `API` button in the right corner will show the API Panel

#### SQL-like query

APITable will provides a Datasheet Query Language (DQL) to query your database-spreadsheet contents.

## 💝 Embed-friendly

#### Share and Embed

Share your datasheet table or folder.
Embed them by copying and pasting HTML scripts.

#### Enterprise-ready Embedding

[AITable.ai](https://aitable.ai) provides more Enterprise-ready Embedding features for securities.

## Installation

Before you begin:
* A host with [docker](https://docs.docker.com/engine/install/) and [docker-compose v2](https://docs.docker.com/engine/install/) installed.
* 4 CPUs/8GB RAM or more are recommended.
* A bash shell with basic utilities like curl installed.
* Native arm64 (apple silicon) container images is not ready yet and may cause bad performance.

To install apitable using docker compose, open your terminal and run this:

```
curl https://apitable.github.io/install.sh | bash
```

Then open [http://localhost:80](http://localhost:80) in your browser to visit it.

We also provide an all-in-one image based on [pm2](https://pm2.keymetrics.io/) for demo or testing purpose (not recommended for enterprise or production usage):

```bash
sudo docker run -d -v ${PWD}/.data:/apitable -p 80:80 --name apitable apitable/all-in-one:latest
```

Depending on your environment, you may need to wait several minutes for all the services to start. This image is amd64 (x86_64) only, you may encounter pretty bad performance on arm64 or apple silicon.

If you want to set up your local development environment, read our [🧑‍💻 Developer Guide](./docs/contribute/developer-guide.md)

## 🧑‍💻 Contributing

Welcome, and thank you for your interest in contributing to APITable!

In addition to writing code, there are many ways for you to contribute.

You can contribute as following:
- Join and modify translations in our [Crowdin Translation Project](https://crowdin.com/project/apitablecom/invite?h=4a985ea532a01d973acc03f2f1c960951693577)
- Create [Issues](https://github.com/apitable/apitable/issues/new/choose)
- Follow our [Twitter](https://twitter.com/apitable_com)
- Create [Documentation](./docs)
- [Contributing Code](./docs/contribute/developer-guide.md)


You can read this repository’s [Contributing Guidelines](./CONTRIBUTING.md) to learn how to contribute.

Here's a quick guide to help you contribute to APITable.


### Development environment

Learn how to set up your local environment, go to our [Developer Guide](./docs/contribute/developer-guide.md).

### Git workflow basic

Here's a general APITable git workflow:

1. Create an issue and describe features you want -> [APITable issues](https://github.com/apitable/apitable/issues)
2. Fork this project -> [Fork APITable project](https://github.com/apitable/apitable/fork)
3. Create your feature branch (`git checkout -b my-new-feature`)
4. Commit your changes (`git commit -am 'Add some features'`)
5. Publish the branch (`git push origin my-new-feature`)
6. Create a new Pull Request -> [Create pull request across forks](https://github.com/apitable/apitable/compare)

### Work conventions

APITable use these common conventions:

- What's our Git branching model? [Gitflow](https://nvie.com/posts/a-successful-git-branching-model/)
- How to collaborate on your fork projects? [Github Flow](https://docs.github.com/en/get-started/quickstart/github-flow)
- How to write good commit message? [Conventional Commits](https://www.conventionalcommits.org/)
- What's our changelog format? [Keep Changelog](https://keepachangelog.com/en/1.0.0/)
- How to versioning and tagging? [Semantic Versioning](https://semver.org/)
- What is the Java Coding Guideline? [Java Coding Guideline](https://google.github.io/styleguide/javaguide.html) | [Intellij IDEA Plugin](https://plugins.jetbrains.com/plugin/8527)
- What is the TypeScript Coding Guideline? -> [TypeScript Style Guide](https://google.github.io/styleguide/tsguide.html) | [ESLint](https://www.npmjs.com/package/@typescript-eslint/eslint-plugin)

### Documentations

- [Help Center](https://help.aitable.ai/)
- [👩‍💻 Developer Center](https://developers.aitable.ai/)
  - [🪡 REST API Docs](https://developers.aitable.ai/api/introduction/)
  - [Widget SDK](https://developers.aitable.ai/widget/introduction/)
  - [Scripting Widget](https://developers.aitable.ai/script/introduction/)
- [Design System](https://figma.com/@apitable)

## 🛣 Roadmap

Please refer to the [Roadmap of AITable](https://aitable.ai/roadmap)

### Future Features

- Heavy-code Interface Builder
- Embeddable 3rd party documentation components
- SQL-like Domain-Specific Languages
- As an IdP
- Advanced automation robot
- Web 3 features
- ...

### Hosted and Enterprise versions offer advanced features

- As an IdP;
- SAML
- Single-Sign-On
- Audit
- Database Backup
- Integrate with ChatGPT, Zapier, Slack, Google Workspace……
- Watermark

For more information on our product, including enterprise self-hosted license, please contact us at <<EMAIL>> or [book a demo](https://aitable.ai/share/shrdaGGppsfg3pjQLXALG?fldy5ZmHYGZx2=<EMAIL>).

## 👫 Get Involved

### 🌏 Why we create APITable and open-source?

- We believe that `Database is the cornerstone` of all the software.
- We believe that making a `Visual Database with rich and easy user interface for everyone` can reduce the difficulty of software industry and increase the world's digitalization adoption.
- We believe that open-sourcing `APITable` work can `Push Human Beings Forward`.

### We are hiring remotely!

We always search for good talents for APITable:

- **Full-stack developer**: You have experience with React, NestJS, TypeScript, Spring Boot, Java, Terraform. And you like to write high quality code with clear documentation and unit tests.
- **Back-end developer**: You have experience with NestJS, TypeScript, Spring Boot, Java, SQL, Kubernetes, Terraform. And you like to write high quality code with clear documentation and unit tests.
- **Front-end developer**: You have experience with React, NextJS, TypeScript, WebPack. And you like to write high quality code with clear documentation and unit tests.

Regardless of time and conditions, if you want to get involved to the team of APITable, do not hesitate to [fill out this form](https://aitable.ai/share/shrDNBDeVlLvB1eFDMWhl) or send your CV to <<EMAIL>>.


## 📺 Screenshot

<p align="center">
    <img src="docs/static/screenshot-realtime.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-auto-form.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-api-panel.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-permissions.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-extensible.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-automation.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-marketing.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-hr.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-it.png" alt="APITable Screenshot Image" />
</p>
<p align="center">
    <img src="docs/static/screenshot-sales.png" alt="APITable Screenshot Image" />
</p>

## 🥰 License

> This repository contains the source code for the Open Source edition of APITable, released under the AGPL.
>
> If you'd like to run your own copy of APITable or contribute to development then this is the place for you.
>
> See [LICENSING](./LICENSING.md) for details.
>
> If you want to use APITable online then you don't need to run this code, we offer a hosted version of the app at [AITable.ai](https://aitable.ai) which optimized for global accelerator.

<br/>

[^info]: Licensed with AGPL-3.0. Designed by [APITable Ltd](https://aitable.ai).
