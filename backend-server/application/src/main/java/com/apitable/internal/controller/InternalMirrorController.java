/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.internal.controller;

import com.apitable.core.support.ResponseData;
import com.apitable.core.util.HttpContextUtil;
import com.apitable.internal.ro.UserMirrorSettingsRo;
import com.apitable.internal.ro.UserMirrorSettingsUpdateRo;
import com.apitable.internal.service.IUserMirrorSettingsService;
import com.apitable.internal.vo.UserMirrorSettingsVo;
import com.apitable.shared.component.scanner.annotation.ApiResource;
import com.apitable.shared.component.scanner.annotation.GetResource;
import com.apitable.shared.component.scanner.annotation.PostResource;

import com.apitable.shared.constants.SessionAttrConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Internal Service - Mirror Interface.
 */
@Slf4j
@RestController
@ApiResource(path = "/internal")
@Tag(name = "Internal")
public class InternalMirrorController {

    @Resource
    private IUserMirrorSettingsService userMirrorSettingsService;

    /**
     * Get user mirror settings.
     */
    @GetResource(path = "/mirrors/{mirrorId}/userSettings", requiredLogin = true, requiredPermission = false)
    @Operation(summary = "Get user mirror settings", description = "Get user's personalized configuration for a specific mirror")
    @Parameter(name = "mirrorId", description = "mirror id", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.PATH, example = "mir123456")
    @Parameter(name = "viewId", description = "view id", required = false,
            schema = @Schema(type = "string"), in = ParameterIn.QUERY, example = "viwX4ChJGYinF")
    public ResponseData<UserMirrorSettingsVo> getUserMirrorSettings(
            @PathVariable("mirrorId") String mirrorId,
            @RequestParam(value = "viewId", required = false) String viewId) {
        
        String userId = HttpContextUtil.getSession(true).getAttribute(SessionAttrConstants.LOGIN_USER_ID).toString();
        log.info("Getting user mirror settings for user: {}, mirror: {}, view: {}", userId, mirrorId, viewId);
        
        UserMirrorSettingsVo settings = userMirrorSettingsService.getUserMirrorSettings(userId, mirrorId, viewId);
        
        if (settings == null) {
            log.debug("No user mirror settings found for user: {}, mirror: {}, view: {}", userId, mirrorId, viewId);
            // 返回404表示资源不存在，便于前端区分情况
            return ResponseData.<UserMirrorSettingsVo>error(404, "User mirror settings not found");
        }
        
        return ResponseData.success(settings);
    }

    /**
     * Save user mirror settings (仅用于初始化).
     */
    @PostResource(path = "/mirrors/{mirrorId}/userSettings", requiredLogin = true, requiredPermission = false)
    @Operation(summary = "Initialize user mirror settings", description = "Initialize user's personalized configuration for a specific mirror (first time access only)")
    @Parameter(name = "mirrorId", description = "mirror id", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.PATH, example = "mir123456")
    public ResponseData<Void> saveUserMirrorSettings(
            @PathVariable("mirrorId") String mirrorId,
            @Valid @RequestBody UserMirrorSettingsRo ro) {
        
        String userId = HttpContextUtil.getSession(true).getAttribute(SessionAttrConstants.LOGIN_USER_ID).toString();
        log.info("Saving user mirror settings for user: {}, mirror: {}, view: {}", userId, mirrorId, ro.getViewId());
        
        userMirrorSettingsService.saveOrUpdateUserMirrorSettings(userId, mirrorId, ro.getViewId(), ro.getConfigData());
        
        return ResponseData.success();
    }

    /**
     * Update user mirror settings incrementally (主要方法).
     */
    @PostResource(path = "/mirrors/{mirrorId}/userSettings/incremental", requiredLogin = true, requiredPermission = false)
    @Operation(summary = "Update user mirror settings incrementally", description = "Apply incremental changes to user's mirror configuration (列宽、行高、筛选、排序、分组等)")
    @Parameter(name = "mirrorId", description = "mirror id", required = true,
            schema = @Schema(type = "string"), in = ParameterIn.PATH, example = "mir123456")
    public ResponseData<Void> updateUserMirrorSettings(
            @PathVariable("mirrorId") String mirrorId,
            @Valid @RequestBody UserMirrorSettingsUpdateRo ro) {
        
        String userId = HttpContextUtil.getSession(true).getAttribute(SessionAttrConstants.LOGIN_USER_ID).toString();
        log.info("Updating user mirror settings incrementally for user: {}, mirror: {}, view: {}, changes: {}", 
                userId, mirrorId, ro.getViewId(), ro.getChanges().size());
        
        userMirrorSettingsService.updateUserMirrorSettingsIncremental(userId, mirrorId, ro.getViewId(), ro.getChanges());
        
        return ResponseData.success();
    }
} 