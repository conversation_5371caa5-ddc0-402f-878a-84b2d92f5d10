{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "target": "es6", "declaration": true, "declarationMap": true, "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitAny": false, "suppressImplicitAnyIndexErrors": true, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "downlevelIteration": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitOverride": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noUnusedParameters": false, "strictBindCallApply": false, "strictFunctionTypes": false}}