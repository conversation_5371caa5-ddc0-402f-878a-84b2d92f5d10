version: "3.9"

services:
  web-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_WEB_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "8080"
    env_file:
      - "${ENV_FILE:-.env}"
    network_mode: host

  imageproxy-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_IMAGEPROXY_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    ports:
      - 8082:8080
    expose:
      - "8080"
    environment:
      - TZ=${TIMEZONE}
      - BASEURL=${AWS_ENDPOINT}
    networks:
      - apitable

  backend-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_BACKEND_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    volumes:
      - ${DATA_PATH}/license.dat:/app/license.dat
    env_file:
      - "${ENV_FILE:-.env}"
    expose:
      - "8081"
    environment:
      - TZ=${TIMEZONE}
    network_mode: host
    healthcheck:
      test: ["CMD-SHELL", "curl -sS 'http://localhost:8081' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60

  room-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_ROOM_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "3333"
      - "3334"
      - "3001"
      - "3002"
      - "3006"
      - "3005"
      - "3007"
    env_file:
      - "${ENV_FILE:-.env}"
    environment:
      - TZ=${TIMEZONE}
      - NODE_ENV=${ENV}
      - NODE_OPTIONS=--max-old-space-size=2048 --max-http-header-size=80000
      - API_MAX_MODIFY_RECORD_COUNTS=${API_MAX_MODIFY_RECORD_COUNTS:-30}
      - INSTANCE_MAX_MEMORY=8192M
      - ENABLE_SOCKET=true
      # - LIMIT_POINTS=1000
    network_mode: host
  databus-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_DATABUS_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "8625"
    env_file:
      - "${ENV_FILE:-.env}"
    environment:
      - TZ=${TIMEZONE}
    network_mode: host
  # Third Party Dockers
  gateway:
    image: ${IMAGE_REGISTRY}/${IMAGE_GATEWAY}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    environment:
      - TZ=${TIMEZONE}
    volumes:
      - ${DATA_PATH}/.data/geteway:/etc/nginx/conf.d/
    network_mode: host
    depends_on:
      web-server:
        condition: service_started
      imageproxy-server:
        condition: service_started
      backend-server:
        condition: service_healthy
      room-server:
        condition: service_started

  minio:
    container_name: minio
    image: ${IMAGE_REGISTRY}/${IMAGE_MINIO}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    command: server /data
    expose:
      - "9000"
    volumes:
      - ${DATA_PATH}/.data/minio/data:/data
      - ${DATA_PATH}/.data/minio/config:/root/.minio
    environment:
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - TZ=${TIMEZONE}
    network_mode: host
    healthcheck:
      test: ["CMD-SHELL", "curl -sS 'http://localhost:9000' || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 30
networks:
  apitable:
    name: apitable_default
    driver: bridge