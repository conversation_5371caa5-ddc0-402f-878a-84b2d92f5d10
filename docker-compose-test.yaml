version: "3.9"

services:
  web-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_WEB_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "8080"
    env_file:
      - "${ENV_FILE:-.env}"
    #   environment:
    #     - TZ=Asia/Shanghai
    #    - DEFAULT_TIME_ZONE=${TIMEZONE}
    volumes:
      - /etc/localtime:/etc/localtime:ro
    network_mode: host

  imageproxy-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_IMAGEPROXY_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    ports:
      - 8082:8080
    environment:
      - TZ=${TIMEZONE}
      - BASEURL=${AWS_ENDPOINT}
    networks:
      - apitable

  backend-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_BACKEND_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    env_file:
      - "${ENV_FILE:-.env}"
    volumes:
      - ${DATA_PATH}/license.dat:/app/license.dat
    expose:
      - "8081"
    environment:
      - TZ=${TIMEZONE}
      - DEFAULT_TIME_ZONE=${TIMEZONE}
    network_mode: host
    healthcheck:
      test: ["CMD-SHELL", "curl -sS 'http://localhost:8081' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60

  room-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_ROOM_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "3333"
      - "3334"
      - "3001"
      - "3002"
      - "3006"
      - "3005"
      - "3007"
    env_file:
      - "${ENV_FILE:-.env}"
    environment:
      - TZ=${TIMEZONE}
      - NODE_ENV=${ENV}
      - NODE_OPTIONS=--max-old-space-size=2048 --max-http-header-size=80000
      - API_MAX_MODIFY_RECORD_COUNTS=${API_MAX_MODIFY_RECORD_COUNTS:-30}
      - INSTANCE_MAX_MEMORY=4096M
      - ENABLE_SOCKET=true
    network_mode: host

  databus-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_DATABUS_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "8625"
    env_file:
      - "${ENV_FILE:-.env}"
    environment:
      - TZ=${TIMEZONE}
    network_mode: host


  gateway:
    image: ${IMAGE_REGISTRY}/${IMAGE_GATEWAY}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    # ports:
    #   - ${NGINX_HTTP_PORT:-80}:80
    #   - ${NGINX_HTTPS_PORT:-443}:443
    environment:
      - TZ=${TIMEZONE}
    volumes:
      - ${DATA_PATH}/.data/geteway:/etc/nginx/conf.d/
    # networks:
    #   - apitable
    network_mode: host
    depends_on:
      web-server:
        condition: service_started
      imageproxy-server:
        condition: service_started
      backend-server:
        condition: service_healthy
      room-server:
        condition: service_started

  minio:
    container_name: minio
    image: ${IMAGE_REGISTRY}/${IMAGE_MINIO}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    command: server --console-address ":9001" /data
    expose:
      - "9000"
      - "9001"
    volumes:
      - ${DATA_PATH}/.data/minio/data:/data
      - ${DATA_PATH}/.data/minio/config:/root/.minio
      - /etc/localtime:/etc/localtime:ro
    #  - /etc/timezone:/etc/timezone:ro
    environment:
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    #  - TZ=${TIMEZONE}
    privileged: true
    network_mode: host
    healthcheck:
      test: ["CMD-SHELL", "curl -sS 'http://localhost:9000' || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 30

  # redis:
  #   container_name: apitable-redis
  #   image: ${IMAGE_REDIS}
  #   pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
  #   restart: always
  #   command:
  #     [
  #         "redis-server",
  #         "--appendonly",
  #         "yes",
  #         "--requirepass",
  #         "${REDIS_PASSWORD}",
  #     ]
  #   expose:
  #     - "6379"
  #   volumes:
  #     - ${DATA_PATH}/.data/redis:/data
  #   environment:
  #     - TZ=${TIMEZONE}
  #   networks:
  #     - apitable

  mysql:
    container_name: apitable-mysql
    image: ${IMAGE_MYSQL}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    volumes:
      - ${DATA_PATH}/.data/mysql:/var/lib/mysql
    expose:
      - "3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - TZ=${TIMEZONE}
    command: --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --sql_mode=IGNORE_SPACE,NO_ENGINE_SUBSTITUTION,NO_ZERO_IN_DATE,NO_ZERO_DATE
      --lower_case_table_names=2
    healthcheck:
      test: "mysql ${MYSQL_DATABASE} -u${MYSQL_USERNAME} -p${MYSQL_PASSWORD} -e 'SELECT 1;'"
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60
    network_mode: host

  rabbitmq:
    container_name: rabbitmq
    image: ${IMAGE_RABBITMQ}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    volumes:
      - ${DATA_PATH}/.data/rabbitmq:/var/lib/rabbitmq
      - /etc/localtime:/etc/localtime:ro
    expose:
      - "5671"
      - "5672"
      - "15672"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USERNAME}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    network_mode: host
networks:
  apitable:
    name: apitable_default
    driver: bridge