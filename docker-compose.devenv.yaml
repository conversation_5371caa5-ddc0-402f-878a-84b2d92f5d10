version: '3'

services:

  backend-server:
    image: apitable/devenv:latest
    restart: always
    ports:
      - "8081:8081"
    expose:
      - "8081"
    working_dir: /devenv
    env_file:
      - "${ENV_FILE:-.env}"
    volumes:
      - ./backend-server:/devenv
    environment:
      - HOME=/tmp
    networks:
      - apitable
    command: sh -c "./gradlew build -x test && java -jar application/build/libs/application.jar"

  web-server:
    image: apitable/devenv:latest
    restart: always
    ports:
      - "3000:3000"
    expose:
      - "3000"
    working_dir: /devenv
    env_file:
      - "${ENV_FILE:-.env}"
    volumes:
      - ./:/devenv
    environment:
      - HOME=/tmp
    networks:
      - apitable
    command: sh -c "pnpm install && pnpm run build:dst:pre && pnpm run sd"

  room-server:
    image: apitable/devenv:latest
    restart: always
    ports:
     - "3333:3333"
     - "3334:3334"
     - "3001:3001"
     - "3002:3002"
     - "3006:3006"
     - "3005:3005"
     - "3007:3007"
    expose:
      - "3333"
      - "3334"
      - "3001"
      - "3002"
      - "3006"
      - "3005"
      - "3007"
    working_dir: /devenv
    env_file:
      - "${ENV_FILE:-.env}"
    volumes:
      - ./:/devenv
    environment:
      - HOME=/tmp
      - ENABLE_SOCKET=true
    networks:
      - apitable
    command: sh -c "pnpm install && pnpm run build:pre && pnpm run start:room-server"

  protoc:
    image: apitable/protoc
    working_dir: /devenv
    environment:
      - PROTO_PATH=scripts/protos
      - PROTO_FILE=scripts/protos/serving/*.proto
      - TS_PROTO_OUT_PATH
      - JAVA_PROTO_OUT_PATH
    volumes:
      - ./:/devenv

networks:
  apitable:
    driver: bridge
