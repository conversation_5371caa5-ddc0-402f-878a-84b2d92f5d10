version: "3.9"

services:
  web-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_WEB_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "8080"
    env_file:
      - "${ENV_FILE:-.env}"
    networks:
      - apitable

  imageproxy-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_IMAGEPROXY_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "8080"
    environment:
      - TZ=${TIMEZONE}
      - BASEURL=${AWS_ENDPOINT}
    networks:
      - apitable

  backend-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_BACKEND_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    env_file:
      - "${ENV_FILE:-.env}"
    expose:
      - "8081"
    environment:
      - TZ=${TIMEZONE}
      - DEFAULT_TIME_ZONE=${TIMEZONE}
    networks:
      - apitable
    depends_on:
      init-db:
        condition: service_completed_successfully
    healthcheck:
      test: ["CMD-SHELL", "curl -sS 'http://localhost:8081' || exit 1"]
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60

  room-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_ROOM_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "3333"
      - "3334"
      - "3001"
      - "3002"
      - "3006"
      - "3005"
      - "3007"
    env_file:
      - "${ENV_FILE:-.env}"
    environment:
      - TZ=${TIMEZONE}
      - NODE_ENV=${ENV}
      - NODE_OPTIONS=--max-old-space-size=2048 --max-http-header-size=80000
      - API_MAX_MODIFY_RECORD_COUNTS=${API_MAX_MODIFY_RECORD_COUNTS:-30}
      - INSTANCE_MAX_MEMORY=4096M
      - ENABLE_SOCKET=true
    networks:
      - apitable
    depends_on:
      mysql:
        condition: service_healthy

  databus-server:
    image: ${IMAGE_REGISTRY}/${IMAGE_DATABUS_SERVER}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    expose:
      - "8625"
    env_file:
      - "${ENV_FILE:-.env}"
    environment:
      - TZ=${TIMEZONE}
    networks:
      - apitable
    depends_on:
      mysql:
        condition: service_healthy

  # Third Party Dockers
  gateway:
    image: ${IMAGE_REGISTRY}/${IMAGE_GATEWAY}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    ports:
      - ${NGINX_HTTP_PORT:-80}:80
      - ${NGINX_HTTPS_PORT:-443}:443
    environment:
      - TZ=${TIMEZONE}
    networks:
      - apitable
    depends_on:
      web-server:
        condition: service_started
      imageproxy-server:
        condition: service_started
      backend-server:
        condition: service_healthy
      room-server:
        condition: service_started
      init-appdata:
        condition: service_completed_successfully

  minio:
    container_name: minio
    image: ${IMAGE_REGISTRY}/${IMAGE_MINIO}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    command: server --console-address ":9001" /data
    expose:
      - "9000"
      - "9001"
    volumes:
      - ${DATA_PATH}/.data/minio/data:/data
      - ${DATA_PATH}/.data/minio/config:/root/.minio
    environment:
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
      - TZ=${TIMEZONE}
    networks:
      - apitable
    healthcheck:
      test: ["CMD-SHELL", "curl -sS 'http://localhost:9000' || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 30

  redis:
    container_name: apitable-redis
    image: ${IMAGE_REDIS}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    command:
      [
          "redis-server",
          "--appendonly",
          "yes",
          "--requirepass",
          "${REDIS_PASSWORD}",
      ]
    expose:
      - "6379"
    volumes:
      - ${DATA_PATH}/.data/redis:/data
    environment:
      - TZ=${TIMEZONE}
    networks:
      - apitable

  mysql:
    container_name: apitable-mysql
    image: ${IMAGE_MYSQL}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    volumes:
      - ${DATA_PATH}/.data/mysql:/var/lib/mysql
    expose:
      - "3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - TZ=${TIMEZONE}
    command: --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --sql_mode=IGNORE_SPACE,NO_ENGINE_SUBSTITUTION
      --lower_case_table_names=2
    healthcheck:
      test: "mysql ${MYSQL_DATABASE} -u${MYSQL_USERNAME} -p${MYSQL_PASSWORD} -e 'SELECT 1;'"
      interval: 5s
      timeout: 5s
      start_period: 30s
      retries: 60
    networks:
      - apitable

  rabbitmq:
    container_name: rabbitmq
    image: ${IMAGE_RABBITMQ}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    restart: always
    volumes:
      - ${DATA_PATH}/.data/rabbitmq:/var/lib/rabbitmq
    expose:
      - "5671"
      - "5672"
      - "15672"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USERNAME}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    networks:
      - apitable

  # init data
  init-db:
    image: ${IMAGE_REGISTRY}/${IMAGE_INIT_DB}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    environment:
      - TZ=${TIMEZONE}
      - DB_HOST=${MYSQL_HOST}
      - DB_PORT=${MYSQL_PORT}
      - DB_NAME=${MYSQL_DATABASE}
      - DB_USERNAME=${MYSQL_USERNAME}
      - DB_PASSWORD=${MYSQL_PASSWORD}
      - DATABASE_TABLE_PREFIX=${DATABASE_TABLE_PREFIX}
      - ACTION=update
    networks:
      - apitable
    depends_on:
      mysql:
        condition: service_healthy

  # init-appdata
  init-appdata:
    image: ${IMAGE_REGISTRY}/${IMAGE_INIT_APPDATA}
    pull_policy: ${IMAGE_PULL_POLICY:-if_not_present}
    env_file:
      - "${ENV_FILE:-.env}"
    networks:
      - apitable
    depends_on:
      mysql:
        condition: service_healthy
      init-db:
        condition: service_completed_successfully

networks:
  apitable:
    name: apitable_default
    driver: bridge
