{"name": "root", "version": "1.10.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "sc": "npm run start:core", "sd": "npm run start:datasheet", "sd:quiet": "cd packages/datasheet && pnpm run dev:quiet", "sd:private": "REACT_APP_DEPLOYMENT_MODELS=PRIVATE npm run  start:datasheet", "sr": "cross-env NODE_ENV=development npm run start:room-server", "sss": "npm run start:components", "start:core": "nx start @apitable/core", "start:i18n": "nx start @apitable/i18n-lang", "start:datasheet": "cd packages/datasheet && pnpm run dev", "lint:datasheet": "nx check:lint @apitable/datasheet", "storybook:components": "nx storybook @apitable/components", "start:widget-sdk": "nx start @apitable/widget-sdk", "start:room-server": "nx start:dev @apitable/room-server ", "start:socket-server": "nx start:socket:dev @apitable/room-server", "start:components": "nx start @apitable/components", "build": "nx run-many -t build", "build:web": "nx run @apitable/datasheet:build", "build:room-server": "nx run @apitable/room-server:build", "build:datasheet": "nx run @apitable/datasheet:build", "build:api-client": "nx run @apitable/api-client:build", "build:dst:pre": "nx run-many -t build -p @apitable/i18n-lang @apitable/core @apitable/icons @apitable/components @apitable/ai @apitable/widget-sdk --parallel=5 && pnpm build:api-client", "build:dst": "nx run --verbose @apitable/datasheet:build", "build:air-agent:pre": "nx run-many -t build -p @apitable/i18n-lang @apitable/icons @apitable/core @apitable/components @apitable/ai --parallel=5", "build:air-agent": "nx run --verbose @apitable/air-agent:build", "build:sr": "nx run-many -t build -p @apitable/i18n-lang @apitable/room-server --parallel=2 && nx run-many -t build-cjs -p @apitable/core", "build:components:doc": "nx build-storybook @apitable/components", "build:dst:private": "REACT_APP_DEPLOYMENT_MODELS=PRIVATE PUBLIC_URL=/build nx run @apitable/datasheet:build && rm packages/datasheet/build/static/**/*.map", "build:i18n": "pnpm --filter @apitable/i18n-lang run build", "start:prod:room-server": "pnpm --filter @apitable/room-server run start:prod", "pm2:nest": "pm2-runtime start packages/room-server/ecosystem.config.js", "test": "nx run-many -t test --exclude @apitable/cypress", "test:core": "nx test @apitable/core", "test:core:cov": "nx test:cov @apitable/core", "test:widget-sdk": "nx test @apitable/widget-sdk", "test:datasheet": "nx test @apitable/datasheet", "test:nest": "nx test @apitable/room-server", "test:ut:room": "nx test:ut @apitable/room-server", "test:ut:room:cov": "nx test:ut:cov @apitable/room-server", "clear:cache": "rm -rf ./package/datasheet/node_modules/.cache/hard-source", "link:widget": "pnpm link @apitable/core && pnpm link @apitable/widget-sdk && pnpm link @apitable/components && pnpm link @apitable/icons", "postinstall": "cd .. && husky install apitable/.husky", "stylelint:datasheet": "nx stylelint @apitable/datasheet", "lint": "cross-env NODE_OPTIONS=--max-old-space-size=8192 eslint --config .eslintrc --ext packages/*/src/**/*.{js,ts,tsx} --format @microsoft/eslint-formatter-sarif --output-file eslint-results.sarif", "cy:open": "nx cy:open @apitable/cypress", "cy:run": "nx cy:run @apitable/cypress", "lint:fix": "eslint --config .eslintrc --fix --ext packages/*/src/**/*.{js,ts,tsx} --format @microsoft/eslint-formatter-sarif", "lint:check": "eslint --config .eslintrc --quiet --ext packages/*/src/**/*.{js,ts,tsx}", "prettier:check": "prettier --check packages/*/src/**/*.{js,ts,tsx}", "prettier:fix": "prettier --write packages/*/src/**/*.{js,ts,tsx} && eslint --config .eslintrc --fix --ext packages/*/src/**/*.{js,ts,tsx}", "deps": "npx npm-check-updates -u && npx check-dependency-version-consistency ."}, "lint-staged": {"packages/*/(src|pages)/**/!(*system_config.interface|strings.interface).{ts,tsx}": "eslint --fix --no-cache"}, "eslintConfig": {"extends": "react-app", "rules": {"no-unused-vars": "off", "no-trailing-spaces": "off", "space-infix-ops": "off", "comma-spacing": "off", "block-spacing": "off", "no-sparse-arrays": "off", "no-sync": "off", "no-undef": "off", "no-unused-expressions": "off", "no-var": "off", "no-else-return": "off", "no-multi-spaces": "off", "no-loss-of-precision": "off"}}, "engines": {"node": "16.15.0", "pnpm": "8.6.12"}, "devDependencies": {"@babel/core": "7.22.20", "@babel/plugin-syntax-import-meta": "^7.10.4", "@commitlint/cli": "17.7.1", "@commitlint/config-conventional": "17.7.0", "@microsoft/eslint-formatter-sarif": "^3.0.0", "@swc/core": "^1.3.86", "@swc/jest": "^0.2.29", "@types/dot-object": "^2.1.2", "@types/jest": "^29.5.11", "@types/lru-cache": "^7.10.9", "@types/node": "^20.6.2", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "apitable": "latest", "babel-eslint": "10.1.0", "babel-loader": "8.1.0", "chalk": "^5.3.0", "concurrently": "^8.2.1", "dot-object": "^2.1.4", "dotenv": "16.3.1", "dotenv-expand": "10.0.0", "eslint": "^8.49.0", "eslint-config-prettier": "^8", "eslint-config-react-app": "7.0.1", "eslint-plugin-flowtype": "8.0.3", "eslint-plugin-import": "2.28.1", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-vika": "0.0.1", "git-format-staged": "^3.0.0", "husky": "8.0.3", "lint-staged": "14.0.1", "lodash": "^4.17.21", "null-loader": "^4.0.1", "nx": "16.10.0", "prettier": "^3.0.3", "pretty-quick": "^3.1.3", "quicktype": "^23.0.75", "svgo": "^3.0.2", "ts-node": "^10.9.1", "ts-pnp": "1.2.0", "typescript": "5.6.2", "wasm-loader": "^1.3.0"}, "resolutions": {"node-gyp": "9.3.1", "@nestjs/microservices@^8.1.1": "8.1.2"}, "bin": "packages/room-server/dist/main.js", "pkg": {"scripts": ["packages/room-server/dist/**/*.js"], "assets": ["node_modules/bull/lib/commands/**/*", "node_modules/@nestjs", "node_modules/lodash", "packages/room-server/**/*.proto", "packages/room-server/**/*.lua", "packages/room-server/**/*.yaml"]}, "packageManager": "pnpm@8.6.12", "pnpm": {"packageExtensions": {"posix": {"optionalDependencies": {"node-gyp": "*"}}}}, "dependencies": {"bufferutil": "^4.0.8", "bullmq": "^5.4.4", "cross-env": "^7.0.0", "glob-parent": "5.1.2", "react": "18.2.0", "utf-8-validate": "^6.0.5"}}