import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  compiler: {
    styledComponents: true,
  },
  // 添加端口配置
  serverOptions: {
    port: 3111
  },// 将端口号修改为你想要的值，例如 3001
  // distDir: 'web_build',
  // output: 'standalone',
  experimental: {},
  // 添加其他常用配置
  swcMinify: true, // 使用 SWC 进行代码压缩
  poweredByHeader: false, // 移除 X-Powered-By header
  images: {
    domains: [], // 配置图片域名白名单
  },
  env: {
    NEXT_PUBLIC_NEXT_API: process.env.NEXT_PUBLIC_NEXT_API || 'http://www.fintechquan.cn'
  }
};

export default nextConfig;
