{"name": "dolphin-main-app-wujie", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3112", "start": "next start -p 3111", "build": "next build", "lint": "next lint", "export": "next export"}, "dependencies": {"@apitable/components": "workspace:*", "@apitable/core": "workspace:*", "@apitable/i18n-lang": "workspace:*", "@apitable/widget-sdk": "workspace:*", "@apitable/icons": "workspace:*", "@apitable/react-flow": "^9.6.12", "react": "18.2.0", "react-dom": "18.2.0", "next": "12.3.1", "redux": "4.2.0", "ahooks": "^3.5.0", "cookies-next": "^2.1.1", "attr-accept": "^2.1.0", "antd": "4.23.5", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.0.4", "@types/mime-types": "^2.1.1", "@types/socket.io-client": "^1.4.36", "store2": "^2.12.0", "@redux-devtools/extension": "^3.2.3", "@types/color": "^3.0.1", "current-device": "^0.10.2", "rc-virtual-list": "^3.4.11", "@types/classnames": "2.2.10", "@types/qs": "^6.9.4", "react-device-detect": "latest", "color": "^3.1.3", "dnd-core": "^16.0.1", "comlink": "^4.3.1", "@types/lodash": "^4.14.197", "immer": "9.0.16", "classnames": "2.2.6", "axios": "^1.3.4", "html-react-parser": "^0.10.2", "redux-batched-actions": "0.5.0", "redux-thunk": "2.4.1", "ts-toolbelt": "^9.6.0", "mime-types": "^2.1.29", "qs": "latest", "@types/react-virtualized-auto-sizer": "1.0.0", "dayjs": "1.11.10", "bowser": "^2.11.0", "react-virtualized-auto-sizer": "1.0.2", "@types/path-browserify": "^1.0.0", "lodash": "^4.17.21", "rc-notification": "~4.6.0", "react-dnd": "^16.0.1", "@types/semver": "^7", "semver": "^7.5.2", "qrcode": "^1.4.4", "path-browserify": "^1.0.1", "@types/qrcode": "^1.3.5", "rc-trigger": "^5.3.3", "react-dnd-html5-backend": "^11.1.3", "lottie-web": "^5.6.10", "react-dnd-touch-backend": "^14.0.2", "antd-mobile": "5.32.4", "rc-queue-anim": "^1.8.5", "socket.io-client": "2.2.0", "styled-components": "latest", "next-redux-wrapper": "^8.0.5", "dom-to-image": "^2.6.0", "urlcat": "^2.0.4", "@types/dom-to-image": "^2.6.2", "@types/react-window": "^1.8.1", "react-window": "^1.8.8", "@types/exceljs": "^1.3.0", "exceljs": "4.1.1", "wujie": "latest", "js-cookie": "latest", "mammoth": "latest", "wujie-react": "latest"}, "devDependencies": {"typescript": "4.8.4", "@types/node": "^14.14.34", "@types/react": "18.0.2", "@types/react-dom": "18.0.2", "postcss": "^8.4.27", "less": "4.1.3", "less-loader": "11.0.0", "next-with-less": "^2.0.5", "autoprefixer": "^10.4.14", "tailwindcss": "^3.3.3"}}