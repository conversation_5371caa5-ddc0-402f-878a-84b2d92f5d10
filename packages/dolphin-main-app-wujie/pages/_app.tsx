import { t } from '@apitable/core';
import type { AppProps } from 'next/app';
import dynamic from 'next/dynamic';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { wrapper } from 'src/store/store';
import { useDispatch } from 'react-redux';
import { getDocumentDataPack, verifyUserAccess } from '@/api/document_api'; // Assuming this is your API method
import { setDocumentData } from '@/store/slice/documentSlice'; // You'll need to create this slice
import { string2Query } from '@/utils/query';
import { IUserInfoVo, setUserInfo } from '@/store/slice/userSlice';
import { setNodeInfo, setNodeShare } from '@/store/slice/nodeSlice';
import { NotificationStore } from '@/notification_store';

// import { initResourceService } from '@/resource_service';
// import { initWorkerStore } from '@/worker';
// import { initEventListen } from '@/events/init_events_listener';
// import * as immer from 'immer';
// import { enableMapSet } from 'immer';
// const initWorker = async () => {
//   console.log('init worker');
//   const comlinkStore = await initWorkerStore();
//   // Initialization functions
//   // initializer(comlinkStore);
//   console.log('init worker 2');

//   const resourceService = initResourceService(comlinkStore.store!);
//   console.log('init worker 3');

//   initEventListen(resourceService);
//   //  if (getBrowserDatabusApiEnabled()) {
//   //    await WasmApi.initializeDatabusWasm();
//   // } else {
//   //   console.log('web assembly is not supported');
//   // }
// };


// const ThemeWrapper = dynamic(() => import('./theme_wrapper'), { ssr: false });

enum LoadingStatus {
  None,
  Start,
  Complete,
}

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(LoadingStatus.None);
  const dispatch = useDispatch();

  // useEffect(() => {
  //   console.log('Current directory:', process.cwd());
  //   console.log('__dirname:', __dirname);
  //   console.log('Pages directory exists:', require('fs').existsSync('./pages'));
  // }, []);
  useEffect(() => {
    const handleStart = () => {
      if (loading !== LoadingStatus.None) return;
      setLoading(LoadingStatus.Start);
    };

    const handleComplete = () => {
      if (loading !== LoadingStatus.Start) return;
      setLoading(LoadingStatus.Complete);
    };

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
    };
  }, [loading, router]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const originalAddEventListener = window.addEventListener;
      window.addEventListener = function (type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) {
        if (type === 'message') {
          document.addEventListener(type, listener, options);
        } else {
          originalAddEventListener.call(window, type, listener, options);
        }
      }
    }
  }, []);
  // useEffect(() => {
  //   // immer.setAutoFreeze(false);
  //   (() => {
  //     if (!process.env.SSR) {
  //       console.log('start init web');
  //       // import('../public/file/js/sensors');
  //       initWorker();
  //     }
  //   })();
  // }, []);
  useEffect(() => {
    const fetchDocumentData = async () => {
      const query = string2Query();
      console.log(query);
      // dispatch(setNodeInfo({
      //   nodeId:1123
      // }));
  
      try {
        const nodeId = query.nodeId as string;
  
        if (!nodeId) {
          console.error('nodeId not found in URL');
          return;
        }
          
  
        // 先进行用户访问权限验证
        const accessResponse = await verifyUserAccess(nodeId);
        console.log(accessResponse,'accessResponse')
        // 检查是否有权限
        // if (accessResponse.data?.success) {
        //   const userInfo = accessResponse.data.data.userInfoVo  as IUserInfoVo // Add other missing properties here with appropriate default values
     
        //   const nodeInfo = accessResponse.data.data.nodeInfoVo;
        //   const nodeShare = accessResponse.data.data.nodeShareVo; // 可选的 nodeShare
  
        //   // 在 store 中存储 userInfo
        //   dispatch(setUserInfo(userInfo));
  
        //   // 在 store 中存储 nodeInfo
        //   dispatch(setNodeInfo(nodeInfo));
  
        //   // 如果存在 nodeShare，则存储
        //   if (nodeShare) {
        //     dispatch(setNodeShare(nodeShare));
        //   }

        //   // NotificationStore.init(userInfo.uuid, userInfo.spaceId);
          
        //   // 可以根据 nodeInfo 中的权限进行进一步处理
        //   if (nodeInfo.permissions.readable) {
        //     const response = await getDocumentDataPack(nodeId);
  
        //     if (response.data?.success && response.data.data) {
        //       // dispatch(setDocumentData(response.data.data));
        //     }
        //   } else {
        //     console.error('No read permission for this node');
        //   }
        // } else {
        //   console.error('Access verification failed');
        // }
      } catch (error) {
        console.error('Failed to fetch document data or verify access:', error);
      }
    };  
    console.log('fetchDocumentData');
    fetchDocumentData();
  }, [dispatch]);
  
  
  return (
    <>
      <Head>
        <title>海豚表格</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <div >
        {/* <div style={{ opacity: loading !== LoadingStatus.Complete ? 0 : 1 }}> */}
        {/* <ThemeWrapper> */}
          <Component {...pageProps} />
        {/* </ThemeWrapper> */}
      </div>
    </>
  );
}

export default wrapper.withRedux(MyApp);