import Document, { Document<PERSON>ontex<PERSON>, Head, Html, Main, NextScript } from 'next/document';
import Script from 'next/script';
import React from 'react';
import { getInitialProps } from 'src/utils/get_initial_props';

interface IClientInfo {
  env: string;
  version: string;
  envVars: string;
  locale: string;
}

declare const window: any;

class MyDocument extends Document<IClientInfo> {
  static async getInitialProps(ctx: DocumentContext) {
    const initialProps = await Document.getInitialProps(ctx);
    const initData = getInitialProps({ ctx });
    return {
      ...initialProps,
      ...initData,
    };
  }

  render() {
    const { env, version, envVars, locale } = this.props;

    return (
      <Html>
        <Head>
          <link rel='shortcut icon' href='/favicon.ico' />
          <link rel='manifest' href='/manifest.json' />
        </Head>
        <body>
          <Main />
          <NextScript />
          <Script id="__initialization_data__" strategy={'beforeInteractive'}>
            {`
            window.__initialization_data__ = {
                env: '${env}',
                version: '${version}',
                envVars: ${envVars},
                locale:'${locale}',
                userInfo: null,
            };
            `}
          </Script>
          <Script id="widget-sdk-init" strategy="afterInteractive">
            {`
              if (typeof window !== 'undefined') {
                window.addEventListener = function(type, listener, options) {
                  if (type === 'message') {
                    // 处理message事件监听
                    document.addEventListener(type, listener, options);
                  } else {
                    EventTarget.prototype.addEventListener.call(window, type, listener, options);
                  }
                }
              }
            `}
          </Script>
        </body>
      </Html>
    );
  }
}

export default MyDocument;
