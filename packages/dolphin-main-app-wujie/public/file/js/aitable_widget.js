'use strict';
/**
 * APITable <https://github.com/aitable/aitable>
 * Copyright (C) 2023 APITable Ltd. <https://aitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
/**
 * AITABLE WIDGET CODE DO NOT MODIFY THIS FILE
 * VERSION 0.0.1
 */
/* eslint-disable */
// @ts-nocheck

(()=>{"use strict";const t="aitable-widget",n="aitable-widget-container",e="aitable-widget-avatar",i="aitable-widget-chat-box-container",a="aitable-widget-chat-box";let o=560,s=!1;const d=()=>{const t=window.innerHeight;if(o=t<656?t-48-48:560,s){const t=document.getElementById(n),e=document.getElementById(i);t&&e&&(e.style.height=o+"px",t.style.height=o+80-48+"px")}},r=()=>{const t=document.getElementById(n),d=document.getElementById(a),r=document.getElementById(i),g=document.getElementById(e);if(d&&t&&r&&g){if(s)g.style.bottom="0",g.style.opacity="1",d.style.opacity="0",r.style.opacity="0",r.style.transform="translate(50%, 50%) scale(0)",t.style.transitionDelay="0.3s",t.style.width="80px",t.style.height="80px";else{const n=window.__aitable.baseUrl+"/embed/ai/"+window.__aitable.share+"?aitable_widget=1";""===d.src?(d.src=n,d.onload=()=>{d.style.opacity="1"}):d.style.opacity="1",g.style.bottom="-90px",g.style.opacity="0",r.style.height=o+"px",r.style.width="400px",r.style.opacity="1",r.style.transform="translate(0, 0) scale(1)",t.style.transitionDelay="0s",t.style.width="432px",t.style.height=o+80-48+"px"}s=!s}},g=async()=>{if(!window||!document)return;if(document.getElementById(t))return;const o=document.createElement("div");if(o.id=t,o.style.display="none",document.body.appendChild(o),await(async()=>{const t=window.__aitable.baseUrl+"/api/v1/node/readShareInfo/"+window.__aitable.share,n=await((t,n,e)=>"function"==typeof fetch?fetch(t,{method:"GET",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}}).then((t=>t.json())):"undefined"!=typeof XMLHttpRequest?new Promise((function(n,i){const a=new XMLHttpRequest;a.open("GET",t,!0),a.setRequestHeader("Content-Type","application/json"),a.onreadystatechange=function(){4===a.readyState&&(200===a.status?n(JSON.parse(a.responseText)):i(a))},a.send(JSON.stringify(e))})):Promise.reject(new Error("No support for performing HTTP requests.")))(t);return!n||200===n.code||(console.warn("AITable ChatBot Widget: "+n.message),!1)})()){const t=`\n      <div id=${n}>\n        <iframe allowtransparency="true" scrolling="no" id=${e}></iframe>\n        <div id=${i}>\n          <iframe allowtransparency="true" scrolling="no" id=${a}></iframe>\n        </div>\n      </div>\n    `;o.innerHTML=t,(()=>{const t=document.getElementById(e),n=t.contentWindow,i=t.contentDocument||n.document,a=document.createElement("style");a.textContent="body { margin: 16; padding: 0; }",i.head.appendChild(a);const o=document.createElement("div");o.style.cssText="\n    width: 48px;\n    height: 48px;\n    cursor: pointer;\n    border-radius: 50%;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    transition: all 0.3s ease;\n    position: relative;\n    left: 0;\n    top: 0;\n  ",i.body.appendChild(o),o.addEventListener("click",r),o.innerHTML='<img height="48" width="48" src="data:image/png;base64,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" />'})(),(()=>{const t=document.createElement("style"),o="light"===(()=>{try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(t){return"light"}})()?"#fefefe":"#1A1A1A";t.textContent=`\n    #${n} {\n      z-index: 2147483639;\n      position: fixed;\n      bottom: 0px;\n      padding: 16px;\n      width: 80px;\n      height: 80px;\n      max-width: 100%;\n      max-height: calc(100% - 0px);\n      min-height: 0px;\n      min-width: 0px;\n      background-color: transparent;\n      border: 0;\n      right: 0;\n      transition: all 0s ease-in-out 0s !important;\n    }\n    #${e} {\n      width: 80px;\n      height: 80px;\n      position: absolute;\n      right: 0;\n      bottom: 0;\n      min-height: 0px;\n      min-width: 0px;\n      margin: 0px;\n      padding: 0px;\n      background-image: none;\n      background-position: 0% 0%;\n      background-size: initial;\n      background-attachment: scroll;\n      background-origin: initial;\n      background-clip: initial;\n      background-color: rgba(0, 0, 0, 0);\n      border-width: 0px;\n      float: none;\n      color-scheme: normal;\n      display: block;\n      transition: all 0.3s ease-in-out 0s !important;\n    }\n    #${i} {\n      margin: 16px;\n      margin-bottom: 0;\n      box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n      transform: translate(50%, 50%) scale(0);\n      position: absolute;\n      background: ${o};\n      opacity: 0;\n      right: 0;\n      top: 0;\n      border-radius: 8px;\n      transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out !important;\n    }\n    #${a} {\n      border-radius: 8px;\n      width: 100% !important;\n      height: 100%;\n      min-height: 0;\n      min-width: 0;\n      padding: 0;\n      opacity: 0;\n      background-image: none;\n      background-position: 0% 0%;\n      background-size: initial;\n      background-attachment: scroll;\n      background-origin: initial;\n      background-clip: initial;\n      background-color: rgba(0, 0, 0, 0);\n      border-width: 0px;\n      float: none;\n      color-scheme: normal;\n      inset: 0px;\n    }\n  `,document.head.appendChild(t)})(),o.style.display="block"}};g(),window.addEventListener("load",g),window.addEventListener("resize",d),window.addEventListener("message",(t=>{"aitable-widget"===t.data.name&&"close"===t.data.action&&r()})),d()})();