{"ANNUAL": "ANNUAL", "ArchiveRecords": "archived ${record_count} record(s)", "BIANNUAL": "BIANNUAL", "CNY": "CNY", "DAYS": "Day", "DeleteArchivedRecords": "delete ${record_count} archived record(s)", "EXPIRATION_NO_BILLING_PERIOD": "NO BILLING PERIOD", "MONTHLY": "MONTHLY", "NO_BILLING_PERIOD": "NO BILLING PERIOD", "No_open_functionality": "Not available yet", "Public_Beta_Period": "Public Beta Period", "QR_code_invalid": "QR code is invalid", "Standalone_Capacity": "Independent add-on capacity", "UnarchiveRecords": "unarchived ${record_count} record(s)", "WEEKS": "Week", "access_to_space_station_editors": "Enter the space to edit", "account_ass_manage": "Account binding", "account_format_err": "Account format error", "account_manager_binding_feishu": "The current Space is bound to the corresponding company of Lark, the \"Account relation management\" is invalid on the current Space.", "account_manager_invalid_subtip": "Account binding can't be managed here for the \"${spaceName}\" Space", "account_manager_invalid_tip": "This Space has been bound to Lark", "account_nickname": "Account name: ", "account_password_incorrect": "Wrong account or password", "account_wallet": "Wallet", "action": "Action", "action_execute_error": "Step execute error\nError message:\n${value}\n\nFor details, please refer to https://help.aitable.ai/docs/manual-automation-robot#how-to-troubleshoot", "action_should_not_empty": "Operation can not be empty", "activate_space": "Activate Space", "active_record_hidden": "The record has been hidden", "active_space": "Activate Space", "activity": "Activity", "activity_banner_desc": "", "activity_integral_income_notify": "Congratulations on getting the reward of  \"<a class=\"activityName\"></a>\": <a class=\"count\"></a>  V coins. You can use them to buy a plan or sevices. For more information, please go to \"Personal Settings > Wallet \".", "activity_integral_income_toadmin": "Congratulations on your space getting the reward of  \"<a class=\"activityName\"></a>\": <a class=\"count\"></a>  V coins. You can use them to buy a plan or sevices. For more information, please go to \"Personal Settings > Wallet \".", "activity_login_desc": "Your friend \"${nickname}\" has invited you to sign up on APITable", "activity_marker": "Click to view the record's comments", "activity_no_rank_number": "", "activity_publish": "", "activity_rank": "", "activity_rank_number": "", "activity_register_tip1": "By signing up you agree to ", "activity_register_tip2": "<Terms of Service>", "activity_reward": "", "activity_rule": "", "activity_rule_content": "", "activity_share_btn": "", "activity_statement": "", "activity_time": "", "activity_tip": "When you're mentioned in a record, you will be notified", "ad_card_desc": "For a demo, quote, or business communication, please contact us by clicking the button below", "adaptive": "Adaptive", "add": "Add", "add_a_trigger": "Add a trigger", "add_an_option": "Add an option", "add_attachment": "Add attachment", "add_automation_node": "Add a new automation node", "add_cover": "Add cover", "add_dashboard": "New dashboard", "add_datasheet_editor": "In addition to \"Update-only\", can also add or delete views and delete records", "add_datasheet_manager": "Can perform all actions on the file node", "add_datasheet_reader": "Can read data or comment on the datasheet", "add_datasheet_updater": "Can read, add and edit records except deleting records", "add_editor": "Editor", "add_favorite_success": "<PERSON>nned to top successfully", "add_filter": "Add filter", "add_filter_condition_tips": "Pick a field to Filter", "add_filter_empty": "Pick another field to <PERSON>lter", "add_folder_editor": "In addition to \"Update-only\", can also edit and share file nodes", "add_folder_manager": "Can perform all actions to the file node", "add_folder_reader": "Can read file nodes in the folder", "add_folder_updater": "Can read, add and edit records except deleting records", "add_form": "New form", "add_form_logo": "Add logo", "add_gallery_view_success_guide": "For the datasheet with pictures, the gallery view will display it very intuitively. Many interesting functions are on the way. Stay tuned~", "add_gantt_group_card": "New record in the group", "add_grouping": "Pick another field to Grouping", "add_grouping_empty": "Pick a field to Grouping", "add_image": "Add image", "add_kanban_group_card": "New record", "add_link_record_button": "Link to record(s) from ${datasheetname}", "add_link_record_button_disable": "Link Record Empty", "add_manager": "Manager", "add_mark_line": "Add mark line", "add_member": "Add member", "add_member_fail": "Failed to add members", "add_member_or_group": "Add member / team", "add_member_or_unit": "Add member or team", "add_member_success": "Member added successfully", "add_new_record_by_name": "New record ${span}", "add_new_view_success": "View named \"${viewName}\" added", "add_on_api_call": "Increase API capacity", "add_or_cancel_favorite_fail": "Failed to pin/unpin", "add_reader": "Read-only", "add_record": "New record", "add_record_out_of_limit_by_api_notify": "You failed to add new record(s) via API in the <a class=\"nodeName\"></a> datasheet.The maximum number of records per datasheet is <a class=\"count\"></a>. To continue to add records via API, please clean up your outdated data or write data into another datasheet.", "add_record_soon_to_be_limit_by_api_notify": "Your datasheet <a class=\"nodeName\"></a> has <a class=\"usage\"></a> records and will reach the maximum limit (<a class=\"count\"></a> records) soon. To continue to add records via API, please clean up your outdated data or write data into another datasheet before you reach the limit.", "add_role_btn": "New role", "add_role_error_empty": "Role name cannot be empty", "add_role_error_exists": "The role name already exists, please modify", "add_role_error_limit": "The name of the role cannot exceed ${max} characters", "add_role_success": "Permission added successfully", "add_role_success_message": "Role created successfully", "add_role_title": "Add role", "add_row_button_tip": "Insert a record", "add_row_created_by_tip": "You have been automatically watched the updates for this record", "add_sort": "Pick another field to Sort", "add_sort_current_user": "Me", "add_sort_current_user_tips": "Current visitor", "add_sort_empty": "Pick a field to Sort", "add_space": "Create Space", "add_sub_admin": "Add sub-admin", "add_sub_admin_contacts_configuration": "Managing teams means that the sub-admins can add, delete, or modify teams; Managing members means that the sub-admins can add, delete, or modify members；Managing roles means that the sub-admins can add, delete, or modify roles.", "add_sub_admin_fail": "Failed to add sub-admin", "add_sub_admin_permissions_configuration": "Sub-admin can edit members' permissions on the \"Security page\". For example, you can disable creating public link", "add_sub_admin_success": "Sub-admin added successfully", "add_sub_admin_template_configuration": "The sub-admin can delete the Space templates", "add_sub_admin_title_member_team": "Members and teams ", "add_sub_admin_title_workbench": "Workbench", "add_sub_admin_workbench_configuration": "The sub-admins have the highest authority of all file nodes in \"Workbench\", and can add, delete, modify and set permissions on file nodes", "add_summry_describle": "Add summary describe", "add_target_values": "Add target value", "add_team": "Add team", "add_updater": "Update-only", "add_widget": "New widget", "add_widget_panel": "New widget board", "add_widget_success": "Widget added", "added_not_yet": "Pending", "additional_styling_options": "Additional color and styling options", "admin": "Admin", "admin_cannot_quit_space": "You are the Space admin so you can't exit. You can transfer or delete this Space on \"Space Management\".", "admin_test_function": "Experimental features of admin", "admin_test_function_content": "The admin can enable or apply for enabling the following experimental features that take effect for all members in the Space", "admin_transfer_space_widget_notify": "The admin <a class=\"memberName\"></a> has transferred the ownership of your custom widget \"<a class=\"widgetName\"></a>\" to <a class=\"involveMemberArr\"></a>", "admin_unpublish_space_widget_notify": "The admin <a class=\"memberName\"></a> has unpublished your custom widget \"<a class=\"widgetName\"></a>\" from the Widget Center", "admins_per_space": "Admins per space", "advanced": "Advanced", "advanced_features": "Advanced", "advanced_mode": "Advanced option", "advanced_permissions": "Advanced Permissions", "advanced_permissions_description": "Advanced permission control, you can...", "afghanistan": "Afghanistan", "aggregate_values": "Aggregate values", "aggregate_values_describle": "Aggregate values by summary", "agree": "Agree", "agreed": "Approved", "ai_advanced_mode_desc": "Advanced mode allows users to customize prompts, providing greater control over the behavior and responses of the AI agent.", "ai_advanced_mode_title": "Advanced mode", "ai_agent_anonymous": "Anonymous${ID}", "ai_agent_conversation_continue_not_supported": "Continuing a previous conversation is not currently supported", "ai_agent_conversation_list": "Conversation list", "ai_agent_conversation_log": "Conversation Log", "ai_agent_conversation_title": "Conversation title", "ai_agent_feedback": "<PERSON><PERSON><PERSON>", "ai_agent_historical_message": "The above is historical message", "ai_agent_history": "History", "ai_agent_message_consumed": "Message consumed", "ai_api_curl_template": "## Create chat completions\n\n```bash\n\ncurl -X POST \"{{apiBase}}/fusion/v1/ai/{{aiId}}/chat/completions\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer {{token}}\" \\\n  -d '{\n    \"model\": \"gpt-3.5-turbo\",\n    \"messages\": [\n      {\n        \"role\": \"system\",\n        \"content\": \"You are a helpful assistant.\"\n      },\n      {\n        \"role\": \"user\",\n        \"content\": \"Hello!\"\n      }\n    ]\n  }'\n\n```\n\n## Returned data example\n\n```json\n{\n  \"id\": \"aitable_ai_CkZH2zQokhry31j_1693452659\",\n  \"conversation_id\": \"CS-0253eb8d-d6c6-4543-88d4-fcb555f52982\",\n  \"actions\": null,\n  \"object\": \"chat.completion\",\n  \"created\": 1693452659,\n  \"model\":\"gpt-3.5-turbo\",\n  \"choices\": [{\n    \"index\": 0,\n    \"message\": {\n      \"role\": \"assistant\",\n      \"content\": \"\\n\\nHello there, how may I assist you today\",\n    },\n    \"finish_reason\": \"stop\"\n  }],\n  \"usage\": {\n    \"prompt_tokens\": 9,\n    \"completion_tokens\": 12,\n    \"total_tokens\": 21,\n    \"total_cost\": 7.900000000000001e-05,\n    \"result\": \"I am an AI, specifically a language model developed by OpenAI.\"\n  }\n}\n```\n", "ai_api_footer_desc": "Embed the AI agent into your website? Learn more", "ai_api_javascript_template": "## Initialize SDK [![github]({{ githubIcon }})](https://github.com/openai/openai-node)\nAITable's chat completion API is compatible with OpenAI's Node.js SDK. You can use our service with the same SDK by simply changing the configuration as follows:\n\n\n```javascript\nconst { Configuration, OpenAIApi } = require(\"openai\");\nconst configuration = new Configuration({\n    apiKey: \"{{token}}\",\n    basePath: \"{{apiBase}}/fusion/v1/ai/{{aiId}}\"\n});\n```\n\n## Create chat completions\n\n```javascript\nconst openai = new OpenAIApi(configuration);\nconst chatCompletion = await openai.createChatCompletion({\n  model: \"gpt-3.5-turbo\",\n  messages: [{role: \"user\", content: \"Hello world\"}],\n});\n```\n\n## Returned data example\n\n```javascript\n\n{\n  \"id\": \"aitable_ai_CkZH2zQokhry31j_1693452659\",\n  \"conversation_id\": \"CS-0253eb8d-d6c6-4543-88d4-fcb555f52982\",\n  \"actions\": null,\n  \"object\": \"chat.completion\",\n  \"created\": 1693452659,\n  \"model\":\"gpt-3.5-turbo\",\n  \"choices\": [{\n    \"index\": 0,\n    \"message\": {\n      \"role\": \"assistant\",\n      \"content\": \"\\n\\nHello there, how may I assist you today\",\n    },\n    \"finish_reason\": \"stop\"\n  }],\n  \"usage\": {\n    \"prompt_tokens\": 9,\n    \"completion_tokens\": 12,\n    \"total_tokens\": 21,\n    \"total_cost\": 7.900000000000001e-05,\n    \"result\": \"I am an AI, specifically a language model developed by OpenAI.\"\n  }\n}\n```\n", "ai_api_python_template": "## Initialize SDK [![github]({{ githubIcon }})](https://github.com/openai/openai-python)\n\nAITable's chat completion API is compatible with OpenAI's Python SDK. You can use our service with the same SDK by simply changing the configuration as follows:\n\n```python\nimport openai\n# Setup parameters\nopenai.api_key = \"{{token}}\"\nopenai.api_base = \"{{apiBase}}/fusion/v1/ai/{{aiId}}\"\n```\n\n## Create chat completions\n\n```python\nchat_completion = openai.ChatCompletion.create(model=\"gpt-3.5-turbo\", messages=[{\"role\": \"user\", \"content\": \"Hello world\"}])\n```\n\n## Returned data example\n\n```python\n\n{\n  \"id\": \"aitable_ai_CkZH2zQokhry31j_1693452659\",\n  \"conversation_id\": \"CS-0253eb8d-d6c6-4543-88d4-fcb555f52982\",\n  \"actions\": null,\n  \"object\": \"chat.completion\",\n  \"created\": 1693452659,\n  \"model\":\"gpt-3.5-turbo\",\n  \"choices\": [{\n    \"index\": 0,\n    \"message\": {\n      \"role\": \"assistant\",\n      \"content\": \"\\n\\nHello there, how may I assist you today\",\n    },\n    \"finish_reason\": \"stop\"\n  }],\n  \"usage\": {\n    \"prompt_tokens\": 9,\n    \"completion_tokens\": 12,\n    \"total_tokens\": 21,\n    \"total_cost\": 7.900000000000001e-05,\n    \"result\": \"I am an AI, specifically a language model developed by OpenAI.\"\n  }\n}\n```\n", "ai_api_tab_title_1": "Create chat completions", "ai_assistant": "Copilot", "ai_bot_model_title": "Agent model", "ai_can_train_count": "Number of trainable times : ${count}", "ai_chat": "AI Chat", "ai_chat_default_prologue": "Welcome aboard! I'm all set and ready to chat with you. Let's have some great conversations!", "ai_chat_default_prompt": "The following is a conversation between a human and an AI. \n\nCurrent conversation:\n{history}\n\nHuman: {input}\nAI:", "ai_chat_textarea_placeholder": "Talk to AI agent…", "ai_chat_unit": "aibot(s)", "ai_close_setting_tip_content": "You have made changes. Do you want to discard them?", "ai_close_setting_tip_title": "Unsaved changes", "ai_copilot_generate_response": "Generating answer for you...", "ai_copilot_processs": "Processing, please wait...", "ai_copilot_start_process_request": "Starting to process your request...", "ai_create_guide_btn_text": "Select datasheet", "ai_create_guide_content": "As an AI agent, I can answer your questions based on the knowledge I have learned. Before starting the conversation, please select a datasheet as a knowledge base, and I will read all the data in it for learning.", "ai_credit_cost_chart_title": "Message credit", "ai_credit_cost_chart_tooltip": "AI queries and training consume message credits. For example, one conversation using the gpt-3.5-turbo model costs 1 message credit. Training a dataset with up to 300,000 characters also costs 1 credit.", "ai_credit_pointer": "Credit(s)", "ai_credit_time_dimension_month": "This month", "ai_credit_time_dimension_today": "Today", "ai_credit_time_dimension_week": "This week", "ai_credit_time_dimension_weekend": "[\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"]", "ai_credit_time_dimension_year": "This year", "ai_credit_usage_tooltip": "Message credits can be used for AI agent queries.The message credits is proportional to the number of seats on your space.", "ai_data_source_rows": "${rows} rows of data", "ai_data_source_update": "Update available", "ai_datasheet_panel_create_btn_text": "Create AI agent", "ai_default_idk": "I don't know", "ai_default_prologue": "Hi there, I'm an AI agent. Nice to meet you! With my training on various datasets, I'm capable of answering your questions.", "ai_default_prompt": "Use the following pieces of context to answer the question at the end. \n\n If you don't know the answer, just say that you don't know, don't try to make up an answer. \n\n{context} \n\nQuestion: {question} \n\nAnswer:", "ai_discar_setting_edit_cancel_text": "Cancel", "ai_discard_setting_edit_ok_text": "Discard", "ai_embed_website": "Embed website", "ai_embed_website_iframe_tips": "To add an AI agent to any location on the website, please add this iframe to your HTML code.", "ai_embed_website_javascript_tips": "Add a chat wdiget to the bottom right corner of the website, please add this script tag to your html.", "ai_empty": "(empty)", "ai_explore_card_title": "Here are some sample questions, give it a try by clicking on any of the messages:", "ai_explore_refresh_btn_text": "Refresh", "ai_fallback_message_desc": "When the AI agent is unable to find the appropriate answer within its datasets,it will respond the content below.", "ai_fallback_message_title": "AI agent's response for unanswerable queries:", "ai_feedback_anonymous": "Anonymous", "ai_feedback_appraisal": "Rating", "ai_feedback_box_alert": "The AI agent managers will assess all messages from the ongoing conversation to improve the quality of the AI agent's responses.Please ensure that sensitive information is not included in the conversation content.", "ai_feedback_box_placeholder": "Please enter your feedback", "ai_feedback_box_send": "Send", "ai_feedback_box_title": "<PERSON><PERSON><PERSON>", "ai_feedback_comment": "Feedback message", "ai_feedback_insight_pagination_total": "Total ${total} items", "ai_feedback_state": "State", "ai_feedback_state_ignore": "ignore", "ai_feedback_state_processed": "processed", "ai_feedback_state_unprocessed": "-", "ai_feedback_time": "Time", "ai_feedback_user": "User", "ai_form_submit_success": "Thank you for your submission!", "ai_input_credit_usage_tooltip": "Engaging in a conversation uses computational power points, and different conversation models consume varying amounts of these points.", "ai_latest_train_date": "Last Trained: ${date}", "ai_mark_state": "Mark as:", "ai_message_credit_title": "Credit usage", "ai_new_agent": "New AI agent", "ai_new_chatbot": "New AI agent", "ai_new_conversation_btn_text": "New chat", "ai_not_set_datasheet_source_error": "I'm sorry, but I don't have the answer to your question right now. Please click on the \"Training\" button in the top right corner of the interface to add datasheets and start training.", "ai_open_remark_can_not_empty": "Please provide a text to let users know how the AI agent can assist them.", "ai_open_remark_desc": "The content that the AI agent will say when a member chats with it for the first time.", "ai_open_remark_title": "Opening remarks", "ai_page_loading_text": "Loading...", "ai_preview_title": "Preview", "ai_prompt_desc": "The starting text or message that a user provides to initiate a conversation with the language model. It can be thought of as a prompt or a cue for LLM to generate a response.", "ai_prompt_title": "Prompt", "ai_prompt_wrong_content": "Please include {context} and {question} in the text.", "ai_query_of_number": "Message Credit", "ai_remain_credit_label": "Chat Left Times: ${credit}", "ai_retrain": "Retrain", "ai_robot_data_source_title": "Datasets", "ai_robot_type_QA_desc": "A NLP and machine learning-based Q&A agent quickly and accurately answers various questions and provides real-time information and useful advice to users.", "ai_robot_type_normal_desc": "This free-chatting agent can answer various questions, provide real-time information, engage in fun small talks, and serve you anytime.", "ai_robot_type_title": "Agent type", "ai_save_and_train": "Save and Train", "ai_select_data_source": "Select the datasheet", "ai_select_form": "Select a form", "ai_send_cancel": "send cancel", "ai_session_time": "Feed<PERSON> on", "ai_setting_change_data_source_tooltip": "Switch data source", "ai_setting_enable_collect_information_desc": "The selected form will appear when the AI agent is unable to answer a question. It will also offer users a convenient shortcut for filling it out at any time in the AI agent's messages and the dialogue's toolbar", "ai_setting_enable_collect_information_title": "Using form to collect information", "ai_setting_max_count": "Do not exceed 255 characters.", "ai_setting_open_data_source_tooltip": "Open the table in a new tab.", "ai_setting_open_url_desc": "When the AI agent is unable to answer a question, it will send the specified URL. Users can also access this URL at any time by clicking on it in the AI agent's messages and the dialogue's toolbar.", "ai_setting_open_url_label": "URL", "ai_setting_open_url_placeholder": "Please enter a URL", "ai_setting_open_url_placeholder_title": "Please enter a URL title", "ai_setting_open_url_title": "Open URL", "ai_setting_open_url_title_label": "URL title", "ai_setting_preview_suggestion_tip_1": "Suggestion 1", "ai_setting_preview_suggestion_tip_2": "Suggestion 2", "ai_setting_preview_suggestion_tip_3": "Suggestion 3", "ai_setting_preview_user_chat_message": "Does Apple have black color?", "ai_setting_select_datasheet_required": "Please select a datasheet", "ai_setting_select_form_required": "Please select a form", "ai_setting_temperature": "Temperature", "ai_setting_temperature_desc": "The higher the temperature, the more creative the AI agent will be. The lower the temperature, the more predictable the AI agent will be.", "ai_setting_title": "Setting", "ai_settings_similarity_filter": "Similarity Filter", "ai_settings_similarity_filter_advanced": "score_threshold", "ai_settings_similarity_filter_desc": "The relevance level of the AI agent when retrieving related knowledge. Lower thresholds may result in irrelevant information, while higher thresholds may miss useful information.", "ai_settings_similarity_filter_moderate": "Moderate", "ai_settings_similarity_filter_relaxed": "Relaxed", "ai_settings_similarity_filter_strict": "Strict", "ai_show_explore_card_desc": "Display explore card in chat interface with randomly selected questions from the datasets. Explore card will only appear before user start a chat with AI agent.", "ai_show_explore_card_title": "Show explore card", "ai_show_suggestion_for_follow_tip_desc": "System will suggest followup messages to the user that they might want to send to the AI agent.", "ai_show_suggestion_for_follow_tip_title": "Show suggestions for follow-up messages", "ai_stop_conversation": "Stop generating", "ai_toolbar_insight_text": "Insight", "ai_toolbar_publish_text": "Publish", "ai_toolbar_setting_text": "Setting", "ai_toolbar_train_text": "Train", "ai_toolbar_training": "Training", "ai_train": "Train", "ai_train_complete_message": "Successfully trained, the AI agent is now ready for you to start chatting with.", "ai_train_credit_cost": "Total detected characters: ${words} (Retraining is expected to cost ${credit} Credit)", "ai_training_empty_desc": "(Only QA-type AI agents require adding datasheets and training)", "ai_training_empty_title": "No training required for chat-type AI agent.", "ai_training_failed_message": "The AI agent training failed. Please try again later.", "ai_training_failed_message_and_allow_send_message": "The AI agent's training failed, using previous dataset for generating answers.", "ai_training_history_desc": "You can check out the details and data sources for each training task by clicking on the time points below.", "ai_training_history_table_column_datasource": "Data source", "ai_training_history_table_column_total_characters": "Total characters", "ai_training_history_table_column_type": "Type", "ai_training_history_title": "History", "ai_training_history_train_info_create_time": "Start time", "ai_training_history_train_info_credit_cost": "Credit cost", "ai_training_history_train_info_status": "Status", "ai_training_message": "The AI agent is currently learning from all the data in datasheet. Please wait...", "ai_training_message_title": "AI agent is training", "ai_training_page_desc": "Train the AI agent to intelligently engage in conversations with you by reading and learning from the text content of datasheets, attachments, and web pages.", "ai_try_again": "Try again", "ai_typing": "typing...", "ai_update_setting_success": "Update successful.", "alarm_no_member_field_tips": "Please create a new member field first", "alarm_save_fail": "Save fail", "alarm_specifical_field_member": "Notify specific member by field", "alarm_specifical_member": "Notify specific member", "alarm_target_type": "Type", "albania": "Albania", "album": "Album", "album_publisher": "Album publisher", "algeria": "Algeria", "alien": "Anonymous", "alien_tip_in_user_card": "This user does not belong to the current Space", "align_center": "Align Center", "align_left": "Align Left", "align_right": "Align Right", "alipay": "Alipay", "all": "All", "all_editable_fields": "All editable fields", "all_record": "All", "allow_apply_join_space": "Allow requests to join the Space", "allow_apply_join_space_desc": "Allow visitors to request for joining the Space from the sharing page (After the admin approves the request, they can automatically join in)", "already_know_that": "I already know that", "american_samoa": "American Samoa", "and": "and", "andorra": "Andorra", "angola": "Angola", "anguilla": "<PERSON><PERSON><PERSON>", "anonymous": "Anonymous", "antigua_and_barbuda": "Antigua and Barbuda", "api_add": "Add records", "api_attachment_file_size_error": "Uploaded attachment size is 0", "api_call": "Maximum number of API requests per month", "api_cumulative_usage_info": "Excessive use is allowed during the public beta", "api_dashboard_not_exist": "the specified dashboard does not exist", "api_datasheet_not_exist": "Can't find the specified datasheet", "api_datasheet_not_visible": "Datasheet not visible", "api_delete": "Delete records", "api_delete_error": "Failed to delete data", "api_delete_error_foreign_datasheet_deleted": "Failed to delete record because the linked datasheet was deleted", "api_embed_link_id_not_exist": "the specified embed link does not exist, please adjust the linkId and try again", "api_embed_link_instance_limit": "embedded links are not supported for nodes other than datasheet type", "api_embed_link_limit": "the maximum number of embedded links has been reached, only {value} links can be created", "api_enterprise_limit": "sorry, it can only be invoked in golden-level space", "api_example_request": "<PERSON><PERSON> request", "api_example_response": "Returned value example", "api_excess": "API overused", "api_favicon_value_error": "Please make sure your favicon.ico value conforms to the URL standard, such as: https://www.example.com/favicon.ico", "api_fieldid_empty_error": "fieldId should not be empty", "api_fields": "Fields", "api_forbidden": "Access forbidden", "api_forbidden_because_of_not_in_space": "The API token user does not belong to the space, please adjust spaceId or API token and try again", "api_forbidden_because_of_usage": "Access forbidden because your API usage exceeds the monthly rate", "api_frequently_error": "The API request rate for the current space is {value} queries per second (QPS). Upgrading the space allows for a higher call frequency. Contact us: <EMAIL>.", "api_get": "Get records", "api_insert_error": "Failed to create data", "api_more_params": "Other parameters or tips", "api_node_node_type_value_error": "The type must be one of the following values: \"Folder\", \"Datasheet\", \"Form\", \"Dashboard\", \"Mirror\".", "api_node_permission_error": "No permission to operate on the node", "api_node_permission_value_error": "The permissions argument must be one of the following integer values: 0,1,2,3.", "api_not_exists": "The API does not exist", "api_org_enterprise_limit": "This API is only available for the Enterprise plan", "api_org_member_delete_primary_admin_error": "Removal of primary admin is not allowed", "api_org_member_role_error": "The role specified with the unitId {unitId} does not exist", "api_org_member_team_error": "The team specified with the unitId {unitId} does not exist", "api_org_permission_member_deny": "This API is only available for the primary admin and sub-admins who can manage members", "api_org_permission_role_deny": "This API is only available for the primary admin and sub-admins who can manage roles", "api_org_permission_team_deny": "This API is only available for the primary admin and sub-admins who can manage teams", "api_org_role_delete_error": "The role with the specified unitId has teams or members, and cannot be deleted", "api_org_role_name_unique_error": "A role with the same name exists", "api_org_team_delete_error": "The team with the specified unitId has sub-teams or members, and cannot be deleted", "api_org_team_name_unique_error": "A sub-team with the same name already exists in the specified parent team", "api_org_update_deny_for_social_space": "The space of the specified spaceId is a third-party integrated space, and the member's teams cannot be manually modified", "api_panel_add_records": "Add records", "api_panel_delete_records": "Delete records", "api_panel_get_records": "Get records", "api_panel_init_sdk": "Initialize SDK", "api_panel_md_curl_example": "<!--split-->\n\n## Get records\n\n```shell\ncurl \"{{ apiBase }}/fusion/v1/datasheets/{{ datasheetId }}/records?viewId={{ viewId }}&fieldKey={{ fieldKey }}\" \\\n  -H \"Authorization: Bearer {{ apiToken }}\"\n\n```\n\n### Returned data example\n\n```json\n\n{{ response }}\n\n```\n\n### Other parameters and tips\n```js\n/**\n* Note: The maximum concurrent access to data for each APITable is limited to 5 times per second\n* All configurable parameters can be sent through URL Query Params,\n*/\n{\n  /**\n   * (Optional) View ID. Defaults to the first view in the APITable. The request will return the filtered/sorted results in the view, and you can use the fields parameter to filter unnecessary field data\n   */\n  viewId: 'viewId1',\n  /**\n   * (Optional) Specifies the page number of the pagination, which is 1 by default and is used in conjunction with the parameter pageSize.\n   */\n  pageNum: 1,\n  /**\n   * (Optional) Specify the total number of records returned per page, default is 100. This parameter only accepts integers from 1-1000.\n   */\n  pageSize: 100,\n  /**\n   * (Optional) Sorts the records of the specified dimensional table. An array of \"sort objects\". Supported order: 'asc' and reverse order: 'desc'. Note: The sorting criteria specified by this parameter will override the sorting criteria in the view.\n   */\n  sort: [{ field: 'field1', order: 'asc' }],\n  /**\n   * (Optional) An array of recordIds. If this parameter is attached, returns the records array specified in the parameter. The return values are sorted in the order of the passed array. Filtering and sorting are ignored at this time. No pagination, up to 1000 records can be queried each time\n   */\n  recordIds: ['recordId1', 'recordId2'],\n  /**\n   * (Optional) Specify the field to return (the default is the field name, and it can also be specified as the field Id by fieldKey). If this parameter is attached, the returned record collection will be filtered, and only the specified fields will be returned.\n   */\n  fields: ['title', 'detail', 'Number of references'],\n  /**\n   * (Optional) Use formulas as filter conditions to return matching records. Visit https://help.aitable.ai/docs/tutorial-getting-started-with-formulas to learn how to use formulas\n   */\n  filterByFormula: '{Number of references} >  0',\n  /**\n   * (Optional) Limit the total number of records returned. If this value is less than the actual total number of records in the table, the total number of records returned will be limited to this value.\n   */\n  maxRecords: 5000,\n  /**\n   * (Optional) Cell value type, the default is 'json', when 'string' is specified, all values will be automatically converted to string format.\n   */\n  cellFormat: 'json',\n  /**\n   * (Optional) Specifies the query of the field and the returned key. By default the column name 'name' is used. When specified as 'id', fieldId will be used as the query and return method (using id can avoid the problem of code failure caused by the modification of the column name)\n   */\n  fieldKey: 'name',\n}\n```\n\n<!--split-->\n\n## Add records\n```shell\ncurl -X POST \"{{ apiBase }}/fusion/v1/datasheets/{{ datasheetId }}/records?viewId={{ viewId }}&fieldKey={{ fieldKey }}\"  \\\n  -H \"Authorization: Bearer {{ apiToken }}\" \\\n  -H \"Content-Type: application/json\" \\\n  --data '{\n  \"records\": {{ records }},\n  \"fieldKey\": \"{{ fieldKey }}\"\n}'\n\n```\n\n### Returned data example\n\n```json\n{{ response }}\n```\n\n### Other parameters and tips\n\nThe add interface receives an array value, and can create multiple records at the same time, and a single request can create up to 10 records\n\n<!--split-->\n\n## Update records\n```shell\ncurl -X PATCH \"{{ apiBase }}/fusion/v1/datasheets/{{ datasheetId }}/records?viewId={{ viewId }}&fieldKey={{ fieldKey }}\" \\\n  -H \"Authorization: Bearer {{ apiToken }}\" \\\n  -H \"Content-Type: application/json\" \\\n  --data '{\n  \"records\": {{ records }},\n  \"fieldKey\": \"{{ fieldKey }}\"\n}'\n```\n### Returned data example\n\n```json\n{{ response }}\n```\n\n\n### Other parameters and tips\n\nThe update interface receives an array value, which can update multiple records at the same time, and a single request can update up to 10 records\nSpecial attention: update will only update the data under the fields you passed in, and will not affect the data that is not passed in. If you need to clear the value, please pass in null explicitly\n\n\n<!--split-->\n\n## Delete records\n```shell\ncurl -X DELETE \"{{ apiBase }}/fusion/v1/datasheets/{{ datasheetId }}/records?{{ deleteParams }}\" \\\n  -H \"Authorization: Bearer {{ apiToken }}\"\n\n```\n\n### Returned data example\n```json\n{\n  \"code\": 200,\n  \"success\": true,\n  \"message\": \"success\"\n}\n```\n\n### Other parameters and tips\n\nThe delete interface receives an array value, which can delete multiple records at the same time, and a single request can delete up to 10 records\n<!--split-->\n\n## Upload attachments\n\n```shell\ncurl -X POST \"{{ apiBase }}/fusion/v1/datasheets/{{ datasheetId }}/attachments\" \\\n  -H \"Authorization: Bearer {{ apiToken }}\" \\\n  -H \"Content-Type: multipart/form-data\" \\\n  -F \"file=@{your file path};\"\n```\n### Returned data example\n\n*Tips: After uploading, please write the data in data to the attachment cell as soon as possible, otherwise the attachment link may become invalid*\n\n```json\n{\n  \"code\": 200,\n  \"success\": true,\n  \"message\": \"success\",\n  \"data\": {\n    \"id\": \"atcPtxnvqti5M\",\n    \"name\": \"6.gif\",\n    \"size\": 33914,\n    \"mimeType\": \"image/gif\",\n    \"token\": \"space/2020/09/22/01ee7202922d48688f61e34f12da5abc\",\n    \"width\": 240,\n    \"height\": 240,\n    \"url\": \"https://s1.aitable.ai/space/2023/02/09/ed97d84948c648498f6b36b1b769071f\"\n  }\n}\n```", "api_panel_md_js_example": "## Initialize SDK [![github]({{ githubIcon }})](https://github.com/apitable/apitable-sdks)\n\n```js\n// If es6 import does not work，use const APITable = require('apitable').default; to replace import { APITable } from \"apitable\";\n\nconst apitable = new APITable({ token: \"{{ apiToken }}\", fieldKey: \"{{ fieldKey }}\" });\nconst datasheet = apitable.datasheet(\"{{ datasheetId }}\");\n```\n\n<!--split-->\n\n## Get records\n\n```js\n\n// Get the records from a specific view. Returns the first page of records by default\ndatasheet.records.query({ viewId: \"{{ viewId }}\"}).then(response => {\n  if (response.success) {\n    console.log(response.data);\n  } else {\n    console.error(response);\n  }\n});\n\n```\n\n### Returned data example\n\n```json\n{{ response }}\n```\n\n### Other parameters and tips\n```js\n/**\n * Global parameter configuration\n */\nnew APITable({\n  /**\n   * (Required) string Your API Token, used for authentication\n   */\n  token: 'YOUR_API_TOKEN',\n  /**\n   * (Optional) Globally specify the query of the field and the returned key. By default the column name 'name' is used. When specified as 'id', fieldId will be used as the query and return method (using id can avoid the problem of code failure caused by the modification of the column name)\n   */\n  fieldKey: 'name', // Defaults\n  /**\n   * (Optional) Request expiration time\n   */\n  requestTimeout: 60000, // Default 60000ms (10 seconds)\n  /**\n   * (Optional) Target server address\n   */\n  host: 'https://api.aitable.ai/fusion/v1', // Defaults\n});\n\n/*******************************/\n\n// Get record\ndatasheet.record.query({\n  /**\n   * (Optional) View ID. Defaults to the first view in the APITable. The request will return the filtered/sorted results in the view, and you can use the fields parameter to filter unnecessary field data\n   */\n  viewId: 'viewId1',\n  /**\n   * (Optional) Sorts the records of the specified APITable. An array of multiple \"sort objects\". Supported order: 'asc' and reverse order: 'desc'. Note: The sorting criteria specified by this parameter will override the sorting criteria in the view.\n   */\n  sort: [{ field: 'field1': order: 'asc' }],\n  /**\n   * (Optional) An array of recordIds. If this parameter is attached, returns the records array specified in the parameter. The return values are sorted in the order of the passed array. Filtering and sorting are ignored at this time. No pagination, up to 1000 records can be queried each time\n   */\n  recordIds: ['recordId1', 'recordId2'],\n  /**\n   * (Optional) Specify the field to be returned (the default is the field name, and it can also be specified as the field Id by fieldKey). If this parameter is attached, the returned record collection will be filtered, and only the specified fields will be returned.\n   */\n  fields: ['title', 'detail', 'Number of references'],\n  /**\n   * (Optional) Use formulas as filter conditions to return matching records. Visit https://help.aitable.ai/docs/tutorial-getting-started-with-formulas to learn how to use formulas\n   */\n  filterByFormula: '{Number of references} >  0',\n  /**\n   * (Optional) Limit the total number of records returned. If this value is less than the actual total number of records in the table, the total number of records returned will be limited to this value.\n   */\n  maxRecords: 5000,\n  /**\n   * (Optional) Cell value type, the default is 'json', when 'string' is specified, all values will be automatically converted to string format.\n   */\n  cellFormat: 'json',\n  /**\n   * (Optional) Specify the query of the field and the returned key. By default the column name 'name' is used. When specified as 'id', fieldId will be used as the query and return method (using id can avoid the problem of code failure caused by the modification of the column name)\n   */\n  fieldKey: 'name',\n});\n\n/*******************************/\n\n/**\n * It is the same as query to obtain record parameters, and automatically handles pagination. until all the data is obtained.\n */\nconst allRecordsIter = datasheet.records.queryAll()\nfor await (const eachPageRecords of allRecordsIter) {\n  console.log(eachPageRecords)\n}\n```\n\n<!--split-->\n\n## Add records\n```js\n// The add method receives an array value, and can create multiple records at the same time, and a single request can create up to 10 records\ndatasheet.records.create({{ records }}).then(response => {\n  if (response.success) {\n    console.log(response.data);\n  } else {\n    console.error(response);\n  }\n})\n```\n\n### Returned data example\n\n```json\n{{ response }}\n```\n\n\n### Other parameters and tips\n\nThe add interface receives an array value that can create multiple records simultaneously, up to 10 records per request\n\n<!--split-->\n\n## Update records\n```js\n/**\n* update receives an array value, multiple records can be updated at the same time, and a single request can update up to 10 records\n* Special Note: update will only update the data under the fields you passed in, and will not affect the unpassed fields. If you need to clear the value, please pass in null explicitly\n*/\ndatasheet.records.update({{ records }}).then(response => {\n  if (response.success) {\n    console.log(response.data);\n  } else {\n    console.error(response);\n  }\n})\n```\n### Returned data example\n```json\n{{ response }}\n```\n\n\n### Other parameters and tips\n\nThe update interface receives an array value, which can update multiple records at the same time, and a single request can update up to 10 records\nSpecial attention: update will only update the data under the fields you passed in, and will not affect the data that is not passed in. If you need to clear the value, please pass in null explicitly\n  \n<!--split-->\n\n## Delete records\n```js\n// The del method receives an array value and can delete multiple records at the same time. A single request can delete up to 10 records\ndatasheet.records.delete({{ recordIds }}).then(response => {\n  if (response.success) {\n    console.log(response.data);\n  } else {\n    console.error(response);\n  }\n})\n```\n### Returned data example\n```json\n{\n  \"code\": 200,\n  \"success\": true,\n  \"message\": \"success\"\n}\n```\n### Other parameters and tips\nThe delete interface receives an array value, which can delete multiple records at the same time, and a single request can delete up to 10 records\n\n<!--split-->\n\n## Upload attachments\n\n```js\n/*\n* Get the file from the file input\n* <input id=\"input\" name=\"file\" type=\"file\" accept=\"*\" >\n* Or use streams to read files in NodeJs\n* const file = fs.createReadStream(/your/file/path)\n*\n* The browser environment is used as an example below\n*/\nconst input = document.getElementById('input');\n\ninput.onchange = function () {\n  const file = this.files[0];\n  datasheet.upload(file).then(response => {\n    /**\n     * response \n     *   success: boolean\n     *   code: number\n     *   message: string\n     *   data: IAttachment\n     */\n    if (response.success) {\n      console.log(response.data);\n    } else {\n      console.error(response);\n    }\n  });\n};\n\n```\n### Returned data example\n\n*Tips: After uploading, please write the data in data to the attachment cell as soon as possible, otherwise the attachment link may become invalid*\n\n```json\n{\n  \"code\": 200,\n  \"success\": true,\n  \"message\": \"success\",\n  \"data\": {\n    \"id\": \"atcPtxnvqti5M\",\n    \"name\": \"6.gif\",\n    \"size\": 33914,\n    \"mimeType\": \"image/gif\",\n    \"token\": \"space/2020/09/22/01ee7202922d48688f61e34f12da5abc\",\n    \"width\": 240,\n    \"height\": 240,\n    \"url\": \"https://s1.aitable.ai/space/2023/02/09/ed97d84948c648498f6b36b1b769071f\"\n  }\n}\n```\n\n### Other parameters and tips\nThe delete interface receives an array value, which can delete multiple records at the same time, and a single request can delete up to 10 records", "api_panel_md_python_example": "## Initialize SDK [![github]({{ githubIcon }})](https://github.com/apitable/apitable-sdks)\n\n```python\nfrom apitable import Apitable\napitable = Apitable(\"{{ apiToken }}\")\ndatasheet = apitable.datasheet(\"{{ datasheetId }}\", field_key=\"{{ fieldKey }}\")\n```\n\n{{ fieldNameTips }}\n<!--split-->\n\n## Get records\n\n```python\n# Returns all records\nrecords = datasheet.records.all()\nfor record in records:\n  print(record.json())\n\n# Returns the records from a specific view\nrecords_via_view = datasheet.records.all(viewId=\"{{ viewId }}\")\n# Get one record (if there are more than one records in the result, return the first record); It's recommended to get one record by its recordId.\none_record = datasheet.records.get({{ pyGetParams }})\n# Returns the records that matches the given condition. If you don't give any condition, it returns all records.\nrecords = datasheet.records.filter({{ pyGetParams }})\n```\n<!--split-->\n\n## Add records\n```python\n# Add one record\nrow = datasheet.records.create({{ addParams }})\n# Add multiple records\nrecords = datasheet.records.bulk_create({{  bulkAddParams }})\n\n```\n<!--split-->\n\n## Update records\n```python\nrow = datasheet.records.get({{ pyGetParams }})\n\n# Update one record\nrow.{{ oneFieldKey }} = {{ oneFieldValue }}\n## row.save()\n\n# Update multiple records\nrow.update({{ updateParams }})\n\n```\n<!--split-->\n\n## Delete records\n```python\nrecord = datasheet.records.get({{ pyGetParams }})\n# Delete one record\nrecord.delete()\n# Delete the records that matches a specific condition\ndatasheet.records.filter({{ pyGetParams }}).delete()\n# Delete the records by recordIds\ndatasheet.delete_records({{ recordIds }})\n```\n<!--split-->\n\n## Upload attachments\n\n```python\n_file = datasheet.upload_file(<local/web_filepath>)\nrecord.{{ attachFieldName }} = [_file]\n```", "api_panel_other_params_and_tips": "Description", "api_panel_return_example": "Returned value example", "api_panel_title": "API panel", "api_panel_type_default_example_attachment": "[\n    {\n        \"id\": \"atcPtxnvqti5M\",\n        \"name\": \"6.gif\",\n        \"size\": 33914,\n        \"mimeType\": \"image/gif\",\n        \"token\": \"space/2020/09/22/01ee7202922d48688f61e34f12da5abc\",\n        \"width\": 240,\n        \"height\": 240,\n        \"url\": \"__host__/space/2020/09/22/01ee7202922d48688f61e34f12da5abc\"\n    }\n]", "api_panel_type_default_example_auto_number": "10001", "api_panel_type_default_example_checkbox": "true", "api_panel_type_default_example_created_by": "{\n    \"uuid\": \"aa3e6af7041c4907ba03889acc0b0cd1\",\n    \"name\": \"<PERSON><PERSON>\",\n    \"avatar\": \"__host__/public/2020/08/03/574bcee4cfc54f6fbb7d686bb237f6f3\"\n}", "api_panel_type_default_example_created_time": "1600777860000", "api_panel_type_default_example_currency": "8.88", "api_panel_type_default_example_date_time": "1600777860000", "api_panel_type_default_example_email": "<EMAIL>", "api_panel_type_default_example_formula": "Fill in this field first, and you can check example value", "api_panel_type_default_example_last_modified_by": "{\n    \"uuid\": \"aa3e6af7041c4907ba03889acc0b0cd1\",\n    \"name\": \"<PERSON><PERSON>\",\n    \"avatar\": \"__host__/public/2020/08/03/574bcee4cfc54f6fbb7d686bb237f6f3\"\n}", "api_panel_type_default_example_last_modified_time": "1600777860000", "api_panel_type_default_example_link": "[\n    \"rec8116cdd76088af\",\n    \"rec245db9343f55e8\",\n    \"rec4f3bade67ff565\"\n]", "api_panel_type_default_example_look_up": "Fill in this field first, and you can check example value", "api_panel_type_default_example_member": "[\n    {\n        \"id\": \"1291258301781176321\",\n        \"type\": 3,\n        \"name\": \"<PERSON>\",\n        \"avatar\": \"https://s1.aitable.ai/space/2023/02/09/79e112dd10424ac7842256736e4f5568\"\n    }\n]", "api_panel_type_default_example_multi_select": "[ \"Option A\", \"Option B\" ]", "api_panel_type_default_example_number": "8", "api_panel_type_default_example_one_way_link": "[\n    \"rec8116cdd76088af\",\n    \"rec245db9343f55e8\",\n    \"rec4f3bade67ff565\"\n]", "api_panel_type_default_example_percent": "0.88", "api_panel_type_default_example_phone": "138xxxx7240", "api_panel_type_default_example_rating": "1", "api_panel_type_default_example_single_select": "Option A", "api_panel_type_default_example_single_text": "This is text in single line", "api_panel_type_default_example_text": "This is text\nin two lines", "api_panel_type_default_example_url": "https://aitable.ai", "api_panel_type_desc_attachment": "Returns an array of attachment objects. Each object includes the following properties: 1. mimeType (string): the media type of the attachment; 2. name (string): the name of the attachment; 3. size (number): the size of the attachment (byte); 4. width (number): the width of the image (px), if the attachment is an image; 5. height (number): the height of the image (px), if the attachment is an image; 6. token (string): the access path of the attachment; 7. preview (string): the preview image URL that you can open in a browser, if the attachment is a PDF", "api_panel_type_desc_autonumber": "Returns a positive number. The system automatically generates the numbers so you can't write into the field by API.", "api_panel_type_desc_cascader": "Returns the selected options as an array of strings", "api_panel_type_desc_checkbox": "Returns true as boolean format if the checkbox is checked. Otherwise the return result will not include this Checkbox field.  ", "api_panel_type_desc_created_by": "Returns an array that contains one unit object. The unit is the member that creates the record. (A unit describes the roles in a Space such as a member or a team. \"unitType\" has two values: 1 stands for team; 3 stands for member.)", "api_panel_type_desc_created_time": "Returns a timestamp in milliseconds", "api_panel_type_desc_currency": "Returns a positive or negative number. The return result will not include the precision or symbol given in the field settings.", "api_panel_type_desc_date_time": "Returns a timestamp in milliseconds", "api_panel_type_desc_email": "Returns an email address as a string", "api_panel_type_desc_formula": "Returns a number, string, or boolean result. The formula result is calculated by the system. You can't write into the Formula field by API. ", "api_panel_type_desc_last_modified_by": "Returns an array that contains one unit object. The unit is the last unit that modifies the record or the specified field(s). (A unit describes the roles in a Space such as a member or a team. \"unitType\" has two values: 1 stands for team; 3 stands for member.)", "api_panel_type_desc_last_modified_time": "Returns a timestamp in milliseconds", "api_panel_type_desc_link": "Returns an array of strings. Each string is the ID of a record that is added into the Two Way Link field.", "api_panel_type_desc_look_up": "Returns a number, string, or array. The Lookup result is calculated by the system. You can't write into the Lookup field by API. ", "api_panel_type_desc_member": "Returns an array of unit objects. A unit describes the roles in a Space such as a member or a team. \"type\" has two values: 1 stands for team; 3 stands for member.", "api_panel_type_desc_multi_select": "Returns the selected options as an array of strings", "api_panel_type_desc_number": "Returns a positive or negative number. The return result will not include the precision or symbol given in the field settings.", "api_panel_type_desc_one_way_link": "Returns an array of strings. Each string is the ID of a record that is added into the One Way Link field.", "api_panel_type_desc_percent": "Returns a positive or negative number. The return result will not include the precision or symbol given in the field settings.", "api_panel_type_desc_phone": "Returns a phone number as a string", "api_panel_type_desc_rating": "Returns a positive number between 1 and 10. The return result will not include this field if the value is empty.", "api_panel_type_desc_single_select": "Returns the selected option as a string", "api_panel_type_desc_single_text": "Returns a string without \"\\n\" line breaks", "api_panel_type_desc_text": "Returns a string with \"\\n\" line breaks", "api_panel_type_desc_url": "Returns a URL object, including title (webpage title), text (webpage address), favicon (webpage icon)", "api_panel_update_records": "Update records", "api_panel_upload_file": "Upload attachments", "api_param_add_widget_btn_type_error": "the parameter addWidgetBtn must be a boolean type", "api_param_api_btn_type_error": "the parameter apiBtn must be a boolean type", "api_param_attachment_array_type_error": "Attachment type must be array", "api_param_attachment_name_type_error": "Attachment name must be string", "api_param_attachment_not_exists": "The attachment specified by the token does not exist", "api_param_attachment_token_type_error": "Attachment token must be string", "api_param_basic_tools_type_error": "the parameter bannerLogo must be a boolean type", "api_param_checkbox_field_type_error": "field:{field} Checkbox field value must be boolean", "api_param_collaborator_status_bar_type_error": "the parameter collaboratorStatusBar must be a boolean type", "api_param_collapsed_type_error": "the parameter collapsed must be a boolean type", "api_param_currency_field_type_error": "field:{field} Currency field value must be a number", "api_param_datetime_field_type_error": "field:{field} Date field value must be a UTC or timestamp", "api_param_default_error": "Parameter error", "api_param_email_field_type_error": "field:{field} Email field value must be string", "api_param_embed_link_id_not_empty": "the parameter linkId is required", "api_param_embed_permission_type_error": "the parameter permissionType must be one of the following values: readOnly, publicEdit, privateEdit", "api_param_filter_field_not_exists": "The filtered field \"{fieldId}\" does not exist", "api_param_form_btn_type_error": "the parameter formBtn must be a boolean type", "api_param_form_setting_btn_type_error": "the parameter formSettingBtn must be a boolean type", "api_param_formula_error": "{value}", "api_param_full_screen_btn_type_error": "the parameter fullScreenBtn must be a boolean type", "api_param_history_btn_type_error": "the parameter historyBtn must be a boolean type", "api_param_invailid_datasheet_name": "datasheet name cannot be empty\n", "api_param_invalid_rating_field": "Rating field value can't be smaller than 0 (only supports 0, 1, 2, 3, 4, or 5)", "api_param_invalid_record_id_value": "The record specified by the recordId does not exist", "api_param_invalid_space_id_value": "Space doesn't exist, please adjust the spaceId and try again", "api_param_link_field_type_error": "field:{field} Link field value must be array", "api_param_member_field_type_error": "field:{field} Member field value must be array", "api_param_member_id_type_error": "field:{field} Member ID must be string\n", "api_param_multiselect_field_type_error": "field:{field} Multiple Select field value must be array", "api_param_multiselect_field_value_type_error": "field:{field} Multiple Select field value must be string", "api_param_node_id_not_empty_key": "the parameter nodeId is required", "api_param_node_info_bar_type_error": "the parameter titleBar must be a boolean type", "api_param_number_field_type_error": "field:{field} Number field value must be a number", "api_param_parent_unit_not_exists": "The unit specified by the parentUnitId does not exist", "api_param_payload_banner_logo_type_error": "the parameter bannerLogo must be a boolean type", "api_param_payload_editable_type_error": "the parameter editable must be a boolean type", "api_param_percent_field_type_error": "field:{field} Percent field value must be a number", "api_param_phone_field_type_error": "field:{field} Phone field value must be string", "api_param_property_error": "Invalid [${property}]", "api_param_rating_field_type_error": "field:{field} Rating field value must be a number", "api_param_record_archived": "The operation cannot be performed because the necessary records are archived. Please unarchive them and try again", "api_param_record_not_exists": "The record specified by the recordId does not exist", "api_param_robot_btn_type_error": "the parameter robotBtn must be a boolean type", "api_param_select_field_value_type_error": "field:{field} Single Select field value must be string", "api_param_sequence_type_error": "The value of the parameter sequence must be a number", "api_param_share_btn_type_error": "the parameter shareBtn must be a boolean type", "api_param_singletext_field_type_error": "field:{field} Single Select field value must be string", "api_param_sort_field_not_exists": "The sorted field \"{fieldId}\" does not exist", "api_param_sort_missing_field": "The \"sort\" parameter misses the \"field\" field", "api_param_tabbar_type_error": "the parameter tabBar must be a boolean type", "api_param_text_field_type_error": "field:{field} Text field value must be string", "api_param_theme_type_error": "the parameter theme must be one of the following values: dark, light", "api_param_toolbar_type_error": "the parameter toolBar must be a boolean type", "api_param_type_error": "{property} must be the \"{value}\" type", "api_param_unit_id_required": "The parameter unitId is required", "api_param_unit_name_required": "the parameter name is required", "api_param_unit_name_type_not_exists": "The unit specified by the unit Name does not exist", "api_param_unit_not_exists": "The unit specified by the unit ID does not exist", "api_param_url_field_type_error": "field:{field} URL field value must be string", "api_param_validate_error": "{message}", "api_param_view_not_exists": "The specified view does not exist", "api_param_viewid_empty_error": "the parameter viewId should not be empty", "api_param_viewid_type_error": "the parameter toolBar must be a string type", "api_param_viewids_empty_error": "viewIds should not be empty", "api_param_widget_btn_type_error": "the parameter widgetBtn must be a boolean type", "api_param_widget_id_not_exists": "The widget specified by the widgetId does not exist", "api_params_automumber_can_not_operate": "Autonumber field can't be edited", "api_params_can_not_operate": "{field} could not create or update", "api_params_cellformat_error": "cellFormat value only supports \"string\" or \"id\"", "api_params_created_time_can_not_operate": "Created Time field can't be edited", "api_params_createdby_can_not_operate": "Created By field can't be edited", "api_params_empty_error": "{property} should not be empty", "api_params_formula_can_not_operate": "Formula field can't be edited", "api_params_instance_attachment_name_error": "Attachment name can't be null", "api_params_instance_attachment_token_error": "Attachment token can't be null", "api_params_instance_error": "{property} must contain {value} keys", "api_params_instance_fields_error": "fields value can't be null", "api_params_instance_member_name_error": "Member name can't be null", "api_params_instance_member_type_error": "Member type can't be null", "api_params_instance_recordid_error": "recordId value can't be null", "api_params_instance_sort_error": "sort value can't be null", "api_params_instance_space_id_error": "the parameter nodeId is required", "api_params_invalid_field_key": "Invalid fieldKey (only supports \"id\" or \"name\")", "api_params_invalid_field_type": "The given field type is not supported", "api_params_invalid_fields_value": "The format of the fields parameter value is wrong", "api_params_invalid_order_sort": "Sort value invalid (only supports \"desc\" or \"asc\")", "api_params_invalid_primary_field_type_error": "The primary field type can't be {value}", "api_params_invalid_sort": "", "api_params_invalid_value": "Invalid value for {property}", "api_params_link_field_recordids_empty_error": "field:{field} recordId value can't be empty array or null", "api_params_link_field_recordids_not_exists": "The linked field specifies a record ({recordId}) that does not exist", "api_params_link_field_records_max_count_error": "field:{field} The Link field was set to receive one recordId only", "api_params_lookup_can_not_operate": "Lookup field can't be edited", "api_params_lookup_field_can_not_sort": "The sorting field ID \"{fieldId}\" specified by the lookup field cannot be used as a sorting field.", "api_params_lookup_filter_field_invalid_operation": "The filtering field ID \"{fieldId}\" specified by the lookup field does not support the \"{operator}\" operator.", "api_params_lookup_filter_field_not_exists": "The filtering field ID \"{fieldId}\" specified by the lookup field does not exist.", "api_params_lookup_related_field_not_link": "The field ID \"{fieldId}\" associated with the lookup field is not a link field.", "api_params_lookup_related_link_field_not_exists": "The link field ID \"{fieldId}\" associated with the lookup field does not exist.", "api_params_lookup_sort_field_not_exists": "The sorting field ID \"{fieldId}\" specified by the lookup field does not exist.", "api_params_lookup_target_field_not_exists": "The field ID \"{fieldId}\" referenced by the lookup field does not exist.", "api_params_max_count_error": "{property} must contain no more than {value} elements", "api_params_max_error": "{property} must not be greater than {value}", "api_params_max_length_error": "${property}'s length must not be greater than ${value}", "api_params_maxrecords_min_error": "maxRecords value can't be smaller than 1", "api_params_member_field_records_max_count_error": "The Member field was set to receive one unit only", "api_params_min_error": "{property} should not be equal to {value}, and {property} must not be less than 1", "api_params_must_unique": "{property} must be unique", "api_params_not_exists": "{property} \"{value}\" does not exist", "api_params_pagenum_min_error": "pageNum value can't be smaller than 1", "api_params_pagesize_max_error": "pagesize value can't be greater than 1000", "api_params_pagesize_min_error": "pagesize value can't be smaller than 1", "api_params_primary_field_not_allowed_to_delete": "The primary field can't be deleted", "api_params_rating_field_max_error": "Rating field value can't be greater than 5 (only supports 0, 1, 2, 3, 4, or 5)", "api_params_recordids_empty_error": "recordId value can't be empty array or null ", "api_params_records_empty_error": "records value can't be empty array or null ", "api_params_records_max_count_error": "Can't update/delete/create more than ${count}  records in one single request", "api_params_tablebundle_max_count_error": "Creation failed, the current workbench is ${product}, only ${count} snapshots can be saved.", "api_params_tree_select_can_not_operate": "It is now allowed to edit cascader field", "api_params_updated_time_can_not_operate": "Last Edited Time field can't be edited", "api_params_updatedby_can_not_operate": "Last Edited By field can't be edited", "api_params_views_max_count_error": "", "api_params_widget_package_id_error": "The parameter widgetPackageId is required", "api_params_workdoc_can_not_operate": "WorkDoc field can't be edited", "api_query_params_invalid_fields": "Invalid fields", "api_query_params_view_id_not_exists": "View ({viewId}) does not exist", "api_request_success": "Success", "api_sdk": "Developer center", "api_server_error": "SERVER_ERROR ({value})", "api_set_view_lock_error": "Failed to update view lock info", "api_show_token": "Show API Token", "api_space_capacity_over_limit": "The Space has reached its maximum attachment capacity", "api_token_generate_tip": "You don't have an API Token. Generate one by clicking on \"+\".", "api_unauthorized": "Unauthorized", "api_update": "Update records", "api_update_error": "Failed to update data", "api_upload": "Upload attachments", "api_upload_attachment_error": "Failed to upload attachment ", "api_upload_attachment_exceed_capacity_limit": "The uploaded attachment exceeds the capacity limit in your Space", "api_upload_attachment_exceed_limit": "You have uploaded the maximum number of attachments", "api_upload_attachment_not_editable": "Can‘t upload attachments because you can't edit the datasheet", "api_upload_attachment_oversize": "The attachment size exceeds 1 GB", "api_upload_invalid_file": "Attachment can't be empty ", "api_upload_invalid_file_name": "Attachment name can't be empty ", "api_upload_tip": "(After uploading, write the data to the attachment cells as soon as possible, otherwise the link of the attachment might get invalid)", "api_usage": "API Usage", "api_usage_info": "Spaces with different subscription plans have different times of API calls per month. If you don't purchase the API package, you can't call API excessively. The usage amount is cleared up on the next monthly.", "api_view_fieldid_not_exist": "the field corresponding to fieldId does not exist", "api_view_filter_conditions_empty_error": "conditions should not be empty", "api_view_filter_operator_not_support": "the value of operator does not match the field", "api_view_filter_operator_value_error": "operator must be one of the following values: is, isNot, contains, doesNotContain, isEmpty, isNotEmpty, isGreater, isGreaterEqual, isLess, isLessEqual, isRepeat", "api_view_rules_empty_error": "rules should not be empty", "api_view_type_error": "type must be one of the following values: Grid, Gallery, Gantt, Kanban, Calendar, Architecture", "api_widget_number_limit": "The number of widget has reached the upper limit, and the creation failed.", "api_your_token": "_Paste_Your_API_Token_", "apitable_choose_basic": "Start Now", "apitable_choose_community": "Download", "apitable_choose_custom": "Request Trial", "apitable_choose_enterprise": "Choose Enterprise", "apitable_choose_plus": "Choose Plus", "apitable_choose_pro": "Choose Pro", "apitable_confirm_password": "Confirm password", "apitable_forget_password_button": "Forgot your password?", "apitable_forget_password_done": "Done", "apitable_forget_password_text": "I already remembered", "apitable_no_account": "No account?", "apitable_og_product_description_content": "APITable is an API-first, embed-friendly no-code database, the best Airtable open-source alternative. Get your data and projects managed magically.", "apitable_og_site_name_content": "APITable | Incredibly Simple and Powerful Work Management OS", "apitable_origin_price_by_month": "{\n    \"plus\": \"$8\",\n    \"pro\": \"$16\"\n}", "apitable_origin_price_by_year": "{\n    \"plus\": \"$6\",\n    \"pro\": \"$14\"\n}", "apitable_password_input_placeholder": "Password between 8 and 24 characters", "apitable_price_sub_title": "Early Bird Offer ${discount} Ends March 7,2023", "apitable_privatized_deployment_desc": "Manage it yourself", "apitable_public_cloud_desc": "APITable as a service", "apitable_sign_in": "Sign in", "apitable_sign_up": "Sign up", "apitable_sign_up_text": "Already have an account?", "app_closed": "Recommended integrations", "app_launch_guide_text_1": "Thousands of members operate one datasheet at the same time, efficient real-time collaboration", "app_launch_guide_text_2": "Dynamic data dashboard, focus on key data in real time", "app_launch_guide_text_3": "Change as needed, fully adapt to the needs of different scenarios", "app_launch_guide_text_4": "Switch multi-dimensional views at your will  to display personalized data", "app_launch_guide_title_1": "Teamwork", "app_launch_guide_title_2": "Data visualization", "app_launch_guide_title_3": "Massive templates", "app_launch_guide_title_4": "Multiple Views", "app_load_failed": "Loading failed, please refresh and try again", "app_modal_content_policy": "Thank you for using our services, the following policies will help you understand the collection, usage, and storage of your personal information, and your related rights. \\nPlease check", "app_modal_content_policy_suffix": "<PERSON><PERSON> agree to start receiving our service.", "app_opening": "Installed application", "app_reload": "Try loading again", "app_sumo_plan_desc": "App Sumo 的描述", "app_timeout_to_refresh": "The network connection timed out, please refresh and try again", "application_integration_information": "Third party application integration information", "apply_join_space": "Apply to join the Space", "apply_join_space_alert_text": "You can apply to join this Space", "apply_join_space_modal_content": "The Space admin of \"${spaceName}\" will receive your application", "apply_join_space_modal_title": "Apply to join this Space", "apply_join_space_success": "The request has been sent to the Space admin. You will be informed in the notification center once the request is approved.", "apply_space_beta_feature_success_notify_all": "<a class=\"memberName\"></a>'s application for enabling the beta feature \"<a class=\"featureName\"></a>\" has been approved. For details, go to \"Account\" -> \"Experimental features\".", "apply_space_beta_feature_success_notify_me": "Your application for enabling the beta feature \"<a class=\"featureName\"></a>\" has been approved. For details, go to \"Account\" -> \"Beta Feature\".", "apply_template": "Use template", "appoint_permission_tip": "Only members below have access to this file node", "apps_support": "All-platform client support", "archive_delete_record": "Delete archived records", "archive_delete_record_title": "Delete record", "archive_notice": "<p>You are trying to archive specific records. Archiving the records will result in the following changes:</p><p>1. Editing is not supported</p><p>2. Functions such as date reminders and subscribe records are not supported</p><p>3. No longer participate in the calculation of lookup, formula and other fields</p><p>Are you sure you want to continue? (You can unarchive in Archive Box in Advanced)</p>", "archive_record_in_activity": " archived this record", "archive_record_in_menu": "Archive record", "archive_record_success": "Successfully archived records", "archive_records_in_menu": "Archive ${chose_num} records", "archive_records_success": "${chose_num} record(s) successfully archived", "archived_action": "Action", "archived_by": "Archied by", "archived_failure": "Transferring failed", "archived_operator_description": "${archived_operator} archived ${archived_num} record(s)", "archived_records": "Archive Box", "archived_select_info": "Selected${selected}items", "archived_successfully": "Transferred", "archived_time": "Archied time", "archived_undo": "Unarchive", "argentina": "Argentina", "armenia": "Armenia", "array_functions": "Array Function", "arts_and_culture": "Arts and culture", "aruba": "Aruba", "asc_sort": "Descending", "assistant": "Assistant", "assistant_activity_train_camp": "Time-limited welfare", "assistant_beginner_task": "Beginner tasks", "assistant_beginner_task_1_what_is_datasheet": "What is APITable?", "assistant_beginner_task_2_quick_start": "Quick start in one minute", "assistant_beginner_task_3_how_to_use_datasheet": "How to use a datasheet", "assistant_beginner_task_4_share_and_invite": "Share and invite friends", "assistant_beginner_task_5_onboarding": "Smart onboarding", "assistant_beginner_task_6_bind_email": "Bind email", "assistant_beginner_task_title1": "Welcome to APITable", "assistant_beginner_task_title2": "Follow the guide and start your journey in APITable~", "assistant_hide": "<PERSON><PERSON> assistant", "assistant_release_history": "Update history", "associated_element": "Associated element", "association_table": "Linked datasheet", "async_compute": "Async computing", "at_least_select_one": "Select at least one member", "at_least_select_one_field": "Select at least one member field", "atlas": "Atlas", "atlas_grade_desc": "Consistent benefits across spaces within Atlas,Members in the Spaces of different Atlas enjoy different benefits.", "attachment_capacity_details_entry": "Details", "attachment_capacity_details_model_capacity_size": "Capacity", "attachment_capacity_details_model_expiry_time": "Expiry time", "attachment_capacity_details_model_expiry_time_permanent": "Permanent", "attachment_capacity_details_model_source": "Source", "attachment_capacity_details_model_tab_expired": "Expired", "attachment_capacity_details_model_tab_in_effect": "In effect", "attachment_capacity_details_model_title": "Attachment capacity details", "attachment_capacity_gift_capacity": "Gift storage", "attachment_capacity_gift_capacity_access_portal": "Invite members to gain", "attachment_capacity_subscription_capacity": "Subscription storage", "attachment_data": "Attachments", "attachment_preview_exit_fullscreen": "Exit Full Screen", "attachment_preview_fullscreen": "Full Screen", "attachment_upload_fail": "Failed to upload ${count} attachment(s)", "audit_add_field_role": "Add field permissions", "audit_add_field_role_detail": "<a class=\"memberName\"></a>In the \"<a class=\"nodeName\"></a>\" datasheet <a class=\"times\"></a>, add [<a class=\"unitName\"></a>] role as [<a class=\"role\"></a>] for the field [<a class=\" fieldName\"></a>] ", "audit_add_node_role": "Add file node permissions", "audit_add_node_role_detail": "Add file node permission，set「${unitNames}」to「${role}」of <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a>", "audit_admin_permission_change_event": "Change admin permissions", "audit_create_template": "Create template", "audit_create_template_detail": "Create template", "audit_datasheet_field_permission_change_event": "Field permissions changed", "audit_delete_field_role": "Delete field permissions", "audit_delete_field_role_detail": "Audit", "audit_delete_node_role": "Delete file node permission", "audit_delete_node_role_detail": "Delete file node permission，delete「${unitNames}」`s 「${role}」role of <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a>", "audit_delete_template": "Delete template", "audit_delete_template_detail": "Delete template", "audit_disable_field_role": "Disable field permissions", "audit_disable_field_role_detail": "Audit", "audit_disable_node_role": "Disable file node permissions", "audit_disable_node_role_detail": "Disable ${nodeType}  <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a> `s permission", "audit_disable_node_share": "Close file node public link", "audit_disable_node_share_detail": "Close ${nodeType} <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a> public link", "audit_enable_field_role": "Enable field permissions", "audit_enable_field_role_detail": "Audit", "audit_enable_node_role": "Enable file node permissions", "audit_enable_node_role_detail": "Enable ${nodeType}  <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a> `s permission", "audit_enable_node_share": "Open file node public link", "audit_enable_node_share_detail": "Open ${nodeType} <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a> public link", "audit_login_event": "Login event", "audit_logout_event": "english", "audit_organization_change_event": "The organizational structure of the contacts has changed", "audit_quote_template": "english", "audit_quote_template_detail": "Audit", "audit_space_cancel_delete": "Audit", "audit_space_cancel_delete_detail": "Audit", "audit_space_change_event": "Space information Changed", "audit_space_complete_delete": "Audit", "audit_space_complete_delete_detail": "Audit", "audit_space_create": "Audit", "audit_space_create_detail": "Audit", "audit_space_delete": "Audit", "audit_space_delete_detail": "Audit", "audit_space_entry_workbench": "Audit", "audit_space_entry_workbench_detail": "Member ${member_name} joined the space \n${space_name}", "audit_space_invite_user": "Audit", "audit_space_invite_user_detail": "Audit", "audit_space_node_copy": "Duplicate file node", "audit_space_node_copy_detail": "Duplicate ${nodeType} 「${sourceNodeName}」, the new file node's name is <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a>", "audit_space_node_create": "Create file node", "audit_space_node_create_detail": "Create a ${nodeType} named <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a>", "audit_space_node_delete": "Delete file node", "audit_space_node_delete_detail": "Delete ${nodeType} named <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a>", "audit_space_node_export": "Export datasheet", "audit_space_node_export_detail": "Member ${member_name} exported datsheet ${node_name}", "audit_space_node_import": "Import file node", "audit_space_node_import_detail": "Import a file node named <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${nodeName}」</a>", "audit_space_node_move": "Move file node", "audit_space_node_move_detail": "Move ${nodeType} <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a>to the folder「${parentName}」", "audit_space_node_rename": "Rename file node", "audit_space_node_rename_detail": "Rename ${nodeType} 「${oldNodeName}」to <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${nodeName}」</a>", "audit_space_node_sort": "Audit", "audit_space_node_sort_detail": "Audit", "audit_space_node_update_cover": "Audit", "audit_space_node_update_cover_detail": "Audit", "audit_space_node_update_desc": "Audit", "audit_space_node_update_desc_detail": "Audit", "audit_space_node_update_icon": "Audit", "audit_space_node_update_icon_detail": "Audit", "audit_space_rename": "Audit", "audit_space_rename_detail": "Audit", "audit_space_rubbish_node_delete": "Audit", "audit_space_rubbish_node_delete_detail": "Audit", "audit_space_rubbish_node_recover": "Restore file nodes", "audit_space_rubbish_node_recover_detail": "Restore ${nodeType} <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a> form the trash", "audit_space_template_event": "Template change", "audit_space_update_logo": "Audit", "audit_space_update_logo_detail": "Audit", "audit_store_share_node": "Save shared file node", "audit_store_share_node_detail": "Restore ${nodeType} to the space, name is <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${nodeName}」</a>", "audit_update_field_role": "Audit", "audit_update_field_role_detail": "Audit", "audit_update_node_role": "Modify file node permission", "audit_update_node_role_detail": "Modify file node permission，modify「${unitNames}」`s role to「${role}」of <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a>", "audit_update_node_share_setting": "Modify file node public link settings", "audit_update_node_share_setting_detail": "Modify ${nodeType} <a href=\"${currentNodeURL}\" target=\"_self\" class=\"auditSourceLink\">「${currentNodeName}」</a> public link", "audit_user_login": "User login", "audit_user_login_detail": "Audit", "audit_user_logout": "User logout", "audit_user_logout_detail": "Audit", "audit_user_operation_event": "", "audit_user_quit_space": "Audit", "audit_user_quit_space_detail": "Audit", "audit_work_catalog_change_event": "english", "audit_work_catalog_permission_change_event": "Explorer permissions changed", "audit_work_catalog_share_event": "Sharing events of file nodes in Explorer", "augmented_views": "Augmented Views", "australia": "Australia", "austria": "Austria", "auth_server_extensions_login_description_content": "APITable Unified Login Page", "authorize": "authorize", "auto": "Auto", "auto_cancel_record_subscription": "<a class=\"memberName\"></a> has removed you as a follower of  \"<a class=\"recordTitle\"></a>\" in \"<a class=\"nodeName\"></a>\", and you have automatically unwatch this record", "auto_cover": "Auto", "auto_create_record_subscription": "<a class=\"memberName\"></a> has added you as a follower of  \"<a class=\"recordTitle\"></a>\" in \"<a class=\"nodeName\"></a>\", and you will automatically watch this record", "auto_save_has_been_opend": "Auto saving view configuration", "auto_save_has_been_opend_content": "All members modify the current view configurations will be automatically saved and synchronized to other members.", "auto_save_view_property": "Autosave view", "autofill_createtime": "Autofill creation time when record created", "automation": "Automation", "automation_action_num_warning": "Each automation supports up to ${value} actions at most", "automation_add_a_action": "Add a Action", "automation_content_should_not_empty": "Text can not be empty", "automation_current_month_run_number": "Runs this month", "automation_default_tips": "Choose a trigger to start your automation", "automation_description_more": "When ${triggerName}, ${actions} and finally  ${lastAction}", "automation_description_one": "When ${triggerName}, then ${lastAction}", "automation_description_trigger": "When ${triggerName}", "automation_detail": "Details", "automation_disabled": "Automation is disabled", "automation_dst_not_existed": "Relative datasheet doest not exist", "automation_editor_label": "Can edit steps in automation and viewing the run history of the automation", "automation_empty_warning": "Automation cannot be empty", "automation_enabled": "Automation is enabled", "automation_enabled_return_via_related_files": "Automation is enabled. Please re-enter the original table via \"Related File nodes\" to use the new button.", "automation_field": "Field", "automation_import_variables_from_pre_tep": "Get data from the pre-step", "automation_is_not_yet_enabled": "Automation is not yet enabled, please enable it and try again", "automation_last_edited_by": "Last edited by", "automation_last_edited_time": "Last edited time", "automation_manager_label": "Can perform all actions on the automation", "automation_more": "More", "automation_no_step_yet": "No step yet", "automation_node_intro": "An Automation Node serves as the starting point of an automation workflow, where you can set triggers and corresponding actions", "automation_not_empty": "Automation node can not be empty", "automation_not_save_warning_description": "The changes you made have not been saved. Are you sure you want to leave?", "automation_not_save_warning_title": "Automation Not Save", "automation_notify_creator_option": "Notify creator on error", "automation_please_set_a_trigger_first": "Please set a trigger first", "automation_reader_label": "Can view the run history of the automation", "automation_refresh": "Refresh", "automation_run_fail": "failed", "automation_run_failure": "Over limit", "automation_run_failure_tip": "The current usage of automation run has been used up", "automation_run_history_item_brief_fail": "${ACTION_NAME} failed", "automation_run_history_item_brief_success": "All ${NUM} actions have been successful", "automation_run_history_item_description": "Run ${RESULT} in ${NUM} seconds(${START_TIME} ~ ${END_TIME})", "automation_run_times_over_limit": "The number of automation runs has exceeded the limit.", "automation_run_usage": "Automation usage", "automation_run_usage_info": "Number of runnable automation per month", "automation_runs_this_month": "Runs this month", "automation_stay_tuned": "Stay tuned", "automation_success": "Success", "automation_tips": "The button field is misconfigured, please check and try again", "automation_updater_label": "Can view the run history of the automation", "automation_variabel_empty": "There is no data that can be used in the pre-step, please adjust and try again.", "automation_variable_datasheet": "Get data from ${NODE_NAME}", "automation_variable_trigger_many": "From ${Trigger_Multiple} and ${Trigger_Last}", "automation_variable_trigger_one": "From ${Trigger_Name}", "autonumber_check_info": "To configure the field of self-incrementing ID, operate in the drop-down menu of the field.", "avatar_modified_successfully": "Avatar changed successfully", "azerbaijan": "Azerbaijan", "back": "Return", "back_login": "Back to log in", "back_login_page": "Back to login page", "back_prev_step": "Go back to the previous step", "back_to_space": "Back to my Space", "back_to_workbench": "Return to workbench", "back_workbench": "Workbench", "background_purple": "Purple background", "bahamas": "Bahamas", "bahrain": "Bahrain", "bangladesh": "Bangladesh", "bar_chart": "Bar Chart", "barbados": "Barbados", "basis": "Basic", "batch_edit_permission": "Bulk edit permissions", "batch_import": "Batch Import", "batch_remove": "Remove", "be_invited_to_reward": "Invited by ${name} to reward", "behavior_type": "Activity", "belarus": "Belarus", "belgium": "Belgium", "belize": "Belize", "benchs_per_space": "Benchs per Space", "benin": "Benin", "bermuda": "Bermuda", "bhutan": "Bhutan", "billing_over_limit_tip_common": "The usage of the space has exceeded the limit, and you can enjoy a higher amount after upgrading.", "billing_over_limit_tip_forbidden": "Your trial duration or subscription period has expired. Please contact the sales consultant to renew.", "billing_over_limit_tip_widget": "The number of widget installations has exceeded the limit, and you can upgrade to get higher usage.", "billing_period": "Billing period: ${period}", "billing_subscription_warning": "Feature experience", "billing_usage_warning": "Usage warning", "bind": "Bind", "bind_app_sumo_btn_str": "Activate account", "bind_dingding_account": "Bind DingTalk account", "bind_email": "Bind email ", "bind_email_same": "Please change to a different email address", "bind_form": "Select a form to link to", "bind_phone_same": "Please change to a different phone number", "bind_resource": "Select a datasheet to link to", "bind_state": "Status: ", "bind_time": "Binding date: ", "bind_wechat_account": "Bind WeChat account", "binding_account": "Bind ${mode} account", "binding_account_failure_tip": "This ${mode} has been bound to another account, please change a new one.", "binding_failure": "Binding failed", "binding_success": "Bound", "black_mirror_list_tip": "Want to get quick access to a view from the Explorer? Mirror is your shortcut to a view", "black_space_alert": "The space you are in has exceeded the limit for a long time. In order not to affect your regular use, please click here to upgrade", "bold": "Bold", "bolivia": "Bolivia", "bosnia_and_herzegovina": "Bosnia and Herzegovina", "botswana": "Botswana", "bound": "Bound", "brand_desc": "Powered by ${logo}", "brazil": "Brazil", "bronze": "Bronze", "bronze_btn_text": "Start now", "bronze_grade": "Community", "bronze_grade_desc": "Suitable for individuals or teams who are new to APITable", "bronze_img": "URL", "brunei": "Brunei", "bulgaria": "Bulgaria", "burkina_faso": "Burkina Faso", "burundi": "Burundi", "button": "<PERSON><PERSON>", "button_add": "Add", "button_bind_confirmed": "Confirm binding", "button_bind_now": "Bind now", "button_change_phone": "Edit phone number", "button_check_history": "check the run history.", "button_check_history_end": ".", "button_click_trigger_explanation": "\"Button is Clicked\" is a trigger that activates when a user clicks on the new button column you've created", "button_color": "Button Color", "button_combine": "Buttons combination", "button_come_on": "Let's go!", "button_execute_error": "Execution failed, please try again or check the run history.", "button_field_invalid": "The button field is invalid, please select again", "button_maxium_text": "maximum words ${count} is allowed", "button_operation": "Button Action", "button_style": "Button style", "button_sub_team": "Subordinate", "button_submit": "Submit", "button_submit_anonymous": "Submit", "button_text": "Button text", "button_text_click_start": "Click to Start", "button_type": "Button Type", "by_at": "at", "by_days": "Days", "by_every": "every", "by_field_id": "Use field ID", "by_hours": "Hours", "by_in": " at", "by_min": "minute(s)", "by_months": "Months", "by_on": " on the", "by_the_day": "By day", "by_the_month": "Monthly", "by_the_year": "Yearly", "by_weeks": "Weeks", "calendar_add_date_time_field": "Create Date field", "calendar_color_more": "More colors", "calendar_const_detail_weeks": "[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\",\"Sunday\"]", "calendar_const_month_toggle_next": "Next Month", "calendar_const_month_toggle_pre": "Last Month", "calendar_const_today": "Today", "calendar_const_touch_tip": "Hand off page turning", "calendar_const_weeks": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Sun\"]", "calendar_create_img_alt": "Create Date fields in Calendar View", "calendar_date_time_setting": "Select Date fields", "calendar_drag_clear_time": "Dragging a record back to the list will clear the dates", "calendar_end_field_name": "End date", "calendar_error_record": "End date earlier than start date", "calendar_init_fields_button": "Create Date field", "calendar_init_fields_desc": "To generate a calendar, please create a Date field in your datasheet by clicking the button below", "calendar_init_fields_no_permission_desc": "A Date field is required to generate a calendar", "calendar_list_search_placeholder": "Search", "calendar_list_toggle_btn": "Record list", "calendar_mobile_preparing": "Stay tuned", "calendar_mobile_preparing_desc": "Calendar View on mobile phones is coming", "calendar_mobile_preparing_text": "You can use Calendar View  on a computer for better experience", "calendar_no_permission": "No permission", "calendar_no_permission_desc": "You don't have permission to create a Date field", "calendar_pick_end_time": "End date", "calendar_pick_start_time": "Start date", "calendar_play_guide_video_title": "How to use a Calendar view", "calendar_pre_record_list": "Unassigned records", "calendar_record": "Record", "calendar_setting": "Settings", "calendar_setting_clear_end_time": "None", "calendar_setting_field_deleted": "Date field is deleted", "calendar_setting_help_tips": "Help manual", "calendar_start_field_name": "Start date", "calendar_view": "Calendar", "calendar_view_all_records": "Expand all records", "calendar_view_all_records_mobile": "All records", "calendar_view_desc": "Use a calendar to arrange tasks and view the project schedule", "cambodia": "Cambodia", "cameroon": "Cameroon", "can_control": "Manager", "can_duplicate": "can save as copy", "can_edit": "can edit", "can_manage": "can manage", "can_not_un_bind_content": "Please bind either your phone number or email address to ensure that you can log in", "can_not_un_bind_title": "Can't unbind", "can_read": "Read-only", "can_updater": "Update-only", "can_view": "can view", "canada": "Canada", "cancel": "Cancel", "cancel_favorite_success": "Unpinned successfully", "cancel_market_app_closing": "Cancel Close App", "cancel_watch_record_button_tooltips": "You will not be notified of new comments or changes to the content of the record after cancel watch", "cancel_watch_record_mobile": "Cancel watch", "cancel_watch_record_multiple": "Cancel watch of these ${count} records", "cancel_watch_record_single": "Cancel watch of this record", "cancel_watch_record_success": "Watch cancelled", "cancelled_account": "Deleted user", "cancelled_log_out_succeed": "The account has been restored", "cannot_access": "Can Access", "cannot_activate_space_by_space_limit": "cannot_activate_space_by_space_limit", "cannot_join_space": "You can't join a new space station because you've exceeded the maximum quota of 10 space stations.", "cannot_switch_field_permission": "Set field permissions not higher than the file node permissions. ", "capacity_file_detail_desc": "The total number of file nodes within the Space is calculated by adding the file nodes from the Team to the file nodes created by each member in their Private. Not enough capacity?", "capacity_file_detail_title": "File Capacity", "capacity_file_member": "Member", "capacity_file_member_private_node_count": "Private", "capacity_file_member_private_percent": "Percent", "capacity_file_member_team": "Team", "capacity_file_member_team_node_count": "Team", "capacity_file_upgrade": "Click to upgrade your Space", "capacity_from_official_gift": "From the official gift", "capacity_from_participation": "By invited ${user} join the space", "capacity_from_purchase": "By purchasing capacity", "capacity_from_subscription_package": "By subscription", "capacity_limit": "The maximum number of attachment capacities to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "capacity_limit_email_title": "Usage reminders for the number of attachment capacities in the \"${SPACE_NAME}\" space", "capacity_reach_limit": "The \"<a class=\"spaceName\"></a>\" Space has reached its maximum capacity.", "cape_verde": "Cape Verde", "cascader_config": "<PERSON><PERSON> config", "cascader_datasource": "Configuration datasheet", "cascader_datasource_placeholder": "Please select cascader datasource", "cascader_datasource_refresh": "<PERSON>tch", "cascader_field_config_placeholder": "Select datasource to config cascader", "cascader_field_configuration_err": "Please re-link the cascader configuration datasheet", "cascader_field_select_placeholder": "Select a field", "cascader_how_to_label": "How to config?", "cascader_max_field_tip": "Cascader must have maximum five levels", "cascader_min_field_error": "Cascader must have minimum two levels", "cascader_mobile_unavailable_tip": "Not supported to edit cascader source and rules on mobile devices", "cascader_new_field_tip": "choose field", "cascader_no_data_field_error": "Some fields have no data. Please reselect or create data and try again", "cascader_no_datasheet_error": "Please select a datasheet", "cascader_no_rules_error": "Please set cascader rules", "cascader_no_sync_tip": "The following data will not syncronize with <a href=\"${url}\" target=\"_blank\">「${datasheetName}」</a>. Click button right to fetch latest data if updated.", "cascader_no_view_error": "Please select a view", "cascader_rules": "Cascader rules", "cascader_rules_help_tip": "Help documentation", "cascader_select_tip": "Select data datasource above to show cascader", "cascader_select_view": "Select view", "cascader_show_all": "Display the full hierarchy path", "cascader_snapshot_update_text": "After fetching the latest data, any new records created will use the latest cascading relationships.", "cascader_snapshot_updating": "The cascader configuration is being updated, please be patient", "cascader_undefined_field_error": "There exists unselected field. Please select the field and try again", "catalog": "Explorer", "catalog_add_from_template_btn_title": "Add from templates", "catalog_empty_tips": "This workspace is empty now. Get started by using templates to create file nodes.", "catalog_private": "Private", "catalog_team": "Team", "category_blank": "[Blank]", "catering": "Catering", "cayman_islands": "Cayman Islands", "cell_find_member": "Find a member or team", "cell_find_option": "Find or add an option", "cell_not_exist_content": "The the cell you are editing has been deleted", "cell_not_find_member": "Member unfound", "cell_not_find_member_or_team": "Member or team unfound", "cell_to_down_edge": "Move to the bottom cell", "cell_to_left_edge": "Move to the left-most cell", "cell_to_right_edge": "Move to the right-most cell", "cell_to_up_edge": "Move to the top cell", "central_african_republic": "Central African Republic", "chad": "Chad", "change": "Edit", "change_avatar": "Edit", "change_email": "Edit email", "change_field_to_multi_text_field": "Convert the \"${b}\" field to a Long Text field", "change_main_admin": "Change", "change_member_team_fail": "Change member's team failed.", "change_member_team_level": "Adjust team level", "change_member_team_success": "Change member's team success.", "change_name": "Edit", "change_nickname_tips": "Tip: You can edit your name later in \"My Settings\".", "change_password": "Change password", "change_password_fail": "Failed to change the password", "change_password_success": "Password reset successfully", "change_phone": "Confirm", "change_primary_admin": "Change", "change_primary_admin_succeed": "New admin assigned successfully", "change_space_logo_success": "Space logo changed", "change_space_name_tip": "Note: You can edit your Space name in \"Settings\".", "changeset_diff_big_tip": "Current content has been updated, please refresh to view", "chart_option_field_had_been_deleted": "The field is invalid, please select again", "chart_option_view_had_been_deleted": "The view is invalid, please select again", "chart_settings": "Chart settings", "chart_sort": "Sort by", "chart_sort_by_ascending": "Ascending", "chart_sort_by_descending": "Descending", "chart_sort_by_x_axis": "Sort by X axis", "chart_sort_by_y_axis": "Sort by Y axis", "chart_widget_setting_help_tips": "Manual", "chart_widget_setting_help_url": "http://help.dolphintable.suanlihaiyang.com/docs/intro-widget-chart", "check_detail": "Details", "check_failed_list": "Check failed list", "check_field": "Select a field", "check_link_automation": "Select a automation to link to", "check_link_form": "Select a form to link to", "check_link_table": "Select a datasheet to link to", "check_more_privileges": "See more privileges", "check_network_status": "Please check the network connection", "check_order_status": "Paid?${action}", "check_run_history": "Check run history", "check_save_space": "Please select a Space to save", "check_selected_record": "Show selected only", "check_table_link_field": "Select a Link field", "checked_the_checkbox": "Please tick the checkbox to agree", "chile": "Chile", "china": "Chinese Mainland", "choose_a_member": "Select members", "choose_a_team": "Select team", "choose_datasheet_to_link": "Select a datasheet to link to", "choose_pey_method": "Payment method", "choose_picture": "Select image", "choose_share_mode": "Select the way of sharing", "choose_type_of_Dolphin_field": "Select field type", "choose_your_own_space": "（Only supports saving to your own space  as creator）", "chose_new_primary_admin_button": "Assign", "claim_special_offer": "Claim This Special Offer!", "clear": "Clear", "clear_all_fields": "Clear all", "clear_cell_by_count": "${count} cell(s) emptied", "clear_date": "Clear dates", "clear_record": "Clean cell data", "click_here": "Please click here", "click_here_to_write_description": "Click here to write description", "click_load_more": "Load more", "click_refresh": "Please click to refresh", "click_start": "Click to Start", "click_to_activate_space": "click_to_activate_space", "click_to_agree": "<PERSON>lick to agree ", "click_to_compare_with_detail": "Detail", "click_to_view": "Click to view", "click_to_view_instructions": "See documentation", "click_top_right_to_share": "Click on the top right to share", "click_upload_tip": "Click here to paste an attachment", "client_meta_label_desc": "APITable - a data productivity platform that helps you manage your work data more efficiently", "client_meta_label_file_deleted_desc": "The shared file node is deleted, so the public share link is invalid", "client_meta_label_file_deleted_title": "Invalid shared file node", "client_meta_label_share_disable_desc": "The shared public link is disabled so you can't access the file node", "client_meta_label_share_disable_title": "Can't access", "client_meta_label_template_deleted_desc": "The template does not exist or is deleted, so the share link is invalid", "client_meta_label_template_deleted_title": "Invalid shared template", "client_meta_label_title": "APITable - a data productivity platform", "close": "Close", "close_auto_save": "Turn off autosave view", "close_auto_save_success": "Closing the Autosave view was configured successfully", "close_auto_save_warn_content": "After a member configures the view, it will only take effect for the individual, and will not be saved and synchronized to other members. ", "close_auto_save_warn_title": "Turn off autosave view", "close_card": "Press \"Esc\" to close the card", "close_menu": "Close menu", "close_node_permission_label": "Give permissions manually", "close_node_share_modal_content": "After disabling the sharing, the generated share link will become invalid", "close_node_share_modal_title": "Close the sharing of this node", "close_permission": "Restore default", "close_permission_warning_content": "all members and teams will see the file node, and the set permissions will be closed at the same time", "close_public_link_success": "Disable public link successfully", "close_share_link": "Disable public link", "close_share_tip": "Disable sharing ${status}", "close_view_sync_success": "The view configuration is no longer syncing with other members", "close_view_sync_tip": "Turn off view configuration synchronization", "code_block": "Code block", "code_sweep": "Scan code to join", "collaborate_and_share": "Collaborate", "collaborator_number": "${number} Collaborator(s)", "collapse": "Collapse", "collapse_all_group": "Collapse all groups", "collapse_full_screen": "Exit fullscreen", "collapse_kanban_group": "Collapse group", "collapse_subgroup": "Collapse group/subgroup", "colombia": "Colombia", "color": "Color", "color_add": "Add color", "color_condition_add": "Add condition", "color_description_when_sync_open": "Coloring records based on following options, ", "color_records_based_on_conditions": "Color records based on conditions", "color_rules_description": "When {RULE_DESCRIPTION} then color records", "color_setting": "Color setting", "colord_in_record": "Define color in records", "colored_button": "Colored Button", "colorful_theme": "Multi-color theme", "coloring_based_on_conditions": "Coloring based on conditions", "column": "field", "column_chart": "Column Chart", "columns_count_limit_tips": "Hey, your datasheet has exceeded the maximum ${column_limit} columns.", "comfirm_close_filter_switch": "If you turn off the filter and sort, the conditions will be cleared", "coming_soon": "Coming soon", "comma": ",", "comma_style": "Separator", "command_action_delete": "deleted ${count} data item(s)", "command_action_insert": "inserted ${count} data item(s)", "command_action_move": "moved to row ${count}", "command_action_replace": "replaced ${count} data item(s)", "command_add_record": "added record", "command_delete_field": "deleted field", "command_delete_record": "deleted record", "command_disable_task_reminder": "disabled task reminder", "command_enable_task_reminder": "enabled task reminder", "command_fix_consistency": "fixed data consistency", "command_insert_comment": "commented", "command_move_column": "moved ${record_count} column(s)", "command_move_row": "moved row", "command_paste_set_record": "pasted record", "command_rollback": "rolled back record", "command_set_field_attr": "modified field configuration", "command_set_kanban_style": "set Kanban style", "command_set_record": "edited record", "command_undo_add_record": "canceled adding record", "command_undo_delete_field": "restored the deleted field", "command_undo_delete_records": "restored the deleted record", "command_undo_move_row": "canceled moving row", "command_undo_paste_set_record": "canceled pasting", "command_undo_rollback": "canceled rolling back", "command_undo_set_field_attr": "canceled editing field configuration", "command_undo_set_record": "canceled editing record", "comment_editor_default_tip": "Comment or mention a member...", "comment_from_who": "${who}'s comments", "comment_is_deleted": "The comment was deleted", "comment_mentioned": "<a class=\"memberName\"></a> mentioned you in <a class=\"times\"></a> comments from the \"<a class=\"nodeName\"></a>\" datasheet.", "comment_too_long": "The character length in the comment section shouldn't exceed ${word_count} .", "comments_per_record": "Comments per Record", "common_format": "CommonFormat", "common_system_notify": "", "common_system_notify_web": "", "communication_group_qrcode": "Forum", "community": "Community", "community_and_local_interest": "Community and local interest", "community_edition": "Community", "community_grade_desc": "Free and open source, you can install yourself", "comoros": "Comoros", "company_grade_desc": "For teams with enterprise-level security and consulting need", "company_security": "Security", "complete_bind_email": "Bound an email address successfully", "complete_invited_email_information": "Invited account ${inviteEmail}, please verify first.", "components_checkbox": "Checkbox", "components_popconfirm": "Bubble confirmation box", "config": "Config", "config_field_permission": "Edit field permissions (Beta)", "configuration_available_range": "Configuration available range", "confirm": "Confirm", "confirm_activate_space_tips": "Please activate the \"${spaceName}\" Space first before you enter it", "confirm_activate_space_title": "Activate the space", "confirm_and_continue": "OK", "confirm_cancel": "Your edits have not been saved. Are you sure to leave?", "confirm_change_field": "Converting field type", "confirm_del_current_team": "Are you sure you want to delete the team?", "confirm_delete": "Confirm and delete", "confirm_delete_node_name_as": "Are you sure you want to delete \"${nodeNameDiv}\"?", "confirm_delete_private_node_name_as": "Are you sure you want to delete \"${nodeNameDiv}\"? The file will be deleted from the Space and the trash.", "confirm_delete_space_btn": "Confirm and delete Space", "confirm_exit": "Confirm to exit", "confirm_exit_space_with_name": "Confirm whether to exit the \"${spaceNameDiv}\" Space", "confirm_import": "Import", "confirm_join": "Confirm to join", "confirm_join_space": "Confirm to join the Space", "confirm_link_inconsistency_detected": "Abnormal data have been found in link cells, fix them automatically?", "confirm_link_toggle_clear_filter": "Please make sure to switch the related column. After switching, the filter condition will be cleared", "confirm_logout": "Confirm", "confirm_logout_title": "Please confirm again", "confirm_market_app_closing": "Confirm close", "confirm_open_apply": "Confirm to turn on \"allow applications to join the Space\"", "confirm_open_invite": "Confirm to turn on \"Inviting members\"", "confirm_the_system_has_detected_a_conflict_in_document": "A data conflict was detected in this datasheet. Fix it automatically?", "confirm_unbind": "Confirm to unbind", "confirm_verified_failed_and_get_the_code_again": "Verification code not verified, please resend the code", "confirmation_password_reminder": "Enter the new password again", "connect_us": "Contact us", "contact_data": "Contacts", "contact_model_desc": "Do you have payment problems? Contact us to help you out", "contact_model_title": "Contact your dedicated customer service", "contact_us": "Contact us", "contact_us_qr_code_desc": "Service and answers are always available when you have questions during use", "contact_us_to_join_company_support": "Scan the code to add our customer service and apply for the startup support program.", "contacts": "Contacts", "contacts_configuration": "Contacts", "contacts_invite_link_template": "From APITable: You were invited to join the \"${spaceName}\" space by ${nickName}.", "contacts_management": "Contacts", "contain_filter_count": "Contains ${count} filters\n", "contains": "contains...", "content_is_empty": "Empty", "content_operations": "Content Operations", "content_production": "Content production", "continue_to_pay": "Continue to pay", "convert": "Convert", "convert_tip": "This action may clear up data in some cells. You can undo the action if anything unexpected happens.", "cook_islands": "Cook Islands", "copilot_auto_agent_desc": "Not sure which one to choose Agent? Try AutoAgent.", "copilot_auto_agent_name": "Auto Agent", "copilot_data_agent_desc": "generate visualizations/data analysis based on your views.", "copilot_data_agent_name": "Data Agent", "copilot_data_agent_policy": "When chatting with <PERSON><PERSON><PERSON>, you agree to the user terms policy", "copilot_data_agent_policy_button": "Policy", "copilot_help_agent_desc": "Ask anything about AITable's help center documents .", "copilot_help_agent_name": "Retrieve Help Center", "copy": " Copy", "copy_automation_url": "Copy URL", "copy_card_link": "Copy record URL", "copy_dashboard_url": "Copy URL", "copy_datasheet_url": "Copy URL", "copy_elink_share": "Copy the elink sharing link", "copy_failed": "Co<PERSON> failed", "copy_folder_url": "Copy URL", "copy_form_url": "Copy URL", "copy_from_cell": "Copy cell", "copy_link": "Copy", "copy_link_success": "<PERSON>pied", "copy_mirror_url": "Copy mirror URL", "copy_record_data": "Copy recorded data", "copy_success": "<PERSON>pied", "copy_template_share_link": "Copy template link", "copy_the_cell": "Copy cell", "copy_token": "Copy", "copy_token_toast": "Token copied to the clipboard", "copy_url": "Copy", "copy_url_line": "Copy record URL", "copy_view": "Duplicate view", "copy_widget": "<PERSON><PERSON> Widget", "copy_widget_fail": "copy widget failed", "copy_widget_success": "copy widget successfully", "costa_rica": "Costa Rica", "count_records": "Count records", "cout_records": "All records", "cover": "Cover image", "cover_field": "Cover", "creat_mirror_templete": "Failed to create a template because the mirror was linked to a datasheet outside the folder", "create": "Create", "create_and_save": "Create and save", "create_date": "Creation date", "create_file_and_folder": "Create a new file node or folder", "create_form": "Create form", "create_form_panel_title": "Select a datasheet to store form data", "create_invitation_link": "Create Invitation Link", "create_link_succeed": "Link created", "create_mirror": "Create mirror from this view", "create_mirror_by_view": "Create mirror from this view", "create_mirror_guide_content": "The mirror function has the ability to hide certain data. You can set \"filter conditions\" and \"hidden fields\" in the original datasheet view to control which records and fields are displayed in the mirror.\n</br>\n</br>\nIf used in conjunction with the \"view lock\" function, it can prevent others from making modifications.\n</br>\n</br>\nIn addition, you can go to \"Original Table>Hidden Fields\" to modify the configuration for \"Show all fields in Mirrors\".", "create_mirror_guide_title": "Mirror hides some records and fields", "create_new_button_field": "Create a new button column field", "create_private_node_tip": "Personal or temporary draft documents can be created here, ${link}", "create_public_invitation_link": "Create public invitation link(s)", "create_space_sub_title": "Hi, please give a name to your <PERSON>~", "create_team_fail": "Create team failed", "create_team_success": "Create team success.", "create_token_tip": "To receive the latest news from the API service, please bind your email first", "create_view_first": "New view", "create_view_form": "Create form from this view", "create_widget": "Create widget", "create_widget_step_tooltip": "See documentation", "create_widget_success": "Create widget success", "create_workspace": "Create a Space", "creative": "Creative", "creative_production": "Creative production", "creator": "Creator", "croatia": "Croatia", "crypto_field": "You have no access to the field", "csv": "As .csv format", "cuba": "Cuba", "cui_chat_exit_message": "You have exited form mode and can continue to answer questions", "cui_chat_exit_text": "Skip this, continue chatting", "cui_next_text": "Next", "cui_select_datasheet_description": "Click here to select datasheets", "cui_select_link_text": "Selected ${links}", "cui_select_user_text": "Selected ${users}", "cui_submit_text": "Submit", "cui_wizard_select_chatbot_model": "Select bot model", "cui_wizard_select_chatbot_model_message": "OK. Now, please select the bot model you'd like to use: ${model} or other options.", "cui_wizard_select_chatbot_type": "Select bot type", "cui_wizard_select_chatbot_type_chat_desc": "Engage in free-flowing conversations without specific dataset, adaptable to various topics.", "cui_wizard_select_chatbot_type_qa_desc": "Answer specific questions with predefined dataset, train and customize easily.", "cui_wizard_select_datasheet": "Select a datasheet as dataset", "cui_wizard_select_datasheet_message": "We're almost there, but not quite done yet. Please select a datasheet to serve as the dataset. The AI agent will refer to the knowledge within the dataset to assist with answering the questions you pose.", "cui_wizard_welcome_message_1": "Hello there! 🌟 Your AI agent is ready to roll. But first, let's set the stage with some initial configurations.", "cui_wizard_welcome_message_2": "Please choose the type of AI agent you'd like to create: a Chat agent or a QA agent.", "cumulative_consumption": "Cumulative consumption:${count}", "cur_import_member_count": "${count} members will be imported", "curacao": "Curacao", "currency_cell_input_tips": " Enter only numeric 、'/'、'-' in date cell\n", "currency_field_configuration_default_placeholder": "Prefill new cells with this value", "currency_field_configuration_precision": "Precision", "currency_field_configuration_symbol": "Symbol", "currency_field_symbol_align": "Align symbol", "currency_field_symbol_align_default": "<PERSON><PERSON><PERSON>", "currency_field_symbol_align_left": "Left", "currency_field_symbol_align_right": "Right", "currency_field_symbol_placeholder": "Enter a currency symbol", "current_column_been_deleted": "The current column is deleted or hidden", "current_count_of_person": "Total Seats", "current_datasheet": "Current datasheet", "current_field_fail": "The data went wrong in this field", "current_file_may_be_changed": "Status of current file node has changed", "current_form_is_invalid": "The form is invalid because  the original view has been deleted", "current_grade": "Current", "current_main_admin": "Current admin", "current_phone_has_been_binded_with_other_email": "The phone number has been bound to another email address", "current_subscribe_plan": "Current plan: ${product} ${seats} member(s)", "current_team": "Current team", "current_v_coins": "Balance", "current_view_add_form": "Create form from this view", "custom": "Custom ", "custom_enterprise": "Customize enterprise space for you", "custom_function_development": "Custom feature development", "custom_grade_desc": "Provides agent deployment, private installation, assistance support and customized professional services", "custom_page_setting_title": "Add a custom page", "custom_picture": "Customized picture", "custom_style": "Style", "custom_upload": "Customized upload", "custom_upload_tip": "A 1:1 square size image is recommended for the better visual experience", "custome_page_title": "Custom Web", "cut_cell_data": "Cut cell(s)", "cyprus": "Cyprus", "czech": "Czech", "dark_theme": "Dark", "dashboard": "Dashboard", "dashboard_access_denied_help_link": "http://help.dolphintable.suanlihaiyang.com/docs/intro-dashboard#access-denied", "dashboard_editor_label": "In addition to \"Read-only\", can also edit widget and share dashboard", "dashboard_manager_label": "In addition to \"Editor\",  can also  add or delete dashboard", "dashboard_reader_label": "Can read widget in the dashboard", "dashboard_updater_label": "Same as \"Read-only\"", "data_calculating": "Computing...", "data_error": "Data error", "data_loading": "Loading...", "datasheet": "Datasheet", "datasheet_1000_rows_limited_tips": "Your datasheet has the maximum number of records", "datasheet_choose_field_type": "Select field type", "datasheet_count": "Total File nodes", "datasheet_editor_label": "In addition to \"Update-only\", can also add or delete views and delete records", "datasheet_exist_widget": "The datasheet has installed these widgets:", "datasheet_experience_label": "Use the template first, then you can modify or write data", "datasheet_is_loading": "There is data being loaded currently, please wait to proceed.", "datasheet_limit": "The maximum number of file nodes to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "datasheet_limit_email_title": "Usage reminders for the number of nodes in the \"${SPACE_NAME}\" space", "datasheet_manager_label": "In addition to \"Editor\",  can also configure datasheet and fields", "datasheet_reach_limit": "The number of datasheets in the \"<a class=\"spaceName\"></a>\" Space has reached the limit.", "datasheet_reader_label": " You can only view the datasheet but cannot edit it.\n", "datasheet_record_limit": "The maximum number of records to be used in the \"<a class=\"nodeName\"></a>\" datasheet is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been created, and you can upgrade to get higher usage.", "datasheet_record_limit_email_title": "Usage reminders for the number of records in the \"${NODE_NAME}\" datasheet", "datasource_selector_search_placeholder": "Search", "datasource_selector_search_result_title": "Node", "date_after_or_equal": "is on or after…", "date_auto_enable_alarm": "Enable task reminder automatically", "date_auto_enable_alarm_setting": "Reminder settings", "date_auto_enable_alarm_tips": "Please save field settings after setting task reminder", "date_auto_enable_alarm_tooltip": "Enable task reminder automatically when you add or edit Date field", "date_before_or_equal": "is on or before…", "date_cell_input_tips": " Enter only numeric 、'/'、'-' in date cell\n", "date_day": "day", "date_functions": "date functions", "date_range": "custom range", "date_setting_time_zone_tooltips": "Changing the time zone will affect the display of the time, thereby affecting the time of the record that displayed in other views", "datetime_format": "Date format", "dating_back_to": "go back", "day": " day(s)", "day_month_year": "day/month/year", "db_click_to_edit_field_desc": "Double-click to edit the field description", "debug_cell_text_1": "*", "decimal": "decimal (1.0)", "default": "<PERSON><PERSON><PERSON>", "default_create_ai_chat_bot": "New AI agent", "default_create_automation": "New automation", "default_create_custom_page": "New Custom Page", "default_create_dashboard": "New dashboard", "default_create_datasheet": "New datasheet", "default_create_file": "New file node", "default_create_folder": "New folder", "default_create_form": "New form", "default_create_mirror": "New mirror", "default_datasheet_attachments": "Attachments", "default_datasheet_options": "Options", "default_datasheet_title": "Title", "default_file_copy": "${file_name} copy", "default_invitation_code_tip": "You and your inviter will both get 1000 V coins", "default_link_join_tip": "Members who join the space through the invite link will be granted \"update-only\" access to the node", "default_picture": "Default picture", "default_theme": "Light", "default_value": "<PERSON><PERSON><PERSON>", "default_view": "Grid view", "del_field_content": "Are you sure you want to delete the \"${fieldname}\" field?", "del_field_tip": "Delete field", "del_invitation_link": "Delete invitation link", "del_invitation_link_desc": "The link will be invalid after deletion", "del_space_now": "Delete Space forever", "del_space_now_tip": "The Space can't be restored after deletion. All file nodes and attachments will be deleted.", "del_space_res_tip": "The Space deleted", "del_team_success": "Delete Team Success", "del_view_content": "Are you sure you want to delete the ${view_name} view?", "delete": "Delete", "delete_archive_record_success": "Successfully deleted archived record", "delete_archived_records_warning_description": "Record(s) will be permanently deleted and can't be restored to datasheet. Do you want to continue?", "delete_comment_tip_content": "The comment can't be restored after deletion. Continue?", "delete_comment_tip_title": "Delete comment", "delete_completey": "Delete forever", "delete_completey_fail": "Failed to delete file node (folder) forever", "delete_field": "Delete field", "delete_field_success": "Field deleted", "delete_field_tips_content": "Are you sure you want to delete the \"${field_title}\" field?", "delete_field_tips_title": "Delete field", "delete_file_message_content": "The file node has been deleted and you can no longer access it.", "delete_kanban_group": "Delete group", "delete_kanban_tip_content": "All records in this group will be moved to the uncategorized group", "delete_kanban_tip_title": "Delete group", "delete_n_columns": "Delete ${count} fields", "delete_now": "Delete now", "delete_person": "Operator", "delete_record": "Delete this Record", "delete_records_count": "Delete ${count} records", "delete_role_member_content": "After confirmation, the member will be removed from the current role", "delete_role_member_success": "Removed successfully", "delete_role_member_title": "Remove", "delete_role_success_message": "Role deleted successfully", "delete_role_warning_content": "You need to move all members in this role out of the teams before deleting", "delete_role_warning_title": "Cannot delete", "delete_row": "Delete record", "delete_row_count": "Delete selected ${count} records", "delete_sort": "Delete the sort condition", "delete_space": "Delete space", "delete_sub_admin_fail": "Failed to delete sub-admin", "delete_sub_admin_success": "Sub-admin deleted", "delete_succeed": "Deleted", "delete_team": "Delete team", "delete_team_fail": "Failed to delete team", "delete_template_content": "The template will be deleted.", "delete_template_title": "Delete this template", "delete_view": "Delete view", "delete_view_success": "View deleted", "delete_widget_content": "The widget can't be restored after deletion. Continue?", "delete_widget_panel_content": "All the widgets in the widget board will be deleted and can't be restored. Continue?", "delete_widget_panel_title": "Delete widget board", "delete_widget_title": "Delete widget", "delete_workspace_succeed": "Space deleted", "deleted_in_curspace_tip": "You are removed by the admin and can no longer access the Space.", "democratic_republic_of_the_congo": "Democratic Republic of the Congo", "denmark": "Denmark", "desc_sort": "Ascending", "description": "Description", "description_save_error": "A network error has occurred, this time it was not saved successfully and you need to save it again.", "deselect": "Deselect", "design_chart_structure": "Design chart structure", "design_chart_style": "More settings", "dev_tools_opening_tip": "Continue clicking ${count} times to open the debugger", "developer_configuration": "Developer", "developer_token": "Developer token", "developer_token_placeholder": "Click \"+\" to generate the token ", "devtool_apply_backup_data": "Apply Backup Data", "devtool_batch_delete_node": "Batch Delete Node", "devtool_more": "more", "devtool_open_eruda": "Open Eruda", "devtool_test_functions": "Test Functions", "dingding_bind": "Please use the DINGTALK scanning  to bind", "dingding_login": "Log in with DingTalk account", "dingtalk": "DingTalk", "dingtalk_activity_upgrade_guidance": "Want to upgrade or renew your plan?<br/>\nPlease go to DingTalk App Store to make a subscription. You also can contact our customer service staff if you need help.", "dingtalk_admin_contact_syncing_tips": "Your organization's contacts are being synchronized, please wait a moment. After the synchronization is done, all members will be notified to use Table, and then they can enter the Space. During the synchronization, please do not adjust the visible range of the application to avoid aborting synchronization.", "dingtalk_admin_panel_message": "DingTalk organization administrators can manage the application's spaces here", "dingtalk_admin_panel_title": "DingTalk space admin panel", "dingtalk_app_desc": "<p>After installing the integration, please complete the space bundle, and after bundling, your team can use Table on DingTalk.</p>\n\n<p>With Table Assistant on DingTalk, you can:</p>\n\n<ul><li>Access Table's space and access space data directly on DingTalk without logging in;</li><li>Receive notifications from the space through DingTalk;</li><li>Set app visibility on DingTalk and automatically sync DingTalk's organization structure;</li></ul>", "dingtalk_app_intro": "Allow you to log in to Table though DingTalk, receive your Space's notifications in DingTalk, and sync organizational chart from DingTalk", "dingtalk_app_notice": "* Note: One Space can only bind one DingTalk self-built application", "dingtalk_base": "Basic Plan with DingTalk", "dingtalk_basic": "Basic Plan with DingTalk", "dingtalk_bind_space_config_detail": "Consistent with the usable range of DingTalk (${maxcount} members)", "dingtalk_bind_space_tips": "The operation will firstly remove all existing members and teams of the \"${spaceName}\" Space, and then get the information from the organizational structure of the DingTalk enterprise. Please inform the admin and be prepared.", "dingtalk_change_admin_reject_msg": "This Space's contacts are managed by the DingTalk integration. Changing admin is unavailable at the moment.", "dingtalk_change_admin_reject_tips": "This member is out of the available range of the application. After modifying it, you can transfer the Space. <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-dingtalk#frequently-asked-questions' target=\\\"_blank\\\">How to modify it?</a>", "dingtalk_da": "DingTalk Apps", "dingtalk_da_from": "Dolphin Official Template Center", "dingtalk_enterprise": "Ultimate Plan with DingTalk", "dingtalk_grade_desc": "Spaces have three kinds of billing plans: Basic Plan with DingTalk(permanently free), Standard Plan with DingTalk \n,Profession Plan with DingTalk and Enterprise Plan with DingTalk. Members in the Spaces of different billing plans enjoy different rights and benefits.", "dingtalk_isv_integration_single_record_comment_mentioned": "b4ac28623fd5493dbc5f995bb1cc17c7", "dingtalk_isv_integration_single_record_member_mention": "5994e97c8766482bb971e6253ad7f6f1", "dingtalk_isv_integration_social_task_reminder": "3587b948d6484319979e00d14353706f", "dingtalk_isv_integration_subscribed_record_cell_updated": "f33cf06b321a4348ba5063355b125184", "dingtalk_isv_integration_subscribed_record_commented": "596ad27391d74630b7df029f940bb887", "dingtalk_isv_production_single_record_comment_mentioned": "8f41773b6eb54d6bba9d82392956dea1", "dingtalk_isv_production_single_record_member_mention": "dc79d4fb0f554ff0af08af4c1f04caa6", "dingtalk_isv_production_subscribed_record_cell_updated": "", "dingtalk_isv_production_subscribed_record_commented": "", "dingtalk_isv_production_task_reminder": "383ba579062a4f25a5d933201f466c48", "dingtalk_isv_staging_single_record_comment_mentioned": "d81ea943488b41ebb42fdeacfb42d0ef", "dingtalk_isv_staging_single_record_member_mention": "76a69635e123402db90f7f2cfbb496b9", "dingtalk_isv_staging_subscribed_record_cell_updated": "", "dingtalk_isv_staging_subscribed_record_commented": "d81ea943488b41ebb42fdeacfb42d0ef", "dingtalk_isv_staging_task_reminder": "", "dingtalk_isv_test_single_record_member_mention": "", "dingtalk_isv_test_social_task_reminder": "", "dingtalk_isv_test_subscribed_record_cell_updated": "", "dingtalk_isv_test_subscribed_record_commented": "", "dingtalk_login_fail_tips": "<PERSON><PERSON> failed, please contact us", "dingtalk_member_contact_syncing_tips": "Your organization's contacts are being synchronized, please wait a moment.\n<br/>\nAfter the synchronization is done, you will be notified to use Table, and then you can enter the Space.", "dingtalk_org_manage_reject_msg": "This Space's contacts are managed by the DingTalk integration. Please go to the DingTalk admin backend to invite members. <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-dingtalk#frequently-asked-questions' target=\\\"_blank\\\">How to invite？</a>", "dingtalk_profession": "Pro Plan with DingTalk", "dingtalk_single_record_member_comment_title": "", "dingtalk_single_record_member_mention_title": "🔔 You're mentioned in a record", "dingtalk_social_deactivate_tip": "If you need to disable the application, please go to DingTalk Admin Panel -> Applications -> 表格.", "dingtalk_space_list_item_tag_info": "DingTalk binding", "dingtalk_standard": "Standard Plan with DingTalk", "dingtalk_sync_address_modal_content": "After manually synchronizing the address book, you need to reassign administrators, file node permissions and column permissions for members and departments. Please know", "dingtalk_tenant_not_exist_tips": "The enterprise didn't authorized, please contact the administrator", "direction_above": "above", "direction_below": "below", "direction_left": "left", "direction_right": "right", "disable": "Disable", "disabled_apply_join_space": "Prohibit apply to join the space", "disabled_crypto_field": "Permissions are set for the current field, and the configuration cannot take effect", "disabled_expand_link_record": "You don't have the permission to view the related datasheet, you can't expand the record", "disabled_file_shared": "Public link sharing disabled", "disabled_file_shared_desc": "The admin has disabled the public link. All public links will be invalid.", "disabled_link_subtitle": "The sharer has no permission to continue sharing, so the public link will be invalid", "disagree_and_exit": "Cancel & Exit", "discard_changes": "Discard", "disconnect_from_the_server": "Can't connect to the server", "discount_amount": "Discount amount", "discount_price_deadline": "As of the date", "display_member_by_count": "${memberCount} members", "display_person_count": "${count} members", "display_success_and_error_count": " ${successCount} items imported; ${errorCount} items failed.", "distribute_a_team": "Assign to team", "divider": "Divider", "djibouti": "Djibouti", "do_not_bind": "Not now", "document_detail": "Documentation", "does_not_contains": "does not contain...", "dominica": "Dominica", "dominican_republic": "Dominican Republic", "donut_chart": "Donut Chart", "double_11_activity": "Double 11 Activity", "down": "Move to the cell below", "downgrade": "Downgrade", "download": "Download", "download_all": "Download all", "download_client": "Download app", "download_image": "Download image", "download_log": "Download log", "downloading_attachments": "Downloading attachment resources", "duplicate": "Duplicate", "duplicate_cell_data": "Copy cell(s)", "duplicate_datasheet": "Duplicate", "duplicate_field": "Duplicate field", "duplicate_record": "Duplicate record", "e_commerce": "e Commerce", "e_commerce_operations": "e-Commerce Operations", "early_bird": "Early Bird", "ecuador": "Ecuador", "edit": "Edit", "edit_cell_data": "Activate cell to start editing", "edit_field_name": "Edit field name", "edit_member": "Edit member", "edit_member_add_button": "Add", "edit_member_email": "Email address", "edit_member_fail": "Failed to edit member", "edit_member_name": "Name", "edit_member_success": "Edit Member Success", "edit_member_team": "Team", "edit_node_desc": "Add a description", "edit_selected_field": "Reselect", "edit_space_name": "Edit space name", "edit_sub_admin_fail": "Failed to edit sub-admin", "edit_sub_admin_success": "Sub-admin edited successfully", "editing_field_desc": "Edit field description", "editing_group": "Edit group", "editor_placeholder": "Type \"/\" to start", "education": "Education", "egypt": "Egypt", "el_salvador": "El Salvador", "email": "Email", "email_bound": "You have bound your email", "email_err": "Invalid email", "email_invite": "Invite via email", "email_placeholder": "Enter email address", "email_verify_warning_button_back": "Logout", "email_verify_warning_button_resend": "Resend email", "email_verify_warning_desc": "A message with a confirmation link has been sent to ${email_address}. Please follow the link to activate your account.", "email_verify_warning_title": "Please confirm your email", "embed_error_page_help": "Learn more", "embed_fail_og_description_content": "The public link for this embed has been disabled and is temporarily unavailable", "embed_failed": "Embed link is unavailable, ", "embed_link_bilibili": "bilibili", "embed_link_bilibili_desc": "By embedding the bilibili videos, you can watch tutorials, and guides, or view the channel homepage in Dolphin.", "embed_link_bilibili_link_text": "How to embed the bilibili video", "embed_link_bilibili_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-bilibili-video", "embed_link_default": "Anything", "embed_link_default_desc": "Paste a link to view any website. ", "embed_link_default_link_text": "Learn more", "embed_link_default_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page", "embed_link_figma": "Figma", "embed_link_figma_desc": "By embedding Figma files, members can view and edit design drafts more conveniently, improving collaboration efficiency. ", "embed_link_figma_link_text": "How to embed Figma files", "embed_link_figma_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-a-figma-file", "embed_link_google_docs": "Docs", "embed_link_google_docs_desc": "By embedding Google Docs, you can edit and view documents in AITable to facilitate team collaboration.", "embed_link_google_docs_link_text": "How to embed Google Docs", "embed_link_google_docs_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-a-google-docs-file", "embed_link_google_sheets": "Sheets", "embed_link_google_sheets_desc": "By embedding Google Sheets, you can edit and view tables in AITable to facilitate team collaboration. ", "embed_link_google_sheets_link_text": "How to embed Google Sheets", "embed_link_google_sheets_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-a-google-sheets-file", "embed_link_jishi_design": "JSdesign", "embed_link_jishi_design_desc": "By embedding JSdesign files, members can view and edit design drafts more conveniently, improving collaboration efficiency. ", "embed_link_jishi_design_link_text": "How to embed JSdesign files", "embed_link_jishi_design_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-js-design-file", "embed_link_tencent_docs": "Tencent Docs", "embed_link_tencent_docs_desc": "By embedding Tencent Docs, you can edit and view Tencent docs in Dolphin to improve collaboration efficiency. ", "embed_link_tencent_docs_link_text": "How to embed Tencent Docs", "embed_link_tencent_docs_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-tencent-docs", "embed_link_wps": "WPS", "embed_link_wps_desc": "By embedding WPS files, you can edit and view WPS documents, tables, and forms in Dolphin to improve collaboration efficiency.", "embed_link_wps_link_text": "How to embed WPS files", "embed_link_wps_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-wps-file", "embed_link_youtube": "YouTube", "embed_link_youtube_desc": "By embedding YouTube videos, you can watch tutorials, and guides, or view the channel homepage in AITable. ", "embed_link_youtube_link_text": "How to Embed YouTube Videos", "embed_link_youtube_link_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page#how-to-add-a-youtube-video", "embed_page": "Custom Page", "embed_page_add_url": "Add Url", "embed_page_doc_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-custom-page", "embed_page_function_desc": "Add web pages into ${edition} for easy access to third-party website documents, videos, and more. You can add recommended website links or any custom links.", "embed_page_node_permission_editor": "On the basis of \"update only\", can also open the public sharing of the file node", "embed_page_node_permission_manager": "Can perform all actions on the file node", "embed_page_node_permission_reader": "Can view the content of the custom page and basic file node information", "embed_page_node_permission_updater": "On the basis of \"read-only\", can also modify the link of the custom page", "embed_page_url_invalid": "Please enter the correct URL", "embed_paste_link_bilibili_placeholder": "Paste the bilibili video link", "embed_paste_link_default_placeholder": "Paste the URL", "embed_paste_link_figma_placeholder": "Paste the Figma file’s share link", "embed_paste_link_google_docs_placeholder": "Paste the Google Docs share link", "embed_paste_link_google_sheets_placeholder": "Paste the Google Sheets share link", "embed_paste_link_jsdesign_placeholder": "Paste the JSdesign file’s share link", "embed_paste_link_tencent_docs_placeholder": "Paste the Tencent Docs share link", "embed_paste_link_wps_placeholder": "Paste the WPS file’s share link", "embed_paste_link_youtube_placeholder": "Paste the YouTube video link", "embed_success": "Add Successfully", "emoji_activity": "Activities", "emoji_custom": "Custom", "emoji_flags": "Flags", "emoji_foods": "Food and drink", "emoji_nature": "Animals and nature", "emoji_not_found": "Emoji not found", "emoji_objects": "Objects", "emoji_people": "People", "emoji_places": "Travel", "emoji_recent": "Recently used", "emoji_search_result": "Search results", "emoji_smileys": "<PERSON><PERSON><PERSON>", "emoji_symbols": "Symbols", "empty": "Empty", "empty_dashboard_list": "No dashboards. ${action}", "empty_data": "Empty data", "empty_datasheet": "New datasheet", "empty_email_tip": "No results, invite a new member via email", "empty_nodes": "Empty", "empty_record": "No records", "empty_trash": "No file nodes were deleted in the past ${day} days", "enable": "Enable", "enabled_view_lock_success": "View Locked", "enabled_view_lock_tip": "Once the view is locked, it can't be edited until a member who can manage the datasheet unlocks it", "encounter_problems": "Having problems?", "encounter_problems_message": "Let me help you", "end": "The end", "end_day": "days", "end_time": "End time", "enjoy": "Stay tuned", "ensure": "Confirm", "enter_names_or_emails": "Enter names or emails", "enter_official_website": "Website", "enter_template_name": "Name the template", "enter_unactive_space_err_message": "Your space has reached the maximum of 10 spaces. This space will be in the inactive state. The space in this state is temporarily inaccessible.", "enter_workspace_name": "Name your Space", "entered_a_valid_redemption_code": "${amount} V coins redeemed successfully", "entered_a_valid_redemption_code_info": "Note: V coins are users' points in the platform, used to purchase services and contents. <a href=\"http://help.dolphintable.suanlihaiyang.com/docs/about-v-coin\">Learn more</a>", "entered_the_wrong_redemption_code": "Enter an 8-digit redeem code", "enterprise": "Enterprise", "enterprise_edition": "Enterprise", "enterprise_third_app": "Ultimate", "entrepreneurship": "Entrepreneurship", "entry_space": "Enter Space", "equal": "is...", "equatorial_guinea": "Equatorial Guinea", "eritrea": "Eritrea", "err_field_group_tip": "Error found in field settings. Grouping does not work temporarily.", "err_filter_field": "Can't add this field as a filter due to field configuration errors", "error": "Error", "error_add_row_failed_wrong_length_of_value": "Records parameter not specified", "error_an_unsynchronized_changeset_is_detected": "localStorage detects unsynchronized changeset. Trying to synchronize...", "error_atta_type": "No support for uploading attachments in this format", "error_boundary_back": "Return to Space", "error_boundary_crashed": "Sorry, something went wrong and the page crashed. We will fix it soon.", "error_code": "Error code ${code}: ${message}", "error_configuration_and_invalid_filter_option": "A configuration error exists in this field. <PERSON><PERSON> failed.", "error_create_view_failed_duplicate_view_id": "Fail to create view: repetitive view ID detected.", "error_data_consistency_and_check_the_snapshot": "An error occurred in data consistency. Please check the snapshot.", "error_del_view_failed_not_found_target": "Failed to delete view: target to be deleted unfound.", "error_detail": "Details of error", "error_email_empty": "Enter email address", "error_field_not_exist": "Field does not exist.", "error_filter_failed_wrong_target_view": "Filtering failed: targeted view went wrong", "error_get_wecom_identity": "Unable to obtain wecom identity", "error_get_wecom_identity_tips": "Please check whether the application is deactivated or the account is moved out of the visible range of the application", "error_get_wecom_identity_tips_bound": "Wig account has been bound to other wecom accounts", "error_group_failed_the_column_not_exist": "Grouping failed: targeted column does not exist.", "error_group_failed_wrong_target_view": "Grouping failed: targeted view went wrong.", "error_integration_app_wecom_bind": "Binding failed. Please check whether the account is within the visible range of the application", "error_local_changeset_is_null_while_status_is_pending": "中英文都没看懂", "error_modify_cell_failed_unmatched_data_type": "Failed to modify cells: field type does not match.", "error_modify_column_failed_column_not_exist": "Failed to adjust column properties: column doesn't exist.", "error_modify_column_failed_wrong_target_view": "Failed to adjust column properties: targeted view error.", "error_modify_view_failed_duplicate_name": "Failed to modify view: duplicate view name\n", "error_modify_view_failed_not_found_target": "Failed to modify view: targeted view unfound.", "error_move_column_failed_invalid_params": "Failed to move the column: parameter error.", "error_move_row_failed_invalid_params": "Failed to move record(s) due to parameter errors", "error_move_view_failed_not_found_target": "Failed to move view: targeted view unfound.", "error_not_exist_id": "The column of id named ${id} does not exist, please verify the data.", "error_not_found_the_source_of_view": "Source view unfound.", "error_not_initialized_datasheet_instance": "The instance of operating datasheet has not initialized.", "error_occurred_while_requesting_the_missing_version": "An error occurred while requesting the missing version.", "error_page_feedback_text": "👉  Report issues", "error_please_bind_message_after_connected": "Please establish the connection before binding the message.", "error_please_close_sharing_page": "Sharing disabled", "error_record_not_exist_now": "The record you're editing is deleted or hidden.", "error_revision_does_not_exist": "Revision does not exist", "error_scan_qrcode_tips": "Please check whether the application is deactivated", "error_set_column_failed_bad_property": "Failed to set the properties of the column:  invalidate column property", "error_set_column_failed_duplicate_column_name": "Failed to edit the field settings due to duplicate field names", "error_set_column_failed_no_support_unknown_column": "Failed to set the properties of the column: modification of unknown column is not recommended.", "error_set_row_height_failed_wrong_target_view": "Failed to set row height due to targeted view errors", "error_something_wrong": "Error occurred", "error_sorted_failed_the_field_not_exist": "Failed to sort: field operated does not exist", "error_sorted_failed_wrong_target_view": "Failed to sort: targeted view error", "error_the_field_dragged_has_been_deleted_or_hidden": "The field you're dragging is deleted or hidden", "error_the_length_of_changeset_is_inconsistent": "The obtained ChangesetLength revisions are inconsistent", "error_the_version_is_inconsistent_while_preparing_to_merge": "Inconsistent data versions of related tables to be merged", "error_wrong_conjunction_type": "Wrong conjunction type: ${conjunction}", "error_wrong_data_in_current_column": "Wrong field data", "escape": "Exit", "essential_features": "Essentials", "estonia": "Estonia", "ethiopia": "Ethiopia", "event_planning": "Event Planning", "every": "Every", "every_day_at": "day(s)", "every_hour_at": "hour(s)", "every_month_at": "month(s)", "every_week_at": "Every Week on", "everyday_life": "Everyday Life", "everyone_visible": "Visible for Everyone", "exact_date": "exact date", "example_value": "Example value", "excel": "As .xlsx format", "exception_form_foreign_datasheet_not_exist": "The datasheet for form mapping does not exist", "exception_network_exception": "Network anomaly", "exchange": "Redeem", "exchange_code_times_tip": "Note: The redeem code can be used once only", "exclusive_consultant": "Exclusive V+ consultant", "exclusive_limit_plan_desc": "Exclusive Limited Tier", "exist_experience": "Exit experience", "exits_space": "Exit space", "expand": "Expand", "expand_activity": "Expand activity", "expand_all_field_desc": "Show field description", "expand_all_group": "Expand all groups", "expand_current_record": "Expand record (Space)", "expand_pane": "Expand panel", "expand_record": "Expand record", "expand_record_attachment_empty": "expand record attachment empty\n", "expand_record_vision_btn_tooltip_center": "Centered display", "expand_record_vision_btn_tooltip_full_screen": "Full screen", "expand_record_vision_btn_tooltip_side": "Side display", "expand_record_vision_setting": "Style of the record card", "expand_record_vision_setting_center": "Center", "expand_record_vision_setting_side": "Side", "expand_rest_records_by_count": "Expand the remaining ${record_count} records", "expand_subgroup": "Expand group/subgroup", "experience_test_function": "Experimenting ${testFunctions}", "expiration": " Expiration date: ${date}", "expiration_time": "Deletion date", "expiration_time_of_space": "Expiration", "expire": "End", "expired": "Expired", "export": "Exporting...", "export_brand_desc": "Powered by", "export_current_preview_view_data": "Export current preview view data", "export_gantt_button_tips": "Export to a .png file", "export_gantt_chart": "Export the gantt chart", "export_to_excel": "Export data", "export_view_data": "Export view data", "export_view_image_warning": "Please click to switch to the view and export again", "extra_tip": "Special tips", "fail": "Failed", "failed_in_file_parsing": "File parsing error", "failed_list": "Failed List", "failed_list_file_download": "Download the failed list", "faq": "FAQs", "faroe_islands": "Faroe Islands", "fashion_and_style": "Fashion and style", "favorite": "<PERSON>n", "favorite_empty_tip1": "Pin file nodes here for quick access.", "favorite_empty_tip2": "Pinned file nodes are only visible to you", "fee_unit": "yuan", "feedback": "<PERSON><PERSON><PERSON>", "feishu_activity_upgrade_guidance": "", "feishu_admin_login_btn": "Bind", "feishu_admin_login_err_message": "You have not signed up yet. Please sign up in http://Dolphin.cn and try again.", "feishu_admin_login_err_to_register": "Sign up now", "feishu_admin_login_title": "Bind existing Table account", "feishu_admin_panel_message": "Space station for feishu enterprise administrator management application", "feishu_admin_panel_title": "Feishu space station management panel", "feishu_base": "Basic Plan with Lark", "feishu_bind_space_btn": "Bind the Space", "feishu_bind_space_config_detail": "Consistent with the availability of Lark (${maxcount} members)", "feishu_bind_space_config_title": "Configure availability status", "feishu_bind_space_err": "Your Space can have a maximum of ${count} members. Contact us if you want to invite more. <a href='http://help.dolphintable.suanlihaiyang.com/docs/how-contact-service' target=\"_blank\">", "feishu_bind_space_need_upgrade": "The maximum number of members of the selected Space is ${maxSeat}, company in Lark that has ${maxCount} people can't be synchronized. Please submit a request for expanding the amount of member.", "feishu_bind_space_select_title": "Select a Space to bind", "feishu_bind_space_tips": "The operation will firstly remove all existing members and teams of the \"${spaceName}\" Space, and then get the information from the organizational structure of the Lark enterprise. Please inform the admin and be prepared.", "feishu_bind_user_subTitle": "Bind an existing account or get a new one", "feishu_bind_user_title": "Bind Table account", "feishu_configure_change_space_master_modal_title": "Confirm to change admin", "feishu_configure_err_of_bound": "Other admins have bound with the Space. Click the button below to enter the Space.", "feishu_configure_err_of_configuring": "Please try again later. The admin is still configuring the application.", "feishu_configure_err_of_identity": "Note: Non-enterprise admins can't bind the Space.", "feishu_configure_err_of_select_valid": "不确认“登录身份”的意思", "feishu_configure_of_authorize_err": "Failed to authorize login. Please confirm whether you are within the scope of application", "feishu_configure_of_idetiity_err": "Illegal operation. You are not the application administrator of the current enterprise", "feishu_disable_upgrade_in_mobile": "You can't do the upgrade on mobile, please move to PC to do the upgrade", "feishu_enterprise": "Ultimate Plan with Lark", "feishu_grade_desc": "Spaces have three kinds of billing plans: Basic Plan with Lark(permanently free), Standard Plan with Lark ,Profession Plan with Lark and Ultimate Plan with Lark. Members in the Spaces of different billing plans enjoy different rights and benefits.", "feishu_manage_address_reject_msg": "This Space's contacts are managed by the Lark integration. Please go to the Lark admin backend to edit contacts.", "feishu_manage_close_btn": "Turn off", "feishu_manage_open_btn": "Turn on for using", "feishu_manage_subTitle": "To use Table assistance on Lark, you can:", "feishu_manage_title": "With this app, you can let your team use Dolphin table onfeishu, which is a team instant messaging software launched by ByteDance company.\nDuring \"Public beta\": After download the application, please bind the Space so as to let your team use Table on Lark.", "feishu_manage_useage": "<ul> < li > no need to log in, you can directly enter the Space station to access the data of the Space; < / Li > < li > receive notification from the Space through the Lark; < / Li > < li > set the application visible range in the Lark, and automatically synchronize the organizational structure of the Lark. </li><li>Special note: A Space can only be bound to one Lark enterprise.</li></ul>", "feishu_profession": "Pro Plan with Lark", "feishu_space_list_item_tag_info": "Lark binding", "feishu_standard": "Standard Plan with Lark", "feishu_upgrade_guidance": "Please contact your Lark administrator to go to the Lark application backend to pay for the upgrade", "field": "Field", "field_circular_err": "The selected field will cause a circular reference. Please correct.", "field_configuration_err": "There is a configuration error in this field. Please check the formula or the configuration used by the field.", "field_configuration_numerical_value_format": "Format", "field_configuration_optional": "Optional", "field_created_by_property_subscription": "Creator auto-watch record", "field_created_by_property_subscription_close_tip": "After closing, the members under the creator column will automatically cancel the records they have followed", "field_created_by_property_subscription_open_tip": "This option only applies to newly created records", "field_desc": "Field description", "field_desc_attachment": "Add attachments such as documents, images, and videos, and preview or download them", "field_desc_autonumber": "Add a unique and self-increasing number for each record", "field_desc_button": "You can trigger automation or jump to a specified web by clicking a button", "field_desc_cascader": "Using a cascading approach, select pre-options.", "field_desc_checkbox": "Select a single checkbox, and quickly assign a status to a record", "field_desc_created_by": "Show the member who created the record, and this field can't be edited", "field_desc_created_time": "Show the date when the record was created, and this field can't be edited", "field_desc_currency": "Enter a number, and define its currency symbol and the layout", "field_desc_datetime": "Enter a date or pick one from the calendar, and define its format (such as Year/Month/Day) and whether to include time", "field_desc_denied": "Access denied for this field", "field_desc_email": "Enter an email address, and click on it to send an email", "field_desc_formula": "Use functions and operational characters to perform the data on the cells.", "field_desc_last_modified_by": "Show the member who edited any field or specified fields most recently, and this field can't be edited", "field_desc_last_modified_time": "Show the date when a record was most recently edited in any editable field or in specified editable fields, and this field can't be edited", "field_desc_length_exceeded": "Please keep the field description within 200 words", "field_desc_link": "Bidirectional link to record(s) in the same or another datasheet", "field_desc_lookup": "Look up data across datasheets and perform simple calculations using the data.", "field_desc_member": "Select members from the Space and optionally notify them ", "field_desc_multi_select": "Select one or more options from a predefined option list", "field_desc_number": "Enter a number, and define its precision and units of measurement", "field_desc_one_way_link": "Unidirectional link to record(s) in the same or another datasheet", "field_desc_percent": "Enter a number that will be formatted as a percent", "field_desc_phone": "Enter a phone number, and click on it to make a phone call", "field_desc_rating": "Add a rating with a custom symbol", "field_desc_single_select": "Select a single option from a predefined option list", "field_desc_single_text": "Enter short and unique texts in one single line", "field_desc_text": "Enter long texts that can span multiple lines", "field_desc_tree_select": "You can fill in the cells by selecting the content according to the hierarchy", "field_desc_url": "Enter a URL, and click on it to open the web page", "field_desc_workdoc": "Create a rich text field to preview and edit document", "field_display_time_zone": "Display time zone", "field_had_deleted": "This field was deleted", "field_head_setting": "Field head setting", "field_help_attachment": "https://help.aitable.ai/docs/manual-field-attachment", "field_help_autonumber": "https://help.aitable.ai/docs/manual-autonumber", "field_help_button": "https://help.aitable.ai/docs/manual-field-button", "field_help_cascader": "https://help.aitable.ai/docs/manual-field-cascader", "field_help_checkbox": "https://help.aitable.ai/docs/manual-field-checkbox", "field_help_created_by": "https://help.aitable.ai/docs/manual-createdby", "field_help_created_time": "https://help.aitable.ai/docs/manual-createdtime", "field_help_currency": "https://help.aitable.ai/docs/manual-field-currency", "field_help_datetime": "https://help.aitable.ai/docs/manual-field-datetime", "field_help_email": "https://help.aitable.ai/docs/manual-field-email", "field_help_formula": "https://help.aitable.ai/docs/manual-formula-field-overview", "field_help_last_modified_by": "https://help.aitable.ai/docs/manual-lastmodifiedby", "field_help_last_modified_time": "https://help.aitable.ai/docs/manual-lastmodifiedtime", "field_help_link": "https://help.aitable.ai/docs/manual-field-link", "field_help_lookup": "https://help.aitable.ai/docs/manual-field-lookup", "field_help_member": "https://help.aitable.ai/docs/manual-filed-member", "field_help_multi_select": "https://help.aitable.ai/docs/manual-field-select", "field_help_number": "https://help.aitable.ai/docs/manual-field-number", "field_help_one_way_link": "https://help.aitable.ai/docs/manual-field-link", "field_help_percent": "https://help.aitable.ai/docs/manual-field-percent", "field_help_phone": "https://help.aitable.ai/docs/manual-field-phone", "field_help_rating": "https://help.aitable.ai/docs/manual-field-rating", "field_help_single_select": "https://help.aitable.ai/docs/manual-field-select", "field_help_single_text": "https://help.aitable.ai/docs/manual-field-single-line-text", "field_help_text": "https://help.aitable.ai/docs/manual-field-text", "field_help_tree_select": "This is the help page of Cascade<PERSON>", "field_help_url": "https://help.aitable.ai/docs/manual-field-url", "field_help_workdoc": "https://help.aitable.ai/docs/manual-field-workdoc", "field_incluede_time_and_time_zone_title": "Include time and time zone", "field_map_tips_for_python": "A field name exists that does not match the variable rules, please turn on \"Use FieldId\" or the code example below may not work! \n[Field Mapping](https://github.com/apitable/apitable-sdks/tree/develop/apitable.py#field-mapping) can help you to solve this problem.", "field_member_property_multi": "Allow adding multiple members", "field_member_property_notify": "Notify members once they're selected", "field_member_property_notify_tip": "Due to the option of 'selected members will automatically follow updates to row data' being enabled, the members under this member column will automatically cancel the following records after closing it", "field_member_property_subscription": "Selected members auto-watch record", "field_member_property_subscription_close_tip": "After closing, the members under the member column will automatically cancel the records they have followed", "field_member_property_subscription_open_tip": "This option only applies to newly selected members", "field_name_formula": "Formula", "field_name_setting": "Field name setting", "field_permission": "Field permissions", "field_permission_add_editor": "Editor", "field_permission_add_reader": "Read-only", "field_permission_close": "Disable field permissions", "field_permission_edit_sub_label": "Can edit cells in the field", "field_permission_editor_lock_tips": "You have limited access to this field. You can edit cells in the field, but can't edit field settings or delete it.", "field_permission_form_sheet_accessable": "Allow populating the field through forms", "field_permission_help_desc": "See documentation", "field_permission_help_url": "https://help.aitable.ai/docs/manual-field-permission", "field_permission_lock_tips": "Permission is set for this field and some members cannot view this field", "field_permission_manager_lock_tips": "A limited number of members have access to the field. You can manage the field.", "field_permission_modal_tip": "Only the following members and teams can view the field", "field_permission_nums": "The maximum number of field permissions to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "field_permission_open": "Enable field permissions", "field_permission_open_tip": "Limit who can view or edit values in this field", "field_permission_open_warning": "Once enabled, if you add this field as the condition for filters, groups, or sorting, all these view settings will be invalid for members who can't view this field.", "field_permission_read_sub_label": "Can view the field only", "field_permission_reader_lock_tips": "A limited number of members have access to the field. You can only view the field.", "field_permission_role_valid": "Field permissions are invalid. This member or team has no permission, please add their permissions first.", "field_permission_switch_closed": "Field permissions disabled", "field_permission_switch_open": "Enabled field permissions", "field_permission_uneditable_tooltips": "You can't downgrade their field permission because they are the Space admin or they enabled the manual permission setting.", "field_permission_view_lock_tips": "This field is already set with permissions. Filters for members that do not have permission to access this field will be invalidated", "field_permisson_close_tip": "The specified field permissions will be cleared and cancelled", "field_range": "Specify fields", "field_required": "Please fill in this field", "field_select_modal_desc": "If any of the fields you select below are edited, the member who edited most recently will show in the last edited by field", "field_select_modal_title": "Select last edited fields", "field_select_time_zone_current": "Current time zone", "field_select_time_zone_other": "Other time zones", "field_set_you_by_user": "<a class=\"memberName\"></a> mentioned you in <a class=\"times\"></a> of the \"<a class=\"nodeName\"></a>\" datasheet.", "field_title": "Field name", "field_title_attachment": "Attachment", "field_title_autonumber": "Autonumber", "field_title_button": "<PERSON><PERSON>", "field_title_checkbox": "Checkbox", "field_title_created_by": "Created by ", "field_title_created_time": "Created time", "field_title_currency": "<PERSON><PERSON><PERSON><PERSON>", "field_title_datetime": "Date", "field_title_denied": "Denied", "field_title_email": "Email", "field_title_formula": "Formula", "field_title_last_modified_by": "Last edited by", "field_title_last_modified_time": "Last edited time", "field_title_link": "Two-way Link", "field_title_lookup": "Lookup", "field_title_member": "Member", "field_title_multi_select": "Multi-select", "field_title_number": "Number", "field_title_one_way_link": "One-way Link", "field_title_percent": "Percent", "field_title_phone": "Phone", "field_title_rating": "Rating", "field_title_single_select": "Select", "field_title_single_text": "Single line text", "field_title_text": "Long text", "field_title_tree_select": "<PERSON>r", "field_title_url": "URL", "field_title_workdoc": "WorkDoc", "field_type": "Field type", "field_type_attachment_select_cell": "Drop attachments here", "fiji": "Fiji", "file": "file node", "file_limits": "${limit} file node limits in one space", "file_name_with_bulk_download": "${fileName}, etc. ${count} files.zip", "file_notification": "Tips", "file_of_rest": "Remaining: ${nodeRest}", "file_sharing": "File node sharing processing...", "file_summary": "Description", "file_upper_bound": "Maximum: ${nodeMax}", "fill_in_completed": "Completed", "filter": "Filter", "filter_delete_tip": "The column selected by the filter is deleted", "filter_fields": "Add \"${field_name}\" as filter", "filter_help_url": "https://help.aitable.ai/docs/manual-filter", "filter_link_data": "Filter linked records", "filtering_conditions_setting": "Filter setting", "filters_amount": "${amount} filter(s)", "find": "Find", "find_next": "Next", "find_prev": "Previous", "finish": "Finish", "finish_editing_cell_left": "Finish editing and move to the left cell", "finish_editing_cell_right": "Finish editing and move to the right cell", "finland": "Finland", "first_bind_email": "First binding mailbox reward", "first_bind_email_msg": "Bind your email for the first time to win ${count} V-coin", "first_bind_phone": "First binding mailbox reward", "first_prize": "", "first_prize_name": "", "first_prize_number": "", "fission_reward": "\"Sharing Blessings\" reward from \"${name}\"", "folder": "Folder", "folder_banner_desc": "It is recommended to use pictures with a width larger than 800 px to achieve a better visual experience.", "folder_contains": "${folders} folders and ${files} file nodes", "folder_content_empty": "Empty folder", "folder_desc_title_placeholder": "Enter a title", "folder_editor_label": "In addition to \"Update-only\", can also edit and share file nodes", "folder_level_2_limit_tips": "Hey, the level of your folder has reached the limit of level-2.", "folder_manager_label": "In addition to \"Editor\", can also add or delete file nodes", "folder_permission": "File node permissions", "folder_reader_label": " You can only view the file nodes under this folder and can't edit them.", "folder_with_link_share_reminder": "The current folder is associated with external  datasheet. Do you want to continue sharing", "folder_with_link_share_view_reminder": "Note: The shared folder has datasheets that link to other datasheets in another folder", "folds_hidden_fields_by_count": "Collapse ${count} hidden field(s)", "follow_client_time_zone": "Client's time zone", "follow_system_time_zone": "Local time zone", "follow_up_guidelines": "Get a better experience by using APITable on computer.", "follow_user_time_zone": "User's time zone", "food_and_drink": "Food and drink", "for_each_person_every_day": "Per person per day", "foreign_filed": "Linked field", "form": "Form", "form_back_workspace": "Back to my Space", "form_brand_visible": "Show branding", "form_compact_option_desc": "Options for radio/multi-select fields will not be tiled on the page", "form_compact_option_mode": "Collapse options", "form_cover_crop_desc": "Support JPG, PNG, and GIF images under 5 MB", "form_cover_crop_tip": "Recommended size: 1440*480", "form_cover_img_desc": "A square image with an aspect ratio 3:1 is recommended for a better visual experience", "form_cover_visible": "Show cover", "form_desc_placeholder": "Enter a description", "form_editor_label": "In addition to \"Update-only\", can also share form", "form_empty_tip": "Please fill in the form first", "form_error_tip": "The form structure has been updated. Please refresh the page.", "form_field_add_btn": "Add", "form_fill_again": "Submit another response", "form_fill_anonymous": "Submit anonymously", "form_fill_anonymous_desc": "Allow submitting without login", "form_fill_listed": "Need login to submit", "form_fill_listed_desc": "Need to log in to submit the form", "form_fill_open_desc": "Publish a form link that everyone can see", "form_fill_setting": "Submit requirement", "form_full_screen": "Full screen width", "form_help_desc": "See documentation", "form_help_link": "https://help.aitable.ai/docs/magic-form", "form_index_visible": "Show index", "form_link_field_empty": "The referenced field has no content", "form_logo_visible": "Show logo", "form_manager_label": "In addition to \"Editor\", can also configure form", "form_network_error_tip": "Network disconnected. Please try again later.", "form_not_configure_options": "No options. Please add options in the original view.", "form_only_read_tip": "The form can't be filled because its folder is not editable", "form_reader_label": "Can read the form,not submit it", "form_setting": "Form settings", "form_share_closed_desc": "Public link", "form_share_closed_popconfirm_content": "After disabled, the public form link will be invalid", "form_share_closed_popconfirm_title": "Disable the public form link", "form_share_opened_desc": "Create public link", "form_share_title": "Collect data via the form", "form_source_text": "Collected data to", "form_space_capacity_over_limit": "The attachment capacity of space has reached the limit, and the form with attachment cannot be submitted", "form_submit": "Submit", "form_submit_anonymous_tooltip": "You can't change the settings in the \"Submit anonymously\" mode", "form_submit_fail": "Failed to submit", "form_submit_loading": "Submitting...", "form_submit_no_limit": "No limit", "form_submit_once": "Submit once only", "form_submit_success": "Submitted", "form_submit_times_limit": "Submit limit", "form_tab_setting": "Settings", "form_tab_share": "Share", "form_thank_text": "Thank you for your submission", "form_the_full": "Full context of the datasheet", "form_title_placeholder": "Enter a title", "form_to_datasheet_view": "Go to the original view", "form_tour_desc": "User guide", "form_tour_link": "https://help.aitable.ai/docs/magic-form", "form_updater_label": "Can read, can also submit form", "form_view": "Form", "form_view_desc": "Use a form to collect data. You can allow others to add new records in your datasheet by sharing forms.", "format": "Format", "format_date": "Format date", "formula_check_info": "The field value can't be edited because it's a formula result", "formula_example_desc": "To calculate, fill in variables, operational character, and function\n to form formulas.", "formula_example_sub_title": "Examples", "formula_example_title": "Sample formula", "formula_how_to_use": "Usage", "formula_learn_more": "More instructions", "formula_learn_more_url": "https://help.aitable.ai/docs/manual-summary-of-formula-functions-and-operators", "france": "France", "free": "Free", "free_edition": "Free", "free_subscription": "free subscription", "free_trial": "Start trial now", "free_update": "Upgrade for free", "freeze_click_when_windows_too_narrow": "Adjust column width", "freeze_column_reset": "Restore default frozen column", "freeze_current_column": "Freeze to this column", "freeze_line_tips": "You can't drag the frozen area. You can adjust the freezing range in field settings.", "freeze_tips_when_windows_too_narrow": "The current window is too small so the frozen area can't scroll", "freeze_tips_when_windows_too_narrow_in_gantt": "The task area is too small so the frozen area can't scroll", "freeze_warning_cant_freeze_field": "The current window is too small so the column can't be frozen\n", "french_guiana": "French Guiana", "french_polynesia": "French Polynesia", "fresh_dingtalk_org": "Sync contacts", "fresh_order_status_action": "Refresh Status", "friend": "Friend", "from_datasheet_associated": "Record from \"${datasheetName}\"", "from_select_link_column": "Look up a field in ${name}", "front_version_error_desc": "Detected that the system version has been updated, please refresh the page to use", "front_version_error_title": "Version Updated", "full_memory_tip": "Attachment uploading stopped processing because the capacity reaches its limit.", "full_screen": "Full screen", "function": "Actions", "function_abs_example": "ABS(-5)", "function_abs_summary": "Returns the absolute value.", "function_and_example": "AND(3>2, 4>3)\n=> true", "function_and_summary": "Returns true if all the arguments are true, returns false otherwise.", "function_arraycompact_example": "ARRAYCOMPACT([1,2,\"\",3,false,\" \", null])\n=> [1,2,3,false,\" \"]", "function_arraycompact_summary": "Removes empty strings and null values from the array. Keeps \"false\" and strings that contain one or more blank characters.", "function_arrayflatten_example": "ARRAYFLATTEN([1, 2, \" \", 3, ],[false])\n=> [1, 2, 3 ,false]", "function_arrayflatten_summary": "Flattens the array by removing any array nesting. All items become elements of a single array.", "function_arrayjoin_example": "ARRAYJOIN(values, \"; \") ", "function_arrayjoin_summary": "Join the array of lookup items into a string with a separator.", "function_arrayunique_example": "ARRAYUNIQUE([1,2,3,3,1])\n=> \"[1,2,3]\"", "function_arrayunique_summary": "Returns only unique items in the array.", "function_associate_sheet": "Records from ${datasheetname}", "function_average_example": "AVERAGE(2, 4, \"6\", \"八\")\n=>(2 + 4 + 6) /4 =3", "function_average_summary": "Returns the average of the numbers.", "function_blank_example": "IF(Date = BLANK(), \"Please enter date\", \"Date entered\")", "function_blank_summary": "Returns a blank value.", "function_ceiling_example": "CEILING(1.01, 0.1) \n=> 1.1\n\nCEILING(-1.99, 0.1)\n=> -1.9", "function_ceiling_summary": "Returns the nearest integer multiple of significance that is greater than or equal to the value. If no significance is provided, a significance of 1 is assumed.", "function_concatenate_example": "CONCATENATE(Name, \" - \", Age)", "function_concatenate_summary": "Joins together the text arguments into a single text value. To concatenate static text, surround it with double quotation marks. To concatenate double quotation marks, you need to use a backslash (\\) as an escape character.\nEquivalent to using the & operator.", "function_content_empty": "The formula can't be empty", "function_count_example": "COUNT(1, 3, 5, \"\", \"seven\")\n=> 3", "function_count_summary": "Returns the number of numeric items", "function_counta_example": "COUNTA(1, 3, 5, \"\", \"seven\")\n=> 4", "function_counta_summary": "Returns the number of non-empty values. This function counts both numeric and text values.", "function_countall_example": "COUNTALL(1, 3, 5, \"\", \"seven\")\n=> 5", "function_countall_summary": "Returns the number of all elements including text and blanks.", "function_countif_example": "COUNTIF({rating}, \"A\") => 2 \n// where {Ratings} is a \"magic quote\" type of dimension column, and the data format is an array. \n\nCOUNTIF({Score}, 3, \">\") => 2 \n// where {score} is a \"magic quote\" type of dimensional column, and the data format is array. \n\nCOUNTIF({smoothie}, \"grapes\") => 2\n// where {jingle} is a dimension column of type \"text\", and the data format is text string.", "function_countif_summary": "Count the number of keyword occurrences in values. \n\nValues: specify where to find the data from. Supports data of array type or text type. \nKeyword: the keyword to be looked up and counted. \nOperation: comparator, not required. You can fill in the condition symbols greater than \">\", less than \"<\", equal to \"=\", not equal to \"! =\", if not filled in the default is equal to. \nExample 1 does not fill in the comparison character, the default count is equal to the number of times the value of \"A\" appears. \nExample 2 is filled with the comparator \">\", which means that the number of occurrences of values greater than \"2\" is counted. \n\nUsage scenarios. \n1) You can count the number of occurrences of the character \"A\" in a string of text arrays [A, B, C, D, A] as 2, see example 1. \n2) You can count the number of numbers greater than 3 in an array of numbers [1, 2, 3, 4, 5] as 2, see example 2. \n3) You can count the number of occurrences of \"grape\" in a string of text \"eat grapes without spitting out the skin\" as 2, see example 3.", "function_created_time_example": "CREATED_TIME()\n=> \"2020-06-10 5:55\"\n\n\"Created time:\" & CREATED_TIME()", "function_created_time_summary": "Returns the creation time of the current record.", "function_current_sheet": "Records from ${datasheetName}", "function_date_time_after": "is after...", "function_date_time_before": "is before...", "function_dateadd_example": "DATEADD({date of begin}, 10, \"days\")", "function_dateadd_summary": "Adds specified \"count\" units to a datetime.", "function_dateadd_url": "https://help.aitable.ai/docs/manual-supported-unit-specifiers-for-date-function", "function_datestr_example": "DATESTR({date of begin})\n=> 2020-06-10", "function_datestr_summary": "Formats a datetime into a string (YYYY-MM-DD).", "function_datetime_diff_example": "DATETIME_DIFF( TODAY(), {date of begin},  \"days\")\n=> 15", "function_datetime_diff_summary": "Returns the difference between datetimes in specified units. Default units are seconds.\nThe difference between datetimes is determined by subtracting [date2] from [date1]. This means that if [date2] is later than [date1], the resulting value will be negative.", "function_datetime_diff_url": "https://help.aitable.ai/docs/manual-supported-unit-specifiers-for-date-function", "function_datetime_format_example": "DATETIME_FORMAT(TODAY(), \"DD-MM-YYYY\")\n=> 10-06-2020", "function_datetime_format_summary": "Formats a datetime into a specified string. For a list of supported format specifiers, please click here.", "function_datetime_format_url": "https://help.aitable.ai/docs/datetime-format-specified-output-format", "function_datetime_parse_example": "DATETIME_PARSE(\"10 Jan 2020 18:00\", \"D MMM YYYY HH:mm\")\n=> \"2020/01/10 06:00\"", "function_datetime_parse_summary": "Interprets a text string as a structured date, with optional input format and locale parameters. The output format will always be formatted 'M/D/YYYY h:mm a'.", "function_datetime_parse_url": "https://help.aitable.ai/docs/set-locale-specified-output-format", "function_day_example": "DAY({date of begin})\n=> 8", "function_day_summary": "Returns the day of the month of a datetime in the form of a number between 1-31.", "function_encode_url_component_example": "ENCODE_URL_COMPONENT({Search Phrase})", "function_encode_url_component_summary": "Replaces certain characters with encoded equivalents for use in constructing URLs or URIs. Does not encode the following characters: - _ . ~", "function_err_end_of_right_bracket": "Function needs to end with \")\"", "function_err_invalid_field_name": "Invalid column or function name: ${fieldId}", "function_err_no_left_bracket": "( should be added to the right side of the function name", "function_err_no_ref_self_column": "Can't add the current Formula field in a formula", "function_err_not_definition": "Undefined function: {name}", "function_err_not_found_function_name_as": "function implementation named: ${fnName} unfound.", "function_err_unable_parse_char": "Parse character {value} failed", "function_err_unknown_operator": "Unknown operational character: {type}", "function_err_unrecognized_char": "Unidentified character$(value)", "function_err_unrecognized_operator": "Unrecognized operational character: ${token}", "function_err_wrong_function_suffix": "Wrong formula ending", "function_err_wrong_unit_str": "Wrong unit parameter value: ${unitStr}", "function_error_example": "IF({age}< 0, ERROR(\"alien\"), \"normal\")", "function_error_summary": "Returns the error value.", "function_even_example": "EVEN(1.5)\n=> 2\n\nEVEN(-1.8)\n=> -2", "function_even_summary": "Returns the smallest even integer that is greater than or equal to the specified value.", "function_example_example": "{Unit Price} * {Quantity} {Name} & \"-\" & {Year} AVERAGE({Mathematics}, {Language}, {English}) IF({Average Score}> 60, \"👍Qualified\", \"❗Failed \")", "function_example_summary": "Fill in variables, operational characters, and functions to form formulas for calculations", "function_example_usage": "Quoting the Column: {Columns name} \nUsing operator: 2 * 5 \nUsing function: AVERAGE({Number Columns 1}, {Number Columns 2}) \nUsing IF statement: IF(logical condition, \"value 1\", \"value 2 \")", "function_exp_example": "EXP(1)\n=> 2.72\n\nEXP(2)\n=> 7.40", "function_exp_summary": "Computes <PERSON><PERSON><PERSON>'s number (e) to the specified power.", "function_false_example": "IF({average score} > 60, TRUE(), FALSE())", "function_false_summary": "Logical value false.", "function_find_example": "FIND(\"apple\", \"This is an apple\")\n=> 12", "function_find_summary": "Finds an occurrence of stringToFind in whereToSearch string starting from an optional startFromPosition.(startFromPosition is 0 by default.) If no occurrence of stringToFind is found, the result will be 0.", "function_floor_example": "FLOOR(1.01, 0.1)\n=> 1.0\n\nFLOOR(-1.99, 0.1)\n=> -2.0", "function_floor_summary": "Returns the nearest integer multiple of significance that is less than or equal to the value. If no significance is provided, a significance of 1 is assumed.", "function_fromnow_example": "FROMNOW({Date}, \"days\")\n=> 25", "function_fromnow_summary": "Calculates the number of days between the current date and another date.", "function_fromnow_url": "https://help.aitable.ai/docs/manual-supported-unit-specifiers-for-date-function", "function_guidance": "Smart onboarding", "function_hour_example": "HOUR({date of begin})\n=> 9", "function_hour_summary": "Returns the hour of a datetime as a number between 0 (12:00am) and 23 (11:00pm).", "function_if_example": "IF(Score > 60, \"Pass\", \"Fail\")\n\nIF(WaterTemp > 100, IF(WaterTemp < 212, \"just right\", \"too hot\"), \"too cold\")\n\nIF(Date = BLANK(), \"Please enter date\", \"Date entered\")", "function_if_summary": "Returns value1 if the logical argument is true, otherwise it returns value2. Can also be used to make nested IF statements.\n\nCan also be used to check if a cell is blank/is empty.", "function_int_example": "INT(1.99)\n=> 1\n\nINT(-1.99)\n=> -2", "function_int_summary": "Returns the greatest integer that is less than or equal to the specified value.", "function_is_after_example": "IS_AFTER({deadline}, TODAY())\n=> 0", "function_is_after_summary": "Determines if [date1] is later than [date2]. Returns 1 if yes, 0 if no.", "function_is_before_example": "IS_BEFORE({deadline}, TODAY())\n=> TRUE", "function_is_before_summary": "Determines if [date1] is earlier than [date2]. Returns 1 if yes, 0 if no.", "function_is_error_example": "IS_ERROR(2/0)", "function_is_error_summary": "Returns true if the expression causes an error.", "function_is_same_example": "IS_SAME({Date 1}, {Date 2}, \"hour\")\n=> 0", "function_is_same_summary": "Compares two dates up to a unit and determines whether they are identical. Returns 1 if yes, 0 if no.", "function_is_same_url": "https://help.aitable.ai/docs/manual-supported-unit-specifiers-for-date-function", "function_iserror_example": "ISERROR(2/0)", "function_iserror_summary": "Returns true if the expression causes an error.", "function_last_modified_time_example": "LAST_MODIFIED_TIME()\n=> \"2020-06-10 6:27 p.m.\"\n\nLAST_MODIFIED_TIME({date of begin})\n=> \"2020-06-09 1:27 p.m.\"", "function_last_modified_time_summary": "Returns the date and time of the most recent modification made by a user in a non-computed field in the table. If you only care about changes made to specific fields, you can include one or more field names, and the function will just return the date and time of the most recent change made to any of the specified fields.", "function_left_example": "LEFT({date of birth}, 4)\n=> 1994", "function_left_summary": "Extract howMany characters from the beginning of the string.", "function_len_example": "LEN(\"apple\")\n=> 5", "function_len_summary": "Counts the character length of a  text.\n\n[string] is the text to calculate the length; Punctuation, spaces, etc. also account for one character.", "function_log_example": "LOG(1024, 2)\n=> 10\n\nLOG(10000)\n=> 4", "function_log_summary": "Computes the logarithm of the value in provided base. The base defaults to 10 if not specified.", "function_lower_example": "LOWER(\"Hello!\")\n=> hello!", "function_lower_summary": "Makes a string lowercase.", "function_max_example": "MAX(5, -5, 555, -55)\n=> 555", "function_max_summary": "Returns the largest of the given numbers.", "function_mid_example": "MID(\"This is an apple\", 12, 5)\n=> apple", "function_mid_summary": "Extract a substring of count characters starting at whereToStart.", "function_min_example": "MIN(5, -5, 555, -55) \n=> -55 \n\nMIN({数学成绩}, {英语成绩}, {语文成绩})", "function_min_summary": "Returns the smallest of the given numbers.", "function_minute_example": "MINUTE({date of begin})\n=>30", "function_minute_summary": "Returns the minute of a datetime as an integer between 0 and 59.", "function_mod_example": "MOD(7, 2)\n=> 1", "function_mod_summary": "Returns the remainder after dividing the first argument by the second.", "function_month_example": "MONTH({date of begin})\n=> 6", "function_month_summary": "Returns the month of a datetime as a number between 1 (January) and 12 (December).", "function_not_example": "NOT({age} > 18)", "function_not_summary": "Reverses the logical value of its argument.", "function_now_example": "NOW()\n=> \"2020-06-02 07:12\"", "function_now_summary": "Returns the current date and time.", "function_odd_example": "ODD(1.5)\n=> 3\n\nODD(-2.1)\n=> -3", "function_odd_summary": "Rounds positive value up to the nearest odd number and negative value down to the nearest odd number.", "function_or_example": "OR(3>2, 2>3)\n=>  true", "function_or_summary": "Returns true if any one of the arguments is true.", "function_power_example": "POWER(2, 5)\n=> 32\n\nPOWER(-5, 3)\n=> -125", "function_power_summary": "Computes the specified base to the specified power.", "function_quarter_example": "QUARTER({date of begin})\n=> 2", "function_quarter_summary": "Returns the quater of a datetime as a number between 1  and 4.", "function_record_id_example": "\"https://awesomeservice.com/view?recordId=\" & RECORD_ID()", "function_record_id_summary": "Returns the ID of the current record.", "function_replace_example": "REPLACE(\"This is an apple\", 12, 8, \"elephant\")\n=> This is an elephant", "function_replace_summary": "Replaces the number of characters beginning with the start character with the replacement text.\n(If you're looking for a way to find and replace all occurrences of old_text with new_text, see SUBSTITUTE().)", "function_rept_example": "REPT(\"Hi! \", 3)\n\n=> Hi! Hi! Hi!", "function_rept_summary": "Repeats string by the specified number of times.", "function_right_example": "RIGHT({date of birth}, 5)\n=> 07-13", "function_right_summary": "Extract howMany characters from the end of the string.", "function_round_example": "ROUND(1.99, 0)\n=> 2\n\nROUND(18.8, -1)\n=> 20", "function_round_summary": "Rounds the value to the number of decimal places given by \"precision.\" (Specifically, ROUND will round to the nearest integer at the specified precision, with ties broken by rounding half up toward positive infinity.)", "function_rounddown_example": "ROUNDDOWN(1.9, 0)\n=> 1\n\nROUNDDOWN(-1.9, 0)\n=> -1", "function_rounddown_summary": "Rounds the value to the number of decimal places given by \"precision,\" always rounding down, i.e., toward zero. (You must give a value for the precision or the function will not work.)", "function_roundup_example": "ROUNDUP(1.1, 0)\n=> 2\n\nROUNDUP(-1.1, 0)\n=> -2", "function_roundup_summary": "Rounds the value to the number of decimal places given by \"precision,\" always rounding up, i.e., away from zero. (You must give a value for the precision or the function will not work.)", "function_search_example": "SEARCH(\"apple\", \"This is an apple\")\n=> 12", "function_search_summary": "Searches for an occurrence of stringToFind in whereToSearch string starting from an optional startFromPosition. (startFromPosition is 0 by default.) If no occurrence of stringToFind is found, the result will be empty.\nSimilar to FIND(), though FIND() returns 0 rather than empty if no occurrence of stringToFind is found.", "function_second_example": "SECOND({date of begin})\n=> 1", "function_second_summary": "Returns the second of a datetime as an integer between 0 and 59.", "function_set_locale_example": "DATETIME_FORMAT(SET_LOCALE(NOW(), \"zh-cn\"), \"lll\")\n=> 2020年6月2日上午11点04分", "function_set_locale_summary": "Sets a specific locale for a datetime. Must be used in conjunction with DATETIME_FORMAT.", "function_set_locale_url": "https://help.aitable.ai/docs/set-locale-specified-output-format", "function_set_timezone_example": "DATETIME_FORMAT(SET_TIMEZONE(NOW(), -8), \"M/D/YYYY h:mm\")\n=> 9/20/2021 2:30", "function_set_timezone_summary": "Sets a specific timezone for a datetime. Must be used in conjunction with DATETIME_FORMAT.", "function_sqrt_example": "SQRT(10000)\n=> 100", "function_sqrt_summary": "Returns the square root of a nonnegative number.", "function_substitute_example": "SUBSTITUTE(\"gold mold\", \"old\", \"et\")\n=> get met\n\nSUBSTITUTE(\"gold mold\", \"old\", \"et\", 1)\n=> get mold", "function_substitute_summary": "Replaces occurrences of old_text with new_text.\nYou can optionally specify an index number (starting from 1) to replace just a specific occurrence of old_text. If no index number is specified, then all occurrences of old_text will be replaced.", "function_sum_example": "SUM(1, 3, 5, \"\", \"VI\")\n=> 1 + 3 + 5 = 9", "function_sum_summary": "Sum together the numbers. Equivalent to number1 + number2 + ...", "function_switch_example": "SWITCH(1, 1, \"one\", 2, \"two\", \"many\")\n\n=>one", "function_switch_summary": "Takes an expression, a list of possible values for that expression, and for each one, a value that the expression should take in that case. It can also take a default value if the expression input doesn't match any of the defined patterns. In many cases, SWITCH() can be used instead of a nested IF() formula.", "function_t_example": "T({name})\n=> kelly\n\nT(3.2)\n=> BLANK", "function_t_summary": "Returns the argument if it is text and blank otherwise.", "function_timestr_example": "TIMESTR(NOW())\n=> \"04:52:12\"", "function_timestr_summary": "Formats a datetime into a time-only string (HH:mm:ss).", "function_today_example": "TODAY()\n=> \"2020-06-02 00:00\"", "function_today_summary": "Returns today's date (year, month, and day), but is not precise to hours, minutes, and seconds (default is 00:00:00). If you want to be precise to the hours, minutes, and seconds, use the function NOW.\n\nNote: The results returned by this function are updated only when the calculation formula is refreshed or the datasheet is refreshed.", "function_tonow_example": "TONOW(\"2019-08-10\", \"y\")\n=> 1\n\nTONOW({date}, \"days\")\n=> 25", "function_tonow_summary": "Calculates the number of days between the current date and another date.", "function_tonow_url": "https://help.aitable.ai/docs/manual-supported-unit-specifiers-for-date-function", "function_trim_example": "TRIM(\" Hello! \")\n\n=> Hello!", "function_trim_summary": "Removes whitespace at the beginning and end of the string.", "function_true_example": "IF({average score} > 60, TRUE(), FALSE())", "function_true_summary": "Logical value true.", "function_upper_example": "UPPER(\"Hello!\")\n=> HELLO!", "function_upper_summary": "Makes string uppercase.", "function_validate_params_count": "${name} function requires ${count} parameters", "function_validate_params_count_at_least": "${name} function requires ${count} parameters at least", "function_value_example": "VALUE(\"$10000\")\n=> 10000", "function_value_summary": "Converts the text string to a number.", "function_view_url": "Introduction of specifiers", "function_weekday_example": "WEEKDAY(TODAY())", "function_weekday_summary": "Returns the day of the week as an integer between 0 and 6, inclusive. You may optionally provide a second argument (either \"Sunday\" or \"Monday\") to start weeks on that day. If omitted, weeks start on Sunday by default. Example:\nWEEKDAY(TODAY(), \"Monday\")", "function_weeknum_example": "WEEKNUM(TODAY(), \"Monday\")", "function_weeknum_summary": "Returns the week number in a year. You may optionally provide a second argument (either \"Sunday\" or \"Monday\") to start weeks on that day. If omitted, weeks start on Sunday by default. Example:\nWEEKNUM(TODAY(), \"Monday\")", "function_workday_diff_example": "WORKDAY_DIFF({Product begin date}, {Product launch date} , \"2020-06-25, 2020-06-26, 2020-06-27\")\n=> 100", "function_workday_diff_summary": "Returns the number of working days between startDate and endDate. Working days exclude weekends and an optional list of holidays, formatted as a comma-separated string of ISO-formatted dates.", "function_workday_example": "WORKDAY({ Product launch date}, 100, \"2020-06-25, 2020-06-26, 2020-06-27\")\n=> 2020-07-12", "function_workday_summary": "Returns a date several workdays after the start date.\n\n[startDate] is the start date you specify.\n[numDays] is the number of workdays after the start date you specify, expressed as a positive number. For example, the number \"1\" represents a date one workday after the start date;\n[holidays] Is not required. It is specific dates that you want to remove from your calendar, such as a holiday. The input format is \"yyyy-mm-dd\", and multiple dates are separated by commas.\n\nThe workdays of this function do not include weekends and specific days that you specify.", "function_xor_example": "XOR(3>2, 2>3, 4>3)\n=> false", "function_xor_summary": "Returns true if an odd number of arguments are true.", "function_year_example": "YEAR({date of begin})\n=> 2020", "function_year_summary": "Returns the four-digit year of a datetime.", "functions": "Functions", "gabon": "Gabon", "gain_some_vb": "", "gallery_arrange_mode": "Arrange mode", "gallery_group_hlep_url": "https://help.aitable.ai/docs/manual-gallery-view", "gallery_guide_desc": "Use cards to display attachments visually and manage attachments easily", "gallery_img_stretch": "Fit", "gallery_style_setting_url": "https://help.aitable.ai/docs/manual-gallery-view#set-style", "gallery_view": "Gallery", "gallery_view_copy_record": "Duplicate record", "gallery_view_delete_record": "Delete record", "gallery_view_expand_record": "Expand record", "gallery_view_insert_left": "Insert record before", "gallery_view_insert_right": "Insert record after", "gallery_view_shortcuts": "Gallery view", "gambia": "Gambia", "gantt_add_date_time_field": "Create Date field", "gantt_add_record": "New task", "gantt_add_task_text": "New task", "gantt_back_to_now_button": "Today", "gantt_by_unit_type": "${unitType}", "gantt_cant_connect_when_computed_field": "The date fields contain computed field that is not allowed to write manually, please adjust the field and try again.", "gantt_check_connection": "Check the connection", "gantt_color_more": "More colors", "gantt_color_setting": "Custom task bar color", "gantt_config_color_by_custom": "Custom color", "gantt_config_color_by_single_select": "By Select field", "gantt_config_color_by_single_select_field": "By Single Select field", "gantt_config_color_by_single_select_pleaseholder": "Choose Select field", "gantt_config_color_help": "How to set up", "gantt_config_friday": "Friday", "gantt_config_friday_in_bar": "<PERSON><PERSON>", "gantt_config_friday_in_select": "Friday", "gantt_config_monday": "Monday", "gantt_config_monday_in_bar": "Mon", "gantt_config_monday_in_select": "Monday", "gantt_config_only_count_workdays": "Duration only counts workdays.", "gantt_config_saturday": "Saturday", "gantt_config_saturday_in_bar": "Sat", "gantt_config_saturday_in_select": "Saturday", "gantt_config_sunday": "Sunday", "gantt_config_sunday_in_bar": "Sun", "gantt_config_sunday_in_select": "Sunday", "gantt_config_thursday": "Thursday", "gantt_config_thursday_in_bar": "<PERSON>hu", "gantt_config_thursday_in_select": "Thursday", "gantt_config_tuesday": "Tuesday", "gantt_config_tuesday_in_bar": "<PERSON><PERSON>", "gantt_config_tuesday_in_select": "Tuesday", "gantt_config_wednesday": "Wednesday", "gantt_config_wednesday_in_bar": "Wed", "gantt_config_wednesday_in_select": "Wednesday", "gantt_config_weekdays_range": "${weekday} to ${weekday}", "gantt_config_workdays_a_week": "Custom standard workdays", "gantt_cycle_connection_warning": "Invalid task dependency, there is a cycle connection\n", "gantt_date_form_start_time_year": "YYYY", "gantt_date_form_start_time_year_month": "MMM, YYYY", "gantt_date_time_setting": "Select Date fields", "gantt_dependency_setting": "Custom dependency ", "gantt_disconnect": "Disconnect", "gantt_end_field_name": "End date", "gantt_error_date_tip": "Something wrong about the dates. Please expand the record to edit.", "gantt_field_config_tip": "This field is not configured", "gantt_guide_desc": "Use a Gantt chart to arrange tasks and view the project schedule", "gantt_historical_data_warning": "How to handle old data?", "gantt_init_fields_button": "Create Date field", "gantt_init_fields_desc": "To generate a Gantt chart, please create two Date fields in your datasheet by clicking the button below", "gantt_init_fields_no_permission_desc": "A Date field is required to generate a Gantt chart", "gantt_init_fields_no_permission_title": "No permission to create Date fields", "gantt_init_fields_title": "Create Date fields first", "gantt_invalid_fs_dependency_warning": "Invalid task dependency, pre task start date is earlier than predecessor task end date", "gantt_month": "Month", "gantt_no_dependency": "No dependencies", "gantt_not_allow_link_multuble_records_gantt_warning": "The dependent relationship field does not allow linking to multiple records. Please modify the field configuration and try it again", "gantt_not_rights_to_link_warning": "You do not have permission for the dependency field and can not set the dependency", "gantt_open_auto_schedule_switch": "Auto schedule", "gantt_open_auto_schedule_warning": "Do you want tasks with dependencies to be automatically aligned when this feature is on? Some tasks' dates will be changed.", "gantt_open_auto_schedule_warning_no": "Don't change old data", "gantt_pick_dates_tips": "Please select a date field", "gantt_pick_end_time": "End date", "gantt_pick_start_time": "Start date", "gantt_pick_two_dates_tips": "Please select the start and end dates", "gantt_quarter": "Quarter", "gantt_set_depedency_field_description": "Need a Link field that connects to the table it is in, by linked means that it set a front task", "gantt_set_depedency_field_tips": "Please select the dependency field", "gantt_setting": "Settings", "gantt_setting_help_tips": "Help manual", "gantt_setting_help_url": "http://help.dolphintable.suanlihaiyang.com/docs/manual-gantt-view", "gantt_start_field_name": "Start date", "gantt_task": "Task", "gantt_task_group_tooltip": "${count} task(s)", "gantt_task_total_date": "${count} days", "gantt_task_total_workdays": "${count} workdays", "gantt_view": "<PERSON><PERSON><PERSON>", "gantt_week": "Week", "gantt_workdays_setting": "Custom workdays", "gantt_year": "Year", "generating_token_value": "Generate token", "generation_fail": "Generation failed", "generation_success": "Generated", "georgia": "Georgia", "germany": "Germany", "get_global_search_upgrade_silver": "Upgrade to Silver Plan for advanced searching", "get_invitation_code": "👉 Get invite code", "get_invite_code": "Click to get invite code", "get_invite_code_tip": "Tips: get the code via \"WeChat QR Code\" or search the official WeChat account of \"dolphindata\", follow, and reply with keywords ${keyword}", "get_link_person_on_internet": "Anyone with the link ", "get_v_coins": "Use your invite code to invite friends, and be rewarded with 1,000 V coins. V coins can be used to purchase services.", "get_verification_code": "Send", "get_verification_code_err_button": "Confirm", "get_verification_code_err_content": "Verification code obtained too frequently, please try again in 20 minutes", "get_verification_code_err_title": "Note", "ghana": "Ghana", "ghost_node_no_access": "No permission to access the source datasheet", "gibraltar": "Gibraltar", "gird_view_shortcuts": "Grid view", "give_feedback_to_translation": "To give feedback on the translation or help with localization, ", "give_feedback_to_translation_learn_more": "learn more", "give_up_edit": "Discard edits", "global": "General", "global_earth": "Global", "global_search": "Search", "global_shortcuts": "General", "global_storage_size_large": "Global storage exceeds the limit", "go_login": "Log in ", "go_to": "Go to", "go_to_dingtalk_admin": "Go to DingTalk Apps", "go_to_here_now": "Go", "gold": "Gold", "gold_grade": "Gold", "gold_grade_desc": "For teams with complex business process", "gold_seat_200_desc": "200", "golden_grade": "Gold", "got_it": "Got it", "got_v_coins": "V coins rewarded", "goto_datasheet_record": "Go to the original datasheet", "government_and_politics": "Government and politics", "grade_desc": "Spaces have three kinds of subscription plans: Bronze Plan, Silver Plan, Gold Plan and Enterprise Plan. Members in the Spaces of different subscription plans enjoy different benefits.", "grade_price_by_day": "{\"bronze\":\"free\",\"silver\":\"1 yuan\",\"gold\":\"1.5 yuan\",\"enterprise\":\"customized on demand\"}", "grade_price_by_month": "{\n    \"bronze\": \"Free\",\n    \"silver\": \"$10 $25 /month\",\n    \"gold\": \" $125 /month\",\n    \"enterprise\": \" $125 /month\",\n    \"community\": \"Free\",\n    \"custom\": \"Customize\",\n    \"free\":\"$0\",\n    \"plus\": \" $24 /month\",\n    \"pro\": \" $49 /month\"\n}", "grade_price_by_month_origin": "{\n  \"bronze\":\"\",\n  \"silver\": \"Incl. 10 users, then $2/user/month\",\n  \"gold\": \"Incl. 10 users, then $2/user/month\",\n  \"enterprise\":\"Incl. 5 seats, then $32/seat/month\",\n  \"free\":\"Forever\",\n  \"plus\": \"Incl. 5 seats, then $7/seat/month\",\n  \"pro\": \"Incl. 5 seats, then $14/seat/month\"\n}", "grade_price_by_year": "{\n    \"bronze\": \"Free\",\n    \"silver\": \"$15 $25 /year\",\n    \"gold\": \" $399 /year\",\n    \"enterprise\": \" $100 /month\",\n    \"community\": \"Free\",\n    \"custom\": \"Customize\",\n    \"free\":\"$0\",\n    \"plus\": \" $19 /month\",\n    \"pro\": \" $39 /month\"\n}\n  ", "grade_price_by_year_origin": "{\n  \"bronze\": \"\",\n  \"silver\": \"Incl. 10 users, then ${originPrice} $1.5/seats/month\",\n  \"gold\": \"Incl. 20 users, then ${originPrice} $3/seats/month\",\n  \"enterprise\": \"Incl. 5 seats, then $30/seat/month\",\n  \"community\": \"\",\n  \"custom\": \"\",\n  \"free\": \"Forever\",\n  \"plus\": \"Incl. 5 seats, then $5/seat/month\",\n  \"pro\": \"Incl. 5 seats, then $12/seat/month\"\n}", "grades_restriction_prompt": "Your feature usage has exceeded the upper limit, and you can upgrade to get higher usage.", "greece": "Greece", "greenland": "Greenland", "grenada": "Grenada", "grid_guide_desc": "Use a datasheet to populate all types of data that can be filtered, grouped, and sorted", "grid_view": "Grid", "grit_keep_sort_disable_drag": "You've enabled automatic sorting. Disable it before you can drag to move.", "group": "Group", "group_amount": "${amount} group(s)", "group_blank": "[None]", "group_by_field": "Group by", "group_field_error_tips": "Grouping condition error, please reset", "group_fields": "Group by \"${field_name}\"", "group_help_url": "https://help.aitable.ai/docs/manual-group", "groups_clubs_hobbies": "Groups, Clubs & Hobbies", "gt_person": "", "guadeloupe": "Guadeloupe", "guam": "Guam", "guatemala": "Guatemala", "guests_per_space": "Guests per Space", "guide_1": "啊这", "guide_2": "It takes only a few minutes to learn the basic functions. Work more productively from this moment on!", "guide_flow_modal_contact_sales": "Contact Sales", "guide_flow_modal_get_started": "Get Started", "guide_flow_of_catalog_step1": "Here is working catalog where all the folders and file nodes of the Space are stored.", "guide_flow_of_catalog_step2": "In the working catalog, you can create a datasheet or a folder as needed.", "guide_flow_of_click_add_view_step1": "In addition to some basic view, you are highly recommended to create an album view if you have attachments in picture format.", "guide_flow_of_datasheet_step1": "This is a list of views that are created for this datasheet. You can create new views here.", "guide_flow_of_datasheet_step2": "Here is the view toolbar where you can perform operations such as filtering or sorting the data in the datasheet.", "guide_flow_of_datasheet_step3": "These the content of your datasheet. Your data is all right here.", "guide_flow_of_datasheet_step4": "For filtering data, click here to create a new view.", "guide_flow_of_folder_show_case_step1": "This is the preview page of the folder. You can set up a cover for each folder, and write down a description under the title of the folder.", "guide_privacy_modal_content": "Please read ${content}, and click agree to start our service.", "guide_restart": "User guidance", "guide_workspace_step_title_prefix": "Space guidence", "guinea": "Guinea", "guinea_bissau": "Guinea-Bissau", "guyana": "Guyana", "haiti": "Haiti", "handbook": "Product manual", "handed_over_workspace": "New admin's team", "heading_five": "Heading 5", "heading_four": "Heading 4", "heading_one": "Heading 1", "heading_six": "Heading 6", "heading_three": "Heading 3", "heading_two": "Heading 2", "health_and_self_improvement": "Health and self-improvement", "help": "Help ", "help_center": "Help center", "help_help_center_url": "https://help.aitable.ai/", "help_partner_program": "Partner Program", "help_product_manual_url": "https://help.aitable.ai/docs/manual/", "help_questions_url": "https://help.aitable.ai/docs/questions", "help_quick_start_url": "https://help.aitable.ai/docs/tutorial-1-quick-start/", "help_resources": "Help Resources", "help_user_community": "Join Community", "help_video_tutorials": "Video tutorials", "hidden": "<PERSON>de", "hidden_field_calendar_tips": "The Calendar view supports showing no more than 10 fields", "hidden_field_calendar_toast_tips": "can't show more fields", "hidden_field_desc": "Hidden field description", "hidden_fields_amount": "${amount} hidden field(s)", "hidden_graphic_fields_amount": "${amount} graphic field", "hidden_groups_by_count": "${count} Group(s) Hidden", "hidden_n_fields": "Hide ${count} fields", "hide_all_fields": "Hide all", "hide_field_tips_in_gantt": "Click to adjust the display fields in the task area", "hide_fields": "Hide fields", "hide_fields_not_go": "Can't go to the hidden field", "hide_graphic_field_tips_in_gantt": "Click to adjust the display fields in the task item", "hide_kanban_grouping": "Hide group", "hide_node_permission_resource": "Hide no permission file node", "hide_one_field": "Hide field", "hide_one_graphic_field": "Hide graphic field", "hide_pane": "Hide panel", "hide_uneditable_automation_node": "Hide uneditable  automation nodes", "hide_unmanageable_files": "Hide unmanageable file nodes", "hide_unmanaged_sheet": "Hide unmanageable resource", "hide_unusable_sheet": "Hide uneditable resource", "highlight": "Highlight", "hint": "Tips", "history_view_more": "Show more", "history_view_tip": "You can view record activity in ${day} days. ", "honduras": "Honduras", "hong_kong": "Hong Kong, China", "how_contact_service": "https://help.aitable.ai/docs/how-contact-service", "how_create_template": "This Space has no custom templates yet. <a href='https://help.aitable.ai/docs/faq-how-create-template'>How to create</a>", "how_many_seconds": "${seconds}s", "how_to_get_v_coins": "How to get V coins", "how_to_report_issues": "https://help.aitable.ai/docs/how-to-report-issues", "hr_and_recruiting": "HR & Recruiting", "hungary": "Hungary", "i_knew_it": "I knew it", "iceland": "Iceland", "icon_setting": "Custom icon", "icp1": "粤ICP备 19106018号", "icp1_mobile": "粤ICP备: 19106018号", "icp2": "公安备案 44030402004286", "icp2_mobile": "公安备案: 44030402004286", "identification": "Verify identity", "identifying_code_placeholder": "Enter your phone number or email", "if_it_is_successful": "Succeed or not", "image": "Image", "image_limit": "The image size must be no larger than ${number} MB", "image_uploading": "Image uploading...", "import": "Import", "import_canceled": "Cancel import", "import_excel": "Import from Excel", "import_failed": "Import failed", "import_failed_list": "Import failed list", "import_file": "Import file", "import_file_btn_title": "Import", "import_file_data_succeed": "File exported", "import_file_outside_limit": "The uploaded file size should not exceed 20 MB", "import_from_excel_tooltip": "Import .xlsx/.csv files", "import_view_data_succeed": "View exported", "import_widget": "Import widget", "import_widget_success": "Widget imported", "improving_info": "Complete personal information", "improving_info_tip": "You can bind an existing account or get a new one", "include_time": "Include time", "inclusion_plan_button_value": "", "inclusion_plan_desc": "{\"first_row\":\"Dolphin表格是一款有温度的产品，我们支持创业企业与教育组织群体，\",\"second_row\":\"提供低至 3 折的普惠定价方案，\",\"third_row\":\"愿为创业企业与教育组织群体提供更多帮助。\"}", "inclusion_plan_title": "APITable Entrepreneurship Support and Education Inclusive Program", "income_expenditure_records": "Income and expenses", "india": "India", "indonesia": "Indonesia", "inform": "Report abuse", "inherit_field_permission_tip": "This field is accessible to all members or teams below", "inherit_permission_tip": "Permissons are inherited from the parent folder", "inherit_permission_tip_root": "All members or teams below can access this file node", "inhert_permission_desc": "Members and teams inherit permissions from superior folder \"${nodeName}\" ", "init_roles": "Finance,Marketing,IT,HR,Legal", "initial_size": "Original size", "initialization_failed_message": "An error occurred while initializing data, please refresh the page", "inline_code": "Inline code", "input_confirmation_password": "Confirm new password", "input_formula": "Enter a formula", "input_new_password": "New password", "insert_above": "Insert above", "insert_below": "Insert below", "insert_field_above": "Insert field above", "insert_field_after": "Insert right", "insert_field_before": "<PERSON><PERSON><PERSON> left", "insert_field_below": "Insert field below", "insert_new_field_below": "Insert new field below", "insert_record": "Insert record", "insert_record_above": "Insert record above", "insert_record_below": "Insert record below", "insert_record_left": "Insert ${record_tip} before", "insert_record_right": "Insert ${record_tip} after", "install_widget": "Install", "installation": "Installation", "instruction_of_node_permission": "Permissions overview", "integer": "<PERSON><PERSON>ger (2)", "integral_income_notify": "You're rewarded \"<a class=\"count\"></a>\" V coins due to \"<a class=\"actionName\"></a>\"", "integral_rule_of_be_invited_to_reward": "Be invited by ${name} to be rewarded", "integral_rule_of_invitation_reward": "Invite ${name} to be rewarded", "integration_app_wecom_bind_loading_text": "Refreshing... Please don't exit", "integration_app_wecom_bind_success_badge": "Exclusive", "integration_app_wecom_bind_success_title": "Congrats! The binding is successful. An enterprise domain name has been generated for the Space.", "integration_app_wecom_config_item1_desc": "After the integration of wecom, please properly save and use the enterprise's exclusive domain name to log in to the datasheet", "integration_app_wecom_config_item1_title": "Enterprise exclusive domain", "integration_app_wecom_config_item2_title": "Wecom application docking information", "integration_app_wecom_config_tips": "To modify the application information, please deactivate the wecom integration before proceeding", "integration_app_wecom_create_application_error1": "Failed to obtain application details. \"Please check ${coypIdLabel}, ${agentIdLabel}, ${secretLabel}\"", "integration_app_wecom_create_application_error2": "Please enable the app", "integration_app_wecom_desc": "<ul><li>Datasheet supports integration with wecom. After integration, you can use wecom scanning code to log in on the web side of datasheet. You can use datasheet without login in wecom and receive message reminders from datasheet</li><li>After integrating the datasheet through self built application in wecom, the datasheet will be displayed in the workbench</li></ul>", "integration_app_wecom_form1_desc": "Please create a self built application in wecom and fill in the application information below <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-wecom#unbinding-instructions' target=\\\"_blank\\\"> how to fill in </a>", "integration_app_wecom_form1_item1_label": "Enterprise ID", "integration_app_wecom_form1_item2_label": "AgentId", "integration_app_wecom_form1_item3_label": "Secret", "integration_app_wecom_form1_title": "Wecom self built application information", "integration_app_wecom_form2_desc": "Please fill in the connection information of datasheet into the self built application of wecom to realize the secret free login of datasheet in wecom <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-wecom#operation-steps' target=\\\"_blank\\\">如何填写</a>", "integration_app_wecom_form2_item1_label": "Application home page", "integration_app_wecom_form2_item2_label": "Trusted domain name", "integration_app_wecom_form2_title": "2. Datasheet docking information\n\n", "integration_app_wecom_info_check": "I have finished configuring", "integration_app_wecom_matters": "<ul><li>In order to integrate the existing Space station with wecom,<span> the operator needs to have the management authority of datasheet Space and wecom at the same time</span>, that is, the main administrator of the Space is also the administrator with application management authority in wecom, and ensure that the wecom account is in normal use</li><li>The Space is integrated with the wecom. The address book on the wecon side needs to be synchronized in one direction. The member and group information will be refreshed.<span>If the Space member has not been bound to the wecom, the Space will be removed</span>, grayed out in the member column, and the relevant permissions will be cleared. It needs to be reset after the same step in the address book</li></ul>", "integration_app_wecom_step1_title": "Integration description", "integration_app_wecom_step2_title": "Create application", "integration_app_wecom_step3_main_desc": "After scanning the code, your Dolphin and wecom account will be bound", "integration_app_wecom_step3_main_title": "The main administrator of the Space scans the following QR code with wecom", "integration_app_wecom_step3_title": "Finish binding", "integration_app_wecom_step_info_title": "Docking information", "integration_dingtalk": "DingTalk Integration", "integration_feishu": "feishu integration", "integration_we_com": "wecom integration", "integration_wecom_disable_button": "Confirm", "integration_wecom_disable_contact": "Contact us if you need help", "integration_wecom_disable_tips_message": "<ul>\n         <li>\n          Due to the change in the operation rules for sevice provider of WeCom, the integration mode of the Table table needs to be adjusted. <i> therefore, it will be impossible to integrate WeCom with self-built application mode shortly after you disable the application. </i>. We apologize for the inconvenience.\n        </li>\n        <li>\n         You can use the \"WeCom third-party application\"  to quickly create an integrated new space <a href= \"http://help.dolphintable.suanlihaiyang.com/docs/integration-wecom\"> click to view the introduction of third-party application </a>\n        </li>\n</ul>", "integration_wecom_disable_tips_title": "Notice", "integration_yozo_office": "YOZO Office integration", "internet": "Internet", "intrant_space_empty_tip": "Oops, you have not joined any Space yet", "intrant_space_list": "Joined Spaces", "intro_dashboard": "http://help.dolphintable.suanlihaiyang.com/docs/intro-dashboard", "intro_widget": "http://help.dolphintable.suanlihaiyang.com/docs/intro-widget", "intro_widget_tips": "What is widget?", "introduction": "Introduction", "invalid_action_sort_tip": "As a grouping field, the sorting of it has been set. The setting of the current order will not take effect.", "invalid_automation_configuration": "Invalid automation configuration, please check and try again", "invalid_field_type": "Invalid field type", "invalid_option_sort_tip": "As a grouping field, the sorting of it has been set. ", "invalid_redemption_code_entered": "Invalid redeem code", "invitation_code_tip": "You and your inviter will both get 1000 V coins, ${text}", "invitation_code_usage_tip": "Tips: Use your invite code to invite friends to get V coins", "invitation_link_old": "Created invitation link(s)", "invitation_reward": "Invited \"${name}\"", "invitation_to_join": "Invite to join the Space", "invitation_to_join_desc": "Collaborate with others on the Space ", "invitation_validation_tip": "To maintain the order of the Space, an immigrant visa is needed~", "invitation_validation_title": "Oops, a large batch of users are flooding into the Space right now.", "invite": "Invite", "invite_by_qr_code": "Invite QR Code", "invite_code": "Invite code\n", "invite_code_add_official": "Accept official invitation and become official friend", "invite_code_cannot_use_mine": "Cannot enter your own invite code", "invite_code_get_v_coin": "Invite your friends to register and get 1000 V-coins", "invite_code_input_placeholder": "Enter an invite code (optional)", "invite_code_no": "👉🏻 Don't have an invite code?", "invite_code_tab_mine": "My invite code", "invite_code_tab_mine_get_v_coin_both": "You and your friend will receive 1000 V-coins after your friend successfully registered with your invite code.", "invite_code_tab_mine_way_1": "The first way: Invite friends through the invite code link", "invite_code_tab_mine_way_1_tip": "If your friend hasn't registered yet, you can copy the link below to your friend", "invite_code_tab_mine_way_2": "The second way: Enter the invite code", "invite_code_tab_mine_way_2_tip": "If your friend has already registered, your friend can also enter your invite code to get the free 1000 V-coins", "invite_code_tab_submit": "Enter the invite code", "invite_code_tab_submit_input_placeholder": "Enter a friend or official invite code (once only)", "invite_code_tab_sumbit_get_v_coin_both": "Enter your friend's invite code,\nyou and your friend will each get 1000 V-coins", "invite_email_already_exist": "This email has been invited. Repeated invitation is unnecessary. ", "invite_empty_tip": "No result. Enter email to invite", "invite_exist_mail_failed": "You can't send an invitation twice via the same email address", "invite_friends": "Invite", "invite_give_capacity_intro": "Invite a new member to join the space by link or email, each one will give the space a 300 MB (1 year) attachment capacity boost", "invite_invite_title_desc": "Account ${invitee} is invited, please log in and confirm to join.", "invite_member": "Invite", "invite_member_join": "Invite member to join", "invite_member_toadmin": "<a class=\"involveMemberArr\"></a> was invited by <a class=\"memberName\"></a> and joined the \"<a class=\"spaceName\"></a>\" Space.", "invite_member_tomyself": "<a class=\"involveMemberArr\"></a> was invited by you and joined the \"<a class=\"spaceName\"></a>\" Space.", "invite_member_touser": "<a class=\"memberName\"></a> invited you to join the \"<a class=\"spaceName\"></a>\" Space.", "invite_members": "Invite members", "invite_ousider_import_file_tip1": "Drag and drop files or click here to import", "invite_ousider_import_file_tip2": "Maximum 500 members can be imported each time ", "invite_ousider_import_file_tip3": "Support .xlsx/.csv files only", "invite_ousider_template_click_to_download": "Download employee-information-template.xlsx", "invite_ousider_template_file_name": "Employee information template.xlsx", "invite_outsider_import_cancel": "Cancel import", "invite_outsider_invite_btn_tip": "Invited members will be able to manage the workbench by default", "invite_outsider_invite_input_already_exist": "This email has been added.", "invite_outsider_invite_input_invalid": "Invalid email address", "invite_outsider_invite_input_placeholder": "Enter one or more email addresses, confirm with <PERSON><PERSON>", "invite_outsider_keep_on": "Invite more", "invite_outsider_send_invitation": "Send invitation", "invite_qr_code_download": "Download this QR code", "invite_qr_code_introduction": "Scan code to join the team", "invite_reward": "Invite to get rewarded", "invite_success": "Invitation sent", "invite_team_and_collaborating": "Invite your team and start collaborating", "invite_via_link": "Invite link", "invite_your_join": " invites you to join", "invitee": "", "invitee_default_permission_desc": "All invited members have the permissions of this role once they join in", "inviter": "", "iran": "Iran", "iraq": "Iraq", "ireland": "Ireland", "is_empty": "is empty", "is_empty_widget_center_space": "No custom widgets", "is_empty_widget_panel_mobile": "No widgets, please add widgets on the PC", "is_empty_widget_panel_pc": "No widgets", "is_not_empty": "is not empty", "is_repeat": "has duplicates", "is_repeat_column_name": "Please enter a unique field name", "is_repeat_disable_tip": "You can add only one \"has duplicates\" filter", "israel": "Israel", "italic": "Italic", "italy": "Italy", "ivory_coast": "Ivory Coast", "jamaica": "Jamaica", "japan": "Japan", "join": "Submit Application", "join_discord_community": "Join community for help", "join_the_community": "Join community", "joined_members": "Active members", "jordan": "Jordan", "journalism_and_publishing": "Journalism and publishing", "jump_link_url": "Go to linked datasheet", "jump_official_website": "Return to the official website", "just_now": "Just now", "kaban_not_group": "Uncategorized", "kanban_add_new_group": "New group", "kanban_copy_record_url": "Copy record URL", "kanban_exit_member_group": "This member is already grouped. Please select again.", "kanban_group_tip": "Grouped by ${kanban_field_id}", "kanban_guide_desc": "Use a kanban board to manage projects and assign tasks", "kanban_insert_record_above": "Insert record above", "kanban_insert_record_below": "Insert record below", "kanban_keep_sort_sub_tip": "Placement in this area will automatically sort", "kanban_keep_sort_tip": "Processing auto sort", "kanban_new_member_field": "Name the field", "kanban_new_option_field": "Name the field", "kanban_new_option_group": "undone, in progress, done", "kanban_no_data": "No records", "kanban_no_permission": "No permission for this grouping field", "kanban_setting_create_member": "Create a Member field", "kanban_setting_create_option": "Create a Select field", "kanban_setting_tip": "Only \"Select\" or \"Member\" fields can be chosen as the grouping field", "kanban_setting_title": "Choose or create a grouping field", "kanban_style_setting_url": "https://help.aitable.ai/docs/manual-kanban-view", "kanban_view": "Ka<PERSON><PERSON>", "kazakhstan": "Kazakhstan", "keep_sort": "Auto sort", "kenya": "Kenya", "key_of_adjective": "'s ", "keybinding_show_keyboard_shortcuts_panel": "Shortcuts", "keyboard_shortcuts": "Shortcuts", "kindly_reminder": "Note", "kiribati": "Kiribati", "know_how_to_logout": "Learn more", "know_more": "Learn more", "known": "I knew", "kuwait": "Kuwait", "kyrgyzstan": "Kyrgyzstan", "lab": "Lab", "label_account": "Account", "label_bind_email": "Email", "label_bind_phone": "Phone", "label_format_day": "Day", "label_format_day_month_and_year_split_by_slash": "Day/Month/Year", "label_format_month": "Month", "label_format_month_and_day_split_by_dash": "Month-Day", "label_format_year": "Year", "label_format_year_and_month_split_by_dash": "Year-Month", "label_format_year_month_and_day_split_by_dash": "Year-Month-Day", "label_format_year_month_and_day_split_by_slash": "Year/Month/Day", "label_password": "Password", "label_set_password": "Set password", "language_setting": "Language", "language_setting_tip": "Your language setting will affect all the Spaces that you log in", "lanxin": "lanxin", "lanxin_bind_space_config_detail": "Consistent with the availability of Lanxin (${maxcount} members)", "lanxin_org_manage_reject_msg": "This Space's contacts are managed by the Lanxin integration. Please go to the Lark admin backend to edit contacts. ", "lanxin_space_list_item_tag_info": "Lanx<PERSON> binding", "laos": "Laos", "lark": "Lark", "lark_admin_login_and_config_sub_title": "Service Conditions: users with Table account can bind the account on Lark", "lark_admin_upgrade": "Please contact your Lark administrator to go to the Lark application backend to pay for the upgrade", "lark_app_desc": "<p>After installing the integration, please complete the space bundle, and after bundling, your team can use Table on Lark.</p>\n\n<p>With Table Assistant on Lark, you can:</p>\n\n<ul><li>Access Table's space and access space data directly on Lark without logging in;</li><li>Receive notifications from the space through Lark;</li><li>Set app visibility on Lark and automatically sync Lark's organization structure;</li></ul>\n", "lark_app_intro": "Allow you to log in to Table through Lark, receive your Space's notifications in Lark, and sync organizational chart from Lark", "lark_app_notice": "* Note: One Space can only bind one Lark self-built application", "lark_can_not_upgrade": "You can't do the upgrade on mobile, please move to PC to do the upgrade", "lark_integration": "Lark Integration", "lark_integration_ability_desc": "Using Lark's self-built applications, you can：", "lark_integration_config_tip": "If you need to modify the application information, please deactivate the Lark integration first before doing so", "lark_integration_config_title": "Docking Information for Lark", "lark_integration_step1": "Integration Instructions", "lark_integration_step1_content": "<ul>\n        <li>Bind the current space to Lark, the original data can be retained intact</li>\n        <li>Access the Table space directly from the Lark Workbench to enable password-free login and access to space data</li>\n        <li>Receive member notifications and comment notifications from Table right on Lark to stay on top of space developments</li>\n        <li>Lark and Table dual-site contacts are automatically updated/synchronized, and Table maintains the same organizational structure as Lark</li>\n      </ul>", "lark_integration_step1_instructions": "dolphindata x Lark Self-Built Application Integration Instructions", "lark_integration_step1_tips": "Tips", "lark_integration_step1_tips_content": "<ul>\n        <li>\n          In addition to the build-your-own app approach, we also offer a version of the Lark Store app that is ready to use right out of the box without configuration and binding to the space <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-lark#free-login' target=\\\"_blank\\\">What's different?</a>\n        </li>\n      </ul>", "lark_integration_step1_warning": "<ul>\n        <li>\n          <p>\n            <span>Operators must have administrative access to both Table space and Lark</span>\n          </p>\n          <p>The operator needs to be the main administrator of the space and also have the permission to create self-built applications in the Lark administration backend</p>\n        </li>\n        <li>\n          <p>\n            <span>The original members of the space will be removed from the space station, and then the member information and department information will be synchronized from the Lark side in one direction</span>\n          </p>\n          <p>After Lark integration is enabled, member and department information will be based on the Lark side, so the original member/group information cannot be retained, and related permission information (sub-administrator permission, file node permission, column permission, etc.) will be lost, so please let administrators know and make preparations</p>\n        </li>\n      </ul>", "lark_integration_step2": "Application Credentials", "lark_integration_step2_appid": "App ID", "lark_integration_step2_appsecret": "App Secret", "lark_integration_step2_content": "Please find the app credentials in the Lark developer backend and fill in the form below <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-lark#self-built-application' target=\\\"_blank\\\">How to fill in?</a>", "lark_integration_step2_next": "Generate routing address", "lark_integration_step2_title": "Get application credentials", "lark_integration_step3": "Routing <PERSON><PERSON><PERSON>", "lark_integration_step3_checkbox": "I have completed the configuration of the above URL on the lark side", "lark_integration_step3_content": "Please fill in the following routing address into the Lark application to achieve the function of password-free login to Table and other related functions in Lark  <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-lark#route-settings' target=\\\"_blank\\\">How to fill in?</a>", "lark_integration_step3_desktop": "Desktop homepage URL", "lark_integration_step3_management": "Management URL", "lark_integration_step3_mobile": "Mobile homepage URL", "lark_integration_step3_redirect": "Redirect URL", "lark_integration_step3_title": "Setting the routing address", "lark_integration_step4": "Event Subscription", "lark_integration_step4_content": "Please find the app credentials in the Lark developer backend and fill in the form below  <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-lark#event-subscription' target=\\\"_blank\\\">How to fill in?</a>", "lark_integration_step4_encryptkey": "Encrypt Key", "lark_integration_step4_next": "Generate event request URL", "lark_integration_step4_title": "Configuring event subscriptions", "lark_integration_step4_verificationtoken": "Verification Token", "lark_integration_step5": "Event Verification", "lark_integration_step5_content": "Please fill in the information below to subscribe to Dolphintdata's events in Lark's self-built application  <a href='http://help.dolphintable.suanlihaiyang.com/docs/integration-lark#event-validation' target=\\\"_blank\\\">How to fill in?</a>", "lark_integration_step5_error_content": "<ul>\n                  <li>1. whether the request URL is pasted incorrectly.</li>\n                  <li>2. Step 4 \"Event Subscription\" whether the Encrypt Key or Verification Token is correctly entered</li>\n                </ul>", "lark_integration_step5_error_title": "Please check：", "lark_integration_step5_request_button": "Checking the status of requests for events", "lark_integration_step5_request_button_error": "Request failed", "lark_integration_step5_request_button_success": "Request successful", "lark_integration_step5_request_url": "Request URL", "lark_integration_step5_title": "Configure the request URL", "lark_integration_step6": "Binding Master Administrator", "lark_integration_step6_content": "After scanning the code, your current Table account will be bound to your Lark account, and you will then become the main administrator of this space", "lark_integration_step6_title": "Please publish and approve the Lark application first", "lark_integration_sync_btn": "Go", "lark_integration_sync_success": "All operations on the Table side have been completed", "lark_integration_sync_tip": "Synchronization in progress", "lark_login": "Login with Lark", "lark_org_manage_reject_msg": "This Space's contacts are managed by the Lark integration. Please go to the Lark admin backend to edit contacts. ", "lark_single_record_comment_mentioned": "", "lark_single_record_comment_mentioned_title": "", "lark_single_record_member_mention": "**Record: **{recordTitle}\n**Mentioned by: **{memberName}\n**Datasheet: **{nodeName}", "lark_single_record_member_mention_title": "** You're mentioned in a record**", "lark_subscribed_record_cell_updated": "**Record:** {recordTitle}\n**Before:** {oldDisplayValue}\n**After:** {newDisplayValue}\n**Member:** {memberName}\n**Datasheet:** {nodeName}", "lark_subscribed_record_cell_updated_title": "**Someone has changed the record you are watching in**", "lark_subscribed_record_commented": "**Record:** {recordTitle}\n**Comment:** {content}\n**Member:** {memberName}\n**Datasheet:** {nodeName}", "lark_subscribed_record_commented_title": "**The record you are watching received a new comment**", "lark_version_enterprise": "Enterprise Plan with Lark", "lark_version_standard": "Standard Plan with Lark", "lark_versions_free": "Basic Plan with Lark", "last_day": "Last day", "last_modified_by_select_modal_desc": "If any of the fields you select below are edited, the member who edited most recently will show in the last edited by field", "last_modified_time_select_modal_desc": "If any of the fields you select below are edited, the most recently edited time will show in the last edited time field", "last_step": "Back", "last_update_time": "Last modified time", "latvia": "Latvia", "layout": "Layout", "learn_about_us": "To understand us,", "lebanon": "Lebanon", "left": "Move to the left cell", "legal": "Legal", "lesotho": "Lesotho", "levels_per_folder": "Levels per Folder", "liberia": "Liberia", "libya": "Libya", "liechtenstein": "Liechtenstein", "limit_chart_values": "There are currently too many graphic categories, showing only the first 300 types of data", "limited_time_offer": "Limited-time offers", "line_chart": "Line Chart", "link": "Link", "link_common_err": "Oops, it seems the link is incorrect.", "link_copy_success": "URL copied", "link_data_error": "Data in the related datasheet error", "link_delete_succeed": "Link deleted", "link_failed": "The public share link is invalid probably because the sharer disabled the link or the admin forbade public sharing", "link_failed_after_close_share_link": "The share link that has been published will become invalid once it is closed", "link_failure": "<PERSON> invalided", "link_input_placeholder": "Enter or paste a URL", "link_invite": "Invite via link", "link_other_datasheet": "content from other linked datasheet(s)", "link_permanently_valid": "Valid permanently", "link_record_no_permission": "Data no permission", "link_to_multi_records": "Link to multiple records", "link_to_specific_view": "Link to records from a view", "linked_datasheet": "Linked datasheet", "list": "List", "list_how_many_folders_and_files": "${folderNumber} Folder, ${fileNumber} file nodes", "list_of_attachments": "Attachments", "lithuania": "Lithuania", "load_tree_failed": "Failed to load tree resources", "loading": "Loading...", "loading_file": "Loading file nodes, please wait...", "local_business": "Local Business", "local_data_conflict": "Resources in the process of initialization of data errors, please refresh and retry", "local_drag_upload": "Upload local files", "lock": "Lock", "lock_view": "Lock view", "log_date_range": "", "log_out_confirm_again_tip": "Please enter ${content} to complete account deletion", "log_out_reading_h6": "Account deletion instructions", "log_out_reading_h8": "After deleting:", "log_out_succeed": "We have received your application", "log_out_user_list": "1. You will not be able to use the account to log in to the Table.<br/>\n2. All your data will be deleted.<br/>\n3. You will not be able to use the account to access the space station you created or joined.<br/>\n4. The deleted account cannot be recovered, if you wish to use the Table service again, you need to register a new Table account.", "log_out_verify_tip": "Your identity needs to be verified before deleting your account", "logical_functions": "Logical Function", "login": "Log in", "login_failed": "<PERSON><PERSON> failed", "login_frequent_operation_reminder_button": "Confirm", "login_frequent_operation_reminder_content": "You entered wrong account and password too frequently, please try again in 20 minutes", "login_frequent_operation_reminder_title": "Note", "login_logo_slogan": "Low-code platform with API-first Database-Spreadsheet", "login_logo_slogan_mobile": "API-first Database-Spreadsheet", "login_mobile_format_err": "Phone format error", "login_mobile_no_empty": "Empty phone number", "login_privacy_policy": "<Privacy Policy>", "login_register": "Log in", "login_slogan": "API-first Database-Spreadsheet", "login_status_expired_button": "Confirm", "login_status_expired_content": "Your session has expired, please log in again", "login_status_expired_title": "Note", "login_sub_slogan": "API-based database-spreadsheet hybrid, one-stop platform for project management and data operations", "login_title": "Login APITable", "login_with_qq_or_phone_for_invited_email": "Invited account ${inviteEmail}, please use the bound Email or phone number to log in.", "logo": "Logo", "logout": "Log out", "logout_warning": "Since your account managed spaces with multiple members(more than or equal to 2 members), please change the main admin to someone else on the \"Space Settings\" page before deleting the account.", "long_time_no_operate": "The datasheet has not been operated for a long time, please refresh the page to keep the data up-to-date~", "long_time_not_editor": "Loading...", "lookup": "Lookup", "lookup_and": "And operation", "lookup_and_example": "AND(1, 1, 1, 1) => 1 ", "lookup_and_summary": "Returns true if all the values are true, that is, not empty", "lookup_arraycompact": "Remove nils", "lookup_arraycompact_example": "ARRAYCOMPACT([1, 2, \"\", 3, false, \" \", null]) => [1, 2, 3, false, \" \"]", "lookup_arraycompact_summary": "Removes all empty strings and null values. Keep \"false\" and strings that contain one or more blank characters.", "lookup_arrayjoin": "Join values with commas", "lookup_arrayjoin_example": "ARRAYJOIN(2020/01/21, 121, apple) => 2020/01/21,121,apple", "lookup_arrayjoin_summary": "Joins all the values into a comma-separated string", "lookup_arrayunique": "Remove duplicates", "lookup_arrayunique_example": "ARRAYUNIQUE([1, 2, 3, 3, 1]) => [1, 2, 3]", "lookup_arrayunique_summary": "Returns an array that includes only unique values", "lookup_average": "AVERAGE", "lookup_average_example": "AVERAGE(2, 4, 6, 8, 10) => 6", "lookup_average_summary": "Returns the arithmetic mean of the values", "lookup_check_info": "The field value can't be edited because it's a lookup result", "lookup_concatenate": "CONCATENATE", "lookup_concatenate_example": "CONCATENATE(\"Name\", \"Year\", \"Age\", 1) => NameYearAge1", "lookup_concatenate_summary": "Joins all the values into a long string", "lookup_conditions_success": "Lookup sorting or filter set successfully", "lookup_configuration_no_link_ds_permission": "Cannot operate on this configuration without view permission for the associated table", "lookup_count": "COUNT", "lookup_count_example": "COUNT(1, 3, 5, \"\", \"apple\") => 3", "lookup_count_summary": "Returns the number of numeric values. Use COUNTALL to count all records.", "lookup_counta": "COUNTA", "lookup_counta_example": "COUNTA(1, 3, 5, \"\", \"Apple\") => 4", "lookup_counta_summary": "Returns the number of numeric and string values that are not empty", "lookup_countall": "COUNTALL", "lookup_countall_example": "COUNTALL(1, 3, 5, \"\", \"apple\") => 5", "lookup_countall_summary": "Returns the number of all values, including empty ones", "lookup_field": "Select a dimension row from ${datasheetName}", "lookup_field_err": "Please add a Link field first", "lookup_filter_condition_tip": "This field type has been converted. Please confirm the filter criteria", "lookup_filter_sort_description": "Add condition", "lookup_filter_waring": "The filter column of this field has type conversion, please check.", "lookup_help": "https://help.aitable.ai/docs/manual-field-lookup", "lookup_link": "Select a Link field", "lookup_max": "MAX", "lookup_max_example": "MAX(1, 3, 5, \"\", \"Apple\") => 5; MAX(2020/01/12, 1993/06/08, 2021/12/12, \"\", \"Apple\") => 2021/12/12", "lookup_max_summary": "Returns the largest value of all the values", "lookup_min": "MIN", "lookup_min_example": "MIN(1, 3, 5, \", \"apple\") => 1, MIN(2020/01/12, 1993/06/08, 2021/12/12, \", \"apple\") => 1993/06/08", "lookup_min_summary": "Returns the smallest value of all the values", "lookup_modal_subtitle": "The Link field has linked this datasheet to another one", "lookup_not_found_search_keyword": "No matches that contain \"${notFoundSearchKeywordSpan}\"", "lookup_or": "Or operation", "lookup_or_example": "OR(1, 0, 1, 1) => 1", "lookup_or_summary": "Returns true if any of the values is true", "lookup_sum": "SUM", "lookup_sum_example": "SUM(1, 3, 5, \"\", \"Apple\") => 9 (1+3+5)", "lookup_sum_summary": "Returns the sum of all the values", "lookup_values": "Original values", "lookup_values_summary": "Returns the original values", "lookup_xor": "eXclusive OR operation", "lookup_xor_example": "XOR(1, 0, 1, 1) => 1", "lookup_xor_summary": "Returns true if an odd number of values are true", "loopkup_filter_pane_tip": "Change of column type selected by filter condition", "lt_person": "", "luxembourg": "Luxembourg", "macau": "Macau, China", "macedonia": "Macedonia", "madagascar": "Madagascar", "mail": "Email", "mail_invite_fail": "Mail invite success.", "mail_invite_success": "Mail invite success.", "main_admin_name": "Admin name", "main_admin_page_desc": "Admin have full access to the Space, such as managing members, managing Space settings.", "main_contain": "Main contents", "malawi": "Malawi", "malaysia": "Malaysia", "maldives": "Maldives", "mali": "Mali", "malta": "Malta", "managable_space_empty_tip": "It seems so empty here~Try to create a Space first.", "managable_space_list": "My Spaces", "manage_company_security": "Permissions and Security Page Settings", "manage_members": "Manage members", "manage_role_empty_btn": "Start using", "manage_role_empty_desc1": "You can freely create roles and add any teams / members to one or more roles.<a target=\"_blank\" href=\"${url}\"> Learn more</a>", "manage_role_empty_desc2": "For example, you can create a role named \"project manager\" and add all project managers in your organization to this role. In this way, you can directly select the role \"project manager\" in the member field or in the record comments and notify them.", "manage_role_empty_title": "Welcome to the Role feature", "manage_role_header_desc": "You can freely create roles and add any teams / members to one or more roles.<a target=\"_blank\" href=\"${url}\"> Learn more</a>", "manage_team": "Manage teams", "manage_template_center": "Manage templates", "manage_widget_center": "Manage widgets", "manage_workbench": "Manage workbench", "manual_save_view": "Save", "manual_save_view_error": "Oops, there's something wrong with the data synchronization, please refresh it", "manufacturing": "Manufacturing", "map_select_data": "Select a view as the data source", "map_select_label_field": "", "map_select_location_field": "", "map_select_location_type": "", "map_select_location_type_dd": "", "map_select_location_type_text": "", "map_select_location_type_url": "", "map_setting": "Map settings", "mark_all_as_processed": "Read all", "mark_notification_as_processed": "Read", "marketing": "Marketing", "marketing_and_sales": "Marketing and sales", "marketing_sub_title": "APITable brings your work tools together by integrating third-party applications. Integrations help connect your data and make APITable even more useful.", "marketplace_app_disable_succeed": "Disabled", "marketplace_app_enable_succeed": "Enabled", "marketplace_integration_app_dec_dingtalk": "<p>After installing the integration, please complete the space bundle, and after bundling, your team can use Table on DingTalk.</p>\n\n<p>With Table Assistant on DingTalk, you can:</p>\n\n<ul><li>Access Table's space and access space data directly on DingTalk without logging in;</li><li>Receive notifications from the space through DingTalk;</li><li>Set app visibility on DingTalk and automatically sync DingTalk's organization structure;</li></ul>", "marketplace_integration_app_dec_feishu": "<p>After installing the integration, please complete the space bundle, and after bundling, your team can use Table on Lark.</p>\n\n<p>With Table Assistant on Lark, you can:</p>\n\n<ul><li>Access Table's space and access space data directly on Lark without logging in;</li><li>Receive notifications from the space through Lark;</li><li>Set app visibility on Lark and automatically sync Lark's organization structure;</li></ul>\n", "marketplace_integration_app_dec_feishu_html_str": "<p>After installing the integration, please complete the space bundle, and after bundling, your team can use Table on Lark.</p>\n\n<p>With Table Assistant on Lark, you can:</p>\n\n<ul><li>Access Table's space and access space data directly on Lark without logging in;</li><li>Receive notifications from the space through Lark;</li><li>Set app visibility on Lark and automatically sync Lark's organization structure;</li></ul>\n", "marketplace_integration_app_dec_office_preview": "<p>Smooth online preview of office files on datasheet, allowing you to view common office files such as Excel, Word, PPT, etc. on your desktop or mobile anywhere, anytime</p>\n\n<ul>\n<li>Online preview of .doc, .docx, .xls, .xlsx, .ppt, .pptx, and .pdf formatted office files</li>\n<li>Desktop and mobile both support file previews of the above formats</li>\n</ul>\n\n<p>Additional notes:</p>\n<ul>\n<li>This feature is powered by \"Yoncentric Cloud Conversion\" and officially integrated</li>\n<li>Click the \"Enable\" button below to enable this integration and agree that \"Yoncentric Cloud Conversion\" reads the office files you want to preview</li>\n<li>If you no longer need the office files preview feature, the space admin can disable it on this page</li>\n</ul>", "marketplace_integration_app_dec_wechatcp": "<p>Using Table on WeCom, you can</p>\n\n<ul><li>Access Table's space and access space data directly on WeCom without logging in;</li><li>Receive notifications from the space through WeCom;</li><li>Set app visibility on WeCom and automatically sync WeCom's organization structure;</li></ul>", "marketplace_integration_app_info_dingtalk": "Allow you to log in to Table though DingTalk, receive your Space's notifications in DingTalk, and sync organizational chart from DingTalk", "marketplace_integration_app_info_fesihu": "Allow you to log in to Table through Lark, receive your Space's notifications in Lark, and sync organizational chart from Lark", "marketplace_integration_app_info_office_preview": "With this integration, you can easily preview Microsoft Office files in \"Attachment\" fields", "marketplace_integration_app_info_wecahtcp": "Allow you to log in to Table through WeCom, receive your Space's notifications in WeCom, and sync organizational chart from WeCom", "marketplace_integration_app_name_dingtalk": "DingTalk", "marketplace_integration_app_name_feishu": "Lark", "marketplace_integration_app_name_officepreview": "Preview Office Files", "marketplace_integration_app_name_wechatcp": "WeCom", "marketplace_integration_app_note_dingtalk": "Note: One Space can only bind one DingTalk self-built application", "marketplace_integration_app_note_feishu": "Note: One Space can only bind one Lark self-built application", "marketplace_integration_app_note_office_preview": "Note: Clicking on \"Authorize\" means you agree to authorize the service provider to read your Office files", "marketplace_integration_app_note_wechatcp": "*Note: Table stops offering WeCom self-built applications. But existing applications won't be influenced.", "marketplace_integration_btncard_appsbtntext_coming_soon": "Coming soon", "marketplace_integration_btncard_appsbtntext_read_more": "Learn more", "marketplace_integration_btncard_btntext_authorize": "Authorize", "marketplace_integration_btncard_btntext_open": "Enable", "martinique": "Martinique", "massive_template": "Start with templates", "matters_needing_attention": "Matters needing attention", "mauritania": "Mauritania", "mauritius": "Mauritius", "max_admin_nums": "The maximum number of sub-admin in space is ${specification}. So far, there are ${usage} admins, and you can upgrade to get higher usage.", "max_api_call": "The API usage of this space has reached the limit（ ${usage}/${specification} ）， and you can upgrade to get higher usage.", "max_archived_rows_per_sheet": "Operation failed. The number of archives has reached the upper limit. Please try to upgrade the space or delete some archived records.", "max_audit_query_days": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "max_calendar_views_in_space": "The maximum number of calendar views to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_capacity_size_in_bytes": "The maximum attachment capacity to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_consumption": "Maximum: ${count} calls/month", "max_form_views_in_space": "The maximum number of forms to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_gallery_views_in_space": "The maximum number of Gallery views to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_gantt_views_in_space": "The maximum number of Gantt views to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_kanban_views_in_space": "The maximum number of Kanban views to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_message_credits": "The maximum number of Al Credit to be used in AI agent is ${specification}. So far, ${usage} has been used, and you can upgrade to get highe r usage.", "max_mirror_nums": "The maximum number of mirror to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_record_num_per_dst": "The current limit for a single datasheet is 50000 rows", "max_records": "Maximum: ${count}", "max_remain_record_activity_days": "This space supports viewing record activity within ${specification} days, upgrade to view more ", "max_remain_timemachine_days": "The space only shows the record activity within ${specification} days, upgrade to view more record activity.", "max_remain_trash_days": "This space supports viewing historical file nodes within ${specification} days, upgrade to view more historical file nodes ", "max_rows_in_space": "The maximum number of records in space is ${specification}. So far, ${usage} records have been created, and you can upgrade to get higher usage.", "max_rows_per_sheet": "The maximum number of records in one datasheet is ${specification}. So far, ${usage} records have been created, and you can upgrade to get higher usage.", "max_seats": "The maximum number of seats in the space is ${specification}, and ${usage} are currently invited, and you can upgrade to get higher usage.", "max_sheet_nums": "The maximum number of file nodes to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "max_value": "Maximum", "maximum_concurrent_volume": "Maximum concurrent volume", "maybe_later": "Maybe Later", "mayotte": "Mayotte", "mbile_manual_setting_tip": "The view changes will only take effect for you", "media_element": "Media Element", "member": "Member", "member_applied_to_close_account": "<a class=\"nickName\"></a> has requested to delete his accoun", "member_data_desc_of_appendix": "All attachments uploaded to the Space are counted in. You can't upload any attachments once the storage reaches its limit.", "member_data_desc_of_dept_number": "Quantity statistics of all teams in the organizational structure (including sub-teams)", "member_data_desc_of_field_number": "Numbers of all files in the catalog & custom templates.", "member_data_desc_of_member_number": "'seats' refers to the number of members joined in the space and AI agents added in workbench.Which means：seats = members+ AI agents", "member_data_desc_of_record_number": "Quantity statistic in all datasheets of working catalog, including the blank record(s)", "member_err": "Nickname cannot exceed 32 characters", "member_family_name": "Name", "member_info": "Members", "member_information_configuration": "Member setting", "member_list_review": "Preview member list", "member_name": "Name", "member_not_exist": "Member does not exist", "member_per_space": "Space members", "member_preview_list": "Preview member list", "member_quit_space": "<a class=\"memberName\"></a> left the \"<a class=\"spaceName\"></a>\" Space.", "member_setting_invite_member_describle": "Can invite new members to the Space", "member_setting_mobile_show_describe": "Show all members' phone numbers in contacts", "member_status_removed": "Removed", "member_type_field": "Columns of member type.", "members_setting": "Members & Teams", "mention": "Mention", "menu_archive_multi_records": "Archive ${count} records", "menu_archive_record": "Archive record", "menu_copy_record_url": "Copy record URL", "menu_delete_multi_records": "Delete ${count} records", "menu_delete_record": "Delete record", "menu_duplicate_record": "Duplicate record", "menu_expand_record": "Expand record", "menu_insert_record": "Insert record ${direction}", "menu_insert_record_above": "Insert${lineCount}above", "menu_insert_record_below": "Insert${lineCount}below", "message_bind_email_succeed": "Email address changed successfully", "message_call_sharing_function_with_browser": "Please call the sharing function of your browser", "message_can_not_associated_because_of_no_editable": "You're not allowed to edit with it", "message_can_not_associated_because_of_no_manageable": "You're not allowed to manage with it", "message_change_phone_successfully": "Phone number changed", "message_code": "Send", "message_coping": "Pasting...", "message_copy_failed": "Paste failed", "message_copy_failed_reasoned_by_wrong_type": "Can't fill the pasted content into this type of field", "message_copy_link_failed": "Co<PERSON> failed", "message_copy_link_successfully": "<PERSON>pied", "message_copy_succeed": "Pasted", "message_delete_template_successfully": "Template deleted", "message_display_count": "${count} new message(s)", "message_exit_space_failed": "Failed to exit the space", "message_exit_space_successfully": "You've exited the space", "message_fields_count_up_to_bound": "The datasheet has reached the upper limit of the field. Unable to create related relations.", "message_file_size_out_of_upperbound": "The size of a single attachment cannot exceed 1G", "message_get_node_share_setting_failed": "Failed to get the sharing settings of the file node", "message_hidden_field_desc": "Field descriptions have been hidden", "message_img_size_limit": "The size of the picture should not exceed ${size}", "message_invalid_url": "Invalid address", "message_invite_member_to_validate": "The invited member(s) will join the space after they verify the invitation email.", "message_member_name_modified_failed": "Failed to edit member name", "message_member_name_modified_successfully": "Member name edited", "message_node_data_sync_failed": "Failed to synchronize node data", "message_preparing_to_download": "Preparing to download resources...", "message_send_invitation_email_to_member": "The invitation email has been sent to ${invitedCount} member(s).", "message_set_password_succeed": "Password set successfully", "message_shown_field_desc": "Field descriptions have been shown", "message_upload_img_failed": "Image upload failed", "message_verification_code_empty": "Empty verification code", "mexico": "Mexico", "miniprogram_code_fail": "Invalid mini-program code. Please click to refresh.", "minute": "minute", "minutes_of_count": "${count} minutes ago", "mirror": "Mirror", "mirror_editor_label": "In addition to \"Update-only\", can also add or delete views and delete records", "mirror_from": "Mirror from: ", "mirror_help_url": "https://help.aitable.ai/docs/manual-mirror", "mirror_manager_label": "In addition to \"Editor\", can also configure mirror", "mirror_reader_label": "Can read data or comment on the mirror", "mirror_resource_dst_been_deleted": "The original datasheet from which the current mirror was generated has been deleted and cannot be accessed further", "mirror_resource_view_been_deleted": "Failed to access this mirror because the original view was deleted", "mirror_show_hidden_checkbox": "Show all fields in Mirrors", "mirror_uploader_label": "Can read, add and edit records except deleting records", "missing_instruction_op_error": "An error occurred while submitting data, please refresh and retry, if you have any questions, please contact us for help", "mobile_and_account_login": "Log in with password", "mobile_function_over_limit_tip": "Available after upgrade", "mobile_show_allow": "Show phone numbers", "mobile_show_configuration_desc": "When on, the phone book will show the phone numbers of all members", "mobile_space_list_tip": "For more \"Space Management\" settings, please access the web app on a computer", "mobile_usage_over_limit_tip": "Usage over limits. Please go to the PC to upgrade.", "modal_content_confirm_revoke_logout": "Are you sure you want to recover your account?", "modal_content_policy": "Thank you for using Table's services. The following terms will help you understand Table's user and privacy policies and the rights you have with respect to them.\nYou can access the full content via ${content}.", "modal_normal_err_text": "Message error, the transmission failed.", "modal_normal_sub_title": "I am a subtitle that cannot be transmitted", "modal_normal_title": "Normal operation box", "modal_privacy_ok_btn_text": "I have read and agree", "modal_res_title": "Result prompt box", "modal_restore_space": "All data from this space will be restored to its previous state", "modal_select_title": "Select modal window", "modal_sub_title": "I am a subtitle", "modal_title": "Modal window", "modal_title_bind_email": "Update your email address", "modal_title_check_mail": "Verify your email", "modal_title_check_new_phone": "Verify new mobile phone number", "modal_title_check_original_mail": "Verify your email", "modal_title_check_original_phone": "Verify your phone number", "modal_title_modify_nickname": "Edit", "modal_title_privacy": "Please read and agree to the User Agreement", "modal_verify_admin_email": "Verify the admin's email", "modal_verify_admin_phone": "Verify the admin's phone number", "modify": "Modify", "modify_field": "Edit field settings", "moldova": "Moldova", "monaco": "Monaco", "mongolia": "Mongolia", "montenegro": "Montenegro", "month": "Month", "month_day": "Month-Day", "montserrat": "Montserrat", "more_color": "More color", "more_data_to_come": "More statistics coming...", "more_education": "More", "more_invite_ways": "More ways to invite", "more_login_mode": "Other login options", "more_member_count": "+ ${count}", "more_setting": "More", "more_setting_tip": "Managing templates means that the sub-admin can delete space templates; Managing widgets means that the sub-admin can unpublish space widgets from the Widget Center and transfer ownership of space widgets", "more_template": "More", "more_tips": "More...", "more_widget": "More widgets", "morocco": "Morocco", "move": "Move", "move_datasheet_link_warn": "Cannot be moved to Team Area alone, because there is a two-way link from the current datasheet to another datasheet, it is recommended to put them in a folder and then move them", "move_favorite_node_fail": "Node moving failed. The system will update the list automatically. ", "move_folder_link_warn": "Cannot be moved to Team Area because the files in the current folder are linked to files outside the folder (may be two-way links or linked by forms, etc.) We recommend that they be placed in a folder before moving", "move_node_modal_content": "After moving, the visibility of the file node may be affected by the parent folder.", "move_other_link_warn": "It is not allowed to move ${node type} to Team Area alone, because it connects to the datasheet of Private Area, it is recommended to put them in a folder before moving them", "move_to": "Move to", "move_to_error_equal_parent": "The file node is under the current folder. Please select another folder", "move_to_modal_title": "Move [${name}] to", "move_to_success": "Move succeeded", "mozambique": "Mozambique", "multilingual_mail": "Chinese and English mail", "must_one_date": "Pick at least one Date field", "my_permissions": "My Permissions", "myanmar": "Myanmar", "name": "Name", "name_length_err": "The name must be between 1 and 100 characters in length", "name_not_rule": "name_not_rule", "name_repeat": "Duplicate name", "namibia": "Namibia", "nav_me": "me", "nav_space_settings": "Settings", "nav_team": "Contacts", "nav_templates": "Templates", "nav_workbench": "Workbench", "navigation_activity_tip": "Upgrade for free", "nepal": "Nepal", "netherlands": "Netherlands", "network_icon_hover_connected": "Online", "network_icon_hover_data_synchronization": "Syncing data...", "network_icon_hover_disconnected": "Network disconnected", "network_icon_hover_reconnection": "Reconnecting...", "network_state_disconnection": "Network disconnected", "new_a_line": "Shift+Enter: break line", "new_automation": "New automation", "new_caledonia": "New Caledonia", "new_custom_page": "New custom page", "new_datasheet": "New datasheet", "new_folder": "New folder", "new_folder_btn_title": "Folder", "new_folder_tooltip": "Create folder", "new_from_template": "Create from template", "new_mian_admin": "New admin", "new_mirror": "New mirror", "new_node_btn_title": "Add", "new_node_tooltip": "Create folders, datasheets, forms and more", "new_something": "New", "new_something_tips": "Create Something New", "new_space": "Create a New Space", "new_space_is_unactived": "The amount of your Space has reached the maximum of 10. The current invited Space will remain in inactive status and unable to access.", "new_space_tips": " ", "new_space_widget_notify": "<a class=\"memberName\"></a> published a custom widget \"<a class=\"widgetName\"></a>\" in \"<a class=\"spaceName\"></a>\"", "new_user_turn_to_home": "If you have not signed up yet, click <a href=\"https://u.Dolphin.cn/qmdp3\" target=\"_blank\"> \"sign up\" </a> and bind the account", "new_user_welcom_notify": "Welcome to APITable, we have prepared a free meeting gift for you to learn APITable, click on this message to jump to learn more.", "new_user_welcome_notify": "Welcome to APITable. We have prepared a free meeting gift for you to learn APITable. Click here to learn more.", "new_view": "Add view", "new_zealand": "New Zealand", "next_page": "Next page", "next_record": "Move to the next record", "next_record_plain": "Next record", "next_record_tips": "Next record (${next_record})", "next_step": "Next", "next_step_add_actions": "Your next step should be to add one or more Actions, defining what specifically should happen when the button is clicked", "nicaragua": "Nicaragua", "nickname": "Nickname", "nickname_in_space": "Name", "nickname_in_space_short": "Name", "nickname_modified_successfully": "Name edited", "niger": "Niger", "nigeria": "Nigeria", "no_access_node": "The file node is hidden", "no_access_record": "No access", "no_access_space_descirption": "Unable to access the Space. You might not have the permission or the Space does not exist.", "no_access_space_title": "Note", "no_access_view": "You have no access to the original datasheet", "no_and_thanks": "No, thanks", "no_automation_node": "There is no automation node in the current space. Learn how to create a new one", "no_comment_tip": "This record has no activity yet", "no_cover": "No cover", "no_data": "No data", "no_datasheet_editing_right": "You do not have manageable access to the original datasheet to which the current form is linked and cannot insert fields", "no_editing_rights": "No editing permission", "no_field_role": "You have no access to the field", "no_file_permission": "The file node cannot be accessed", "no_file_permission_content": "You do not have access to the file node or the file node has been deleted", "no_file_permission_message": "The file node's permissions have been changed and you cannot access it.", "no_foreignDstId": "Please link to a datasheet", "no_foreign_dst_readable": "No permission to view the related datasheet", "no_link_ds_permission": "No edit permissions for related datasheets", "no_lookup_field": "Please select a field to look up", "no_match_tip": "Sorry, we can't find the page you are looking for", "no_member_ds_permission": "You can't edit this record outside the Space", "no_more": "End", "no_next_record_tips": "No next record", "no_notification": "No notifications", "no_numerical_field": "no numerical field", "no_option": "No options", "no_permission": "No permission", "no_permission_access": "No permission to access", "no_permission_add_option": "No permission to add new options", "no_permission_add_widget": "No permission to install widgets", "no_permission_add_widget_panel": "No permission to add widget board", "no_permission_create_widget": "No permission to create widget", "no_permission_edit_value": "No permission to modify the data", "no_permission_img_url": "/space/2022/04/07/e277ea54e8cd4a4b8f13f786e352517d?attname=English.png", "no_permission_search_look_field": "No permission to search datasheet", "no_permission_select_view": "You don't have the permission to view the related datasheet, you can't specify a view", "no_permission_setting": "You don't have the permission to do this action", "no_permission_setting_admin": "No permission to modify the permission of \"<PERSON><PERSON>\"", "no_permission_tips_description": "Sorry, you are not permitted to operate this. Please refresh if you have updated your permission.", "no_permission_tips_title": "Note", "no_permission_to_edit_datasheet": "No permission to edit this related datasheet", "no_previous_record_tips": "No previous record", "no_redemption_code_entered": "Enter a redeem code", "no_sapce_save": "There is no space  to save", "no_search_field": "No matches that contain \"${keyword}\"", "no_search_result": "No results", "no_selected_record": "No selected records", "no_step_summary": "Triggers and actions have not been added", "no_support_mobile_desc": "We're trying out best to work on the mobile terminal. Please run this on computer~", "no_support_pc_desc": "Sorry, this browser is not supported yet.", "no_support_pc_sub_desc": "These 4 browsers are recommended: Chrome, Firefox, Safari, and Microsoft Edge", "no_view_create_form": "Can't create a form without a Grid view", "no_view_find": "No results", "node_info": "File node information", "node_info_created_time": "Created time:", "node_info_createdby": "Created by:", "node_info_last_modified_by": "Last modified by:", "node_info_last_modified_time": "Last modified time:", "node_name": "Node name", "node_not_exist_content": "The file node you are accessing has no permissions or has been deleted", "node_not_exist_title": "Note", "node_number_err_content": "Your action failed because the Space has reached the maximum number of file nodes", "node_permission": "File node permissions", "node_permission_desc": "The sub-admin will have the access to manage workbench, and to set \"manageable\", \"editable\", and \"read-only\" permissions for other members.", "node_permission_extend_desc": "Permissions inherit from the parent folder", "node_permission_has_been_changed": "Your permission has been changed to \"${nodeRoleName}\"", "node_permission_item_tips_admin_he": "The administrator of the space who can manage this file node", "node_permission_item_tips_admin_you": "You are the administrator of the space and can manage this file node", "node_permission_item_tips_file_he": "The file node manager who can manage this file node", "node_permission_item_tips_file_you": "You are the file node manager and can manage this file node", "node_permission_item_tips_other_he": "The ${role} of this file node", "node_permission_item_tips_other_he_edit": ", you can click the menu on the right to modify or remove permission", "node_permission_item_tips_other_you": "You are the ${role} of this file node", "node_permission_label_space_and_file": "Space & File Node Manager", "node_permission_nums": "The maximum number of node permissions to be used in space is ${specification}. So far, ${usage} has been used, and you can upgrade to get higher usage.", "node_with_link_share_reminder": "The current datasheet is associated with the contents of other datasheet. Do you want to continue sharing?", "node_with_link_share_view_reminder": "Note: The shared datasheet has data linked from another datasheet", "nodes_per_space": "Nodes per Space", "nonprofit": "Nonprofit", "nonprofits_and_volunteering": "Nonprofits and volunteering", "nonsupport_video": "Sorry, your browser does not support embedded video.", "norway": "Norway", "not_available": "None", "not_equal": "is not...", "not_found_field_the_name_as": "Field named \"${value}\" unfound.", "not_found_record_contains_value": "No matches that contain \"${searchValueSpan}\" ", "not_found_this_file": "The file node you are searching for is unfound", "not_found_Dolphin_field_the_name_as": "Column named  \"${value}\" unfound", "not_joined_members": "Unactive members", "not_mail_invitee_page_tip": "The current login user is not invitee (${text}). <br/>Please select the corresponding account to enter the space station.", "not_shared": "Sharing is disabled", "not_shared_tip": "After turning this on, user(s) outside the Space can view content you shared via the public links.", "not_support_yet": "Not supported for this moment", "notes_delete_the_view_linked_to_form": "A form has been created from this view. Deleting it will make the form become invalid. Are you sure you want to delete \"${view_name}\"?", "notes_delete_the_view_linked_to_mirror": "This view has mirrors that others might be working on. Deleting it will make the mirrors no longer available. Are you sure you want to continue?", "notification_center": "Notifications", "notification_center_tab_all": "All (${count} )", "notification_center_tab_comment": "I was mentioned (${count} )", "notification_center_tab_member_field": "Assigned to me (${count} )", "notification_center_tab_unread": "Unread (${count} )", "notification_delete_record_by_count": "${count} record(s) deleted", "notification_record_mention": "${user} mentioned you in ${record}", "notification_space_name_changed": "The \"<a class=\"oldSpaceName\"></a>\" Space's name was changed to \"<a class=\"newSpaceName\"></a>\".", "notified_assign_permissions_number": "and ${number} others of people ", "notified_assign_permissions_text": "granting permissions", "notify_creator_when_there_is_an_error_occurred": "Notify creator when there is an error occurred", "notify_time_zone_change_content": "The current client time zone is ${client_time_zone}, which is different from the user time zone ${user_time_zone}, update to the client time zone?", "notify_time_zone_change_desc": "The time zone has been updated to ${time_zone}", "notify_time_zone_change_title": "Detected that the time zone has changed", "notify_type_datasheet": "notify_type_datasheet", "notify_type_member": "Member notification", "notify_type_node": "node notification", "notify_type_space": "space_notify", "notify_type_system": "system_notify", "null": "[None]", "number_cell_input_tips": "Please enter a number (positive or negative), decimal, or e (mathematical constant)", "number_field_configuration_symbol": "Custom unit", "number_field_format": "Precision", "number_field_symbol_placeholder": "Enter a unit of measurement", "number_of_records": "${number}${span}", "number_of_records_unit": " ", "number_of_rows": "Number of record", "number_of_something": "${count}${span}", "number_of_something_unit": " ", "number_of_team": "Total teams", "number_of_trigger_is_full": "The number of triggers for this automation node is full", "numeric_functions": "Numeric Function", "nvc_err_300": "Oops, something went wrong. Click <a href=\"javascript:__nc.reset()\"> to refresh </a> and try again.", "nvc_err_network": "The network is unstable. Please <a href=\"javascript:__nc.reset()\">click to refresh</a>.", "nvc_start_text": "Drag the bar to the right end", "nvc_yes_text": "Verified", "obtain_verification_code": "Verification code not obtained or expired", "offical_website_footer_nav_data": "[{\n  \"title\": \"产品\",\n  \"lists\": [{\n    \"name\": \"快速入门\",\n    \"url\": \"http://help.dolphintable.suanlihaiyang.com/docs/tutorial-1-quick-start\"\n  }, {\n    \"name\": \"产品指南\",\n    \"url\": \"http://help.dolphintable.suanlihaiyang.com/docs/manual-1-what-is-Table\"\n  }, {\n    \"name\": \"产品价格\",\n    \"url\": \"/pricing/\"\n  }, {\n    \"name\": \"产品路线图\",\n    \"url\": \"https://Dolphin.cn/share/shrvp76H9lQBFLaRG1wSY/dstB1RtGAw5qMoqnnY/viw9giKo0IcT5\"\n  }]\n}, {\n  \"title\": \"关于我们\",\n  \"lists\": [{\n    \"name\": \"公司介绍\",\n    \"url\": \"/company/\"\n  }, {\n    \"name\": \"加入我们\",\n    \"url\": \"/join-us/\"\n  }, {\n    \"name\": \"媒体报道\",\n    \"url\": \"/press/\"\n  }, {\n    \"name\": \"表格课堂\",\n    \"url\": \"https://edu.Dolphin.cn/\"\n  }, {\n    \"name\": \"海豚合伙人\",\n    \"url\": \"/partners/\"\n  }]\n}, {\n  \"title\": \"解决方案\",\n  \"lists\": [{\n    \"name\": \"PMO项目群管理\",\n    \"url\": \"/business-pmo/\"\n  }, {\n    \"name\": \"智慧电商运营\",\n    \"url\": \"/ecommerce/\"\n  }, {\n    \"name\": \"CRM客户管理\",\n    \"url\": \"/crm/\"\n  }, {\n    \"name\": \"HR人力资源管理\",\n    \"url\": \"/hr/\"\n  }, {\n    \"name\": \"Scrum敏捷开发管理\",\n    \"url\": \"/scrum/\"\n  }, {\n    \"name\": \"营销策划与市场运营\",\n    \"url\": \"/marketing/\"\n  }, {\n    \"name\": \"OKR目标管理\",\n    \"url\": \"/okr/\"\n  }, {\n    \"name\": \"教育培训管理\",\n    \"url\": \"/education/\"\n  }, {\n    \"name\": \"智慧门店管理\",\n    \"url\": \"/shop/\"\n  }]\n}, {\n  \"title\": \"支持\",\n  \"lists\": [{\n    \"name\": \"意见反馈\",\n    \"url\": \"https://Dolphin.cn/share/shrCvbFC53xc3kl00B4Pg\"\n  }, {\n    \"name\": \"帮助中心\",\n    \"url\": \"http://help.dolphintable.suanlihaiyang.com/\"\n  }, {\n    \"name\": \"开发者中心\",\n    \"url\": \"/developers/\"\n  }, {\n    \"name\": \"服务条款\",\n    \"url\": \"/service-agreement/\"\n  }, {\n    \"name\": \"隐私协议\",\n    \"url\": \"/service-agreement/\"\n  }, {\n    \"name\": \"安全与合规\",\n    \"url\": \"/security/\"\n  }]\n}, {\n  \"title\": \"服务\",\n  \"lists\": [{\n    \"name\": \"加入社群\",\n    \"url\": \"/chatgroup/\"\n  }, {\n    \"name\": \"Github开源\",\n    \"url\": \"https://github.com/Dolphindata\"\n  }, {\n    \"name\": \"预约演示\",\n    \"url\": \"https://Dolphin.cn/share/shrlRw3YWmqZ4BMl0B7qZ/fomUMtKMblNchCG7Ef\"\n  }, {\n    \"name\": \"专有云部署\",\n    \"url\": \"https://Dolphin.cn/share/shrVrGPclBql6w9ysUHzR/fomed5397fFJfdcRvL\"\n  }, {\n    \"name\": \"应用连接\",\n    \"url\": \"/connections/\"\n  }]\n}, {\n  \"title\": \"联系我们\",\n  \"lists\": [{\n    \"name\": \"地址：深圳市南山区国信投资大厦1710\",\n    \"url\": \"https://ditu.amap.com/place/B0FFJ14BJ7\"\n  }, {\n    \"name\": \"售前咨询：点击联系商务\",\n    \"url\": \"https://Dolphin.cn/share/shrlRw3YWmqZ4BMl0B7qZ/fomUMtKMblNchCG7Ef\"\n  }, {\n    \"name\": \"合作：<EMAIL>\",\n    \"url\": \"mailto:<EMAIL>\"\n  }, {\n    \"name\": \"媒体：<EMAIL>\",\n    \"url\": \"mailto:<EMAIL>\"\n  }, {\n    \"name\": \"招聘：<EMAIL>\",\n    \"url\": \"mailto:<EMAIL>\"\n  }]\n}]", "offical_website_nav_data": "[{\n  \"name\": \"产品\",\n  \"href\": \"/login\",\n  \"children\": [\n    {\n      \"name\": \"Dolphin表格 Overview\",\n      \"href\": \"/login/\"\n    },\n    {\n      \"name\": \"应用连接 Integration\",\n      \"href\": \"/connections/\"\n    },\n    {\n      \"name\": \"客户端（Beta） Download\",\n      \"href\": \"/download/\"\n    }\n  ]\n}, {\n  \"name\": \"解决方案\",\n  \"href\": \"/\",\n  \"children\": [{\n    \"name\": \"PMO项目群管理\",\n    \"href\": \"/business-pmo/\"\n  }, {\n    \"name\": \"智慧电商运营\",\n    \"href\": \"/ecommerce/\"\n  }, {\n    \"name\": \"CRM客户管理\",\n    \"href\": \"/crm/\"\n  }, {\n    \"name\": \"HR人力资源管理\",\n    \"href\": \"/hr/\"\n  }, {\n    \"name\": \"Scrum敏捷开发管理\",\n    \"href\": \"/scrum/\"\n  }, {\n    \"name\": \"营销策划与市场运营\",\n    \"href\": \"/marketing/\"\n  }, {\n    \"name\": \"OKR目标管理\",\n    \"href\": \"/okr/\"\n  }, {\n    \"name\": \"教育培训管理\",\n    \"href\": \"/education/\"\n  }, {\n    \"name\": \"智慧门店管理\",\n    \"href\": \"/shop/\"\n  }, {\n    \"name\": \"更多解决方案 >\",\n    \"href\": \"/solutions/\"\n  }]\n}, {\n  \"name\": \"模板/案例\",\n  \"href\": \"/template\",\n  \"children\": [\n    {\n      \"name\": \"模板中心 Tempaltes\",\n      \"href\": \"/template\"\n    },\n    {\n      \"name\": \"客户案例 Customers\",\n      \"href\": \"/cases/\"\n    }\n  ]\n},\n  {\n    \"name\": \"首页\",\n    \"href\": \"/?home=1\"\n  },\n  {\n    \"name\": \"学习资源\",\n    \"href\": \"https://edu.Dolphin.cn/index\",\n    \"children\": [\n      {\n        \"name\": \"快速入门 Tutorials\",\n        \"href\": \"http://help.dolphintable.suanlihaiyang.com/docs/tutorial\"\n      },\n      {\n        \"name\": \"产品手册 Manual\",\n        \"href\": \"http://help.dolphintable.suanlihaiyang.com/docs/manual\"\n      },\n      {\n        \"name\": \"海豚课堂 Dolphin Education\",\n        \"href\": \"https://edu.Dolphin.cn/\"\n      },\n      {\n        \"name\": \"社区 Dolphin BBS\",\n        \"href\": \"https://bbs.Dolphin.cn/\"\n      },\n      {\n        \"name\": \"第三方连接 Integration\",\n        \"href\": \"http://help.dolphintable.suanlihaiyang.com/docs/connection\"\n      },\n      {\n        \"name\": \"开发者中心 Developer Center\",\n        \"href\": \"/developers/\"\n      },\n      {\n        \"name\": \"常见问题 FAQ\",\n        \"href\": \"http://help.dolphintable.suanlihaiyang.com/docs/questions\"\n      }\n    ]\n  }]", "office_preview": "Preview Office Files", "office_preview_app_desc": "<p>Smooth online preview of office files on datasheet, allowing you to view common office files such as Excel, Word, PPT, etc. on your desktop or mobile anywhere, anytime</p>\n\n<ul>\n<li>Online preview of .doc, .docx, .xls, .xlsx, .ppt, .pptx, and .pdf formatted office files</li>\n<li>Desktop and mobile both support file previews of the above formats</li>\n</ul>\n\n<p>Additional notes:</p>\n<ul>\n<li>This feature is powered by \"Yoncentric Cloud Conversion\" and officially integrated</li>\n<li>Click the \"Enable\" button below to enable this integration and agree that \"Yoncentric Cloud Conversion\" reads the office files you want to preview</li>\n<li>If you no longer need the office files preview feature, the space admin can disable it on this page</li>\n</ul>", "office_preview_app_intro": "With this integration, you can easily preview Microsoft Office files in \"Attachment\" fields", "office_preview_app_notice": "* Note: Clicking on \"enable\" means you agree to authorize the service provider to read your Office files", "official_account_qrcode": "APITable", "official_adjustment": "Official adjustment", "official_gift": "Official gift", "official_invitation_code": "Official Invitation Code", "official_invitation_code_desc1": "Scan the code on WeChat and follow the official account below to get ${text}", "official_invitation_code_desc2": "After use, you can get ${text} reward", "official_invitation_reward": "Official invitation reward", "official_invite_code_tip": "To get the official invitation code: reply \"Dolphin\" to the official WeChat account of \"dolphindata\".", "official_mode": "Method 2: the official invitation code", "official_name": "APITable", "official_template": "Recommended", "official_template_centre_description": "Recommended by APITable", "official_website": "Official website", "official_website_partners": "[{\n  \"name\": \"五源资本\",\n  \"url\": \"https://www.5ycap.com/\"\n}, {\n  \"name\": \"IDG资本\",\n  \"url\": \"https://cn.idgcapital.com/\"\n}, {\n  \"name\": \"天图资本\",\n  \"url\": \"http://www.tiantu.com.cn/\"\n}, {\n  \"name\": \"惟客数据\",\n  \"url\": \"https://wakedata.com/\"\n}, {\n  \"name\": \"方云智能\",\n  \"url\": \"https://www.farcloud.com/\"\n}, {\n  \"name\": \"SegmentFault思否\",\n  \"url\": \"https://segmentfault.com/\"\n}, {\n  \"name\": \"字符科技\",\n  \"url\": \"http://www.alphabets.cn/\"\n}, {\n  \"name\": \"ones.ai\",\n  \"url\": \"https://ones.ai/\"\n}, {\n  \"name\": \"集简云\",\n  \"url\": \"https://jijyun.cn/\"\n}, {\n  \"name\": \"粤湾商盟\",\n  \"url\": \"http://www.gbabusiness.cn/\"\n},{\n  \"name\": \"语鹦企服\",\n  \"url\": \"https://crm.bytell.cn/\"\n}, {\n  \"name\": \"ShowMeBug\",\n  \"url\": \"https://www.showmebug.com/\"\n}, {\n  \"name\": \"flomo 浮墨笔记\",\n  \"url\": \"https://flomoapp.com/\"\n}, {\n  \"name\": \"高瓴资本\",\n  \"url\": \"https://www.hillhousecap.com/zh-hans/\"\n}, {\n  \"name\": \"靖亚资本\",\n  \"url\": \"http://www.emventures.cn/\"\n}, {\n  \"name\": \"AdsPower指纹浏览器\",\n  \"url\": \"https://www.adspower.net/\"\n}, {\n  \"name\": \"微伴助手\",\n  \"url\": \"https://weibanzhushou.com/\"\n}, {\n  \"name\": \"Tapdata\",\n  \"url\": \"https://tapdata.net/\"\n}, {\n  \"name\": \"CodeFun\",\n  \"url\": \"https://code.fun/\"\n}]", "official_website_without_abbr": "Official website", "og_keywords_content": "APITable, big data, digitization, digital transformation, data center, business center, data assets, digital smart office, remote office, data workbench, blockchain, artificial intelligence, multidimensional tables, aPaaS, hpaPaaS, RAD, database applications, rapid development tools", "og_page_title": " APITable ", "og_product_description_content": "APITable, building block multimedia datasheet, pioneer of multidimensional tables technology, data sorting artifact, making everyone a data designer", "og_site_name_content": "APITable", "okay": "OK", "old_user_modal_desc": "", "old_user_turn_to_home": "Already have an account? <a href=\"/login\">Log in</a>", "oman": "Oman", "one_month": "1 month", "one_way_link_data_error": "Data in the related datasheet error", "one_year": "1 year", "online_custome_service": "Online customer service", "online_edition": "Online", "online_video_training": "Online video training", "open": "Open", "open_api_panel": "Open API panel", "open_auto_save_success": "Autosave view is turned on successfully", "open_auto_save_warn_content": "All changes under this view are automatically saved and synchronized with other members.", "open_auto_save_warn_title": "Turn on autosave view", "open_automation_time_arrival": "https://aitable.ai/contact-sales-open-source", "open_failed": "Open failed", "open_in_new_tab": "open in a new tab", "open_invite_after_operate": "Once switched on, all members can invite new members from the contacts panel", "open_keyboard_shortcuts_panel": "Open shortcuts", "open_node_permission": "Enable node permissions", "open_node_permission_content": "To enable field permissions, you need to specify the permissions of the current node first.", "open_node_permission_label": "Give permissions manually", "open_public_link": "Create public link", "open_quickgo_panel": "Open search bar in workbench", "open_share_edit": "Turn on permission for sharing and editing", "open_url": "Open URL", "open_url_emby_warning": "URL cannot be empty", "open_url_formula_emby_warning": "Formula can not be empty", "open_url_tips_formula": "Please enter formula", "open_url_tips_string": "Please enter URL", "open_url_tips_switch": "Switch to ${INPUT_MODE}", "open_view_filter": "Open filter setting", "open_view_group": "Open group setting", "open_view_hidden": "Open field settings", "open_view_sort": "Open sort setting", "open_view_sync_tip": "", "open_workbench": "Open working catalog", "operate": "Operations", "operate_fail": "Action failed", "operate_info": "Note", "operate_success": "Action done", "operate_warning": "Action warning", "operated": "Operated", "operation": "Operations", "operation_log_allow_other_save": "Others can save the file node to their own Space", "operation_log_closed_share": "Sharing disabled", "operation_log_not_allow_other_save": "Others can't save the file node to their own Space", "operation_log_open_share": "Sharing enabled", "operation_log_update_share": "Share link updated", "operations": "Operations", "operator_and": "And", "operator_or": "Or", "option_configuration_advance_palette": "Advanced color board", "option_configuration_basic_palette": "Basic", "option_configuration_limited_time_free": "Limited Free", "option_configuration_silver_palette": "Silver plan exclusive", "option_name_repeat": "Duplicate option", "or": "or", "order_price": "Order Price", "ordered_list": "Numbered list", "ordinary_members": "Member", "ordinary_members_setting": "Manage members", "org_chart_can_not_drop": "Cannot drop to this record card", "org_chart_choose_a_link_field": "Select a Link field as a sublevel", "org_chart_choose_a_self_link_field": "You can only select a Link field that links to the current datasheet", "org_chart_collapse_node": "Collapse all record cards below", "org_chart_collapse_node_disabled": "The operation cannot be executed", "org_chart_controls_close_menu": "Close pending record list", "org_chart_controls_fit_view": "Adaptive", "org_chart_controls_open_menu": "Open pending record list", "org_chart_controls_toggle_full": "Full Screen", "org_chart_controls_zoom_in": "Zoom in", "org_chart_controls_zoom_out": "Zoom out", "org_chart_controls_zoom_reset": "Reset to 100%", "org_chart_copy_record_url": "Copy record URL", "org_chart_create_a_node_copy": "Duplicate record card", "org_chart_create_a_node_copy_disabled": "The operation cannot be executed", "org_chart_cycle_button_tip": "Click to delete the link between records", "org_chart_del_link_relationship": "Delete link", "org_chart_delete_disabled": "The operation cannot be executed", "org_chart_drag_clear_link": "Dragging to the pending records area will clear the original link", "org_chart_err_head": "Failed to render", "org_chart_err_title": "The following records create a circular link, in which case the architecture can't render successfully. Please change the link", "org_chart_expand_node": "Expand all record cards below", "org_chart_expand_record": "Expand the record card", "org_chart_init_fields_button": "Create a Two-way Link field", "org_chart_init_fields_desc": "All records linked by the field will become children (i.e. subordinates) of the current record", "org_chart_init_fields_no_permission_desc": "You need to create at least one Link field that links to the current datasheet to generate an Architecture view", "org_chart_init_fields_no_permission_title": "No permission to create Two-way Link", "org_chart_init_fields_title": "You need a Link field to determine the hierarchy", "org_chart_insert_into_child": "Insert record card below", "org_chart_insert_into_child_disabled": "The operation cannot be executed", "org_chart_insert_into_parent": "Insert record card above", "org_chart_insert_into_parent_disabled": "The operation cannot be executed", "org_chart_layout_horizontal": "Horizontal layout", "org_chart_must_have_a_link_field": "Select a Link field that links to the current datasheet", "org_chart_pick_link_field": "Select a Link field", "org_chart_play_guide_video_title": "How to use an Architecture view", "org_chart_please_click_button_to_create_a_node": "No records yet. Click the button below to create one first", "org_chart_please_drag_a_node_into_canvas": "Please drag a record from the right to the graphic area to create the first record card", "org_chart_please_drag_a_node_into_canvas_if_list_closed": "Open the record list and drag a record from the right to the graphic area to create the first record card", "org_chart_record_list": "Pending records", "org_chart_setting": "Settings", "org_chart_setting_field_deleted": "The field was deleted", "org_chart_setting_field_invalid": "Please select a Link field that links to the current datasheet", "org_chart_tip_drag_node_insert": "Release the mouse node \"${dragRecordName}\" will be inserted to the ${direction} of the node \"${targetRecordName}", "org_chart_tip_drag_node_merge": "The node \"${dragRecordName}\" will become a child of the node \"${targetRecordName}\" when the mouse is released", "org_chart_view": "Architecture", "org_guide_desc": "Easier to build organizations and business structure, and easier to adjust quickly", "organization_and_role": "Organization", "orgin_plan_discount": "Original Program Discounts", "origin_price": "price", "other_equitys": "Other", "other_equitys_desc": "Depending on the subscription plan of the space.There will be different restrictions on other benefits", "other_invitation_rule": "Other ways to invite members", "other_login": "Other login options", "other_view_desc": "Depending on the subscription plan of the space.There are different restrictions on the creation of advanced views", "other_views": "Advanced Views", "owner": "${node_type} admin", "page_not_found_tip": "Sorry, we can't find the page you are looking for", "page_timeout": "The page is timeout. Please refresh.", "page_to_down_edge": "Scroll to the bottom", "page_to_up_edge": "Scroll to the top", "pagination_component_jump": "Go to", "pagination_component_page": "page", "pagination_component_page_size": "${val}/page", "pagination_component_total": "${start}-${end} of ${total} items", "paid_edition": "Paid", "paid_subscription": "paid subscription", "pakistan": "Pakistan", "palau": "<PERSON><PERSON>", "palestine": "Palestine", "panama": "Panama", "papua_new_guinea": "Papua New Guinea", "paragraph": "Paragraph", "paraguay": "Paraguay", "partner": "Sharer", "password": "Password", "password_length_err": "Password must be between 8 and 24 characters in length", "password_login": "Log in with password", "password_not_identical_err": "'New password' and 'Confirm new password' must match", "password_pattern_err": "The password must include numbers and letters", "password_reset_succeed": "Password reset successfully, and you will log in automatically", "password_rules": "8-24 digits with letters and numbers", "paste": "Paste", "paste_attachment": "${keyboardShortcut} Paste attachments here", "paste_attachment_error": "Please paste the attachment into an attachment field", "paste_cell_data": "Paste cell(s)", "paste_tip_add_field": "One or more new fields will be added to this datasheet", "paste_tip_for_add_record": "One or more records will be added to this datasheet", "paste_tip_for_add_record_field": "New fields and records will be added to this datasheet", "paste_tip_permission_field": "One or more fields should've been added to this datasheet, but you don't have the permission to add them, so this action won't take effect", "paste_upload": "Paste and upload", "path": "Original path", "pay_model_price_contact": "Payment problems? ${contact_us}", "pay_model_price_desc": "Amount to be paid", "pay_model_price_public_transfer_desc": "Please add exclusive customer service for corporate transfer", "pay_model_title": "Pay order", "payment_record": "Payment record", "payment_reminder": "Payment Reminder", "payment_reminder_content": "The new plan you have selected is more deductible than the amount to be paid. It is recommended that you choose the new plan with a longer duration. If you confirm to do so, the excess amount will not be refundable. ${action} if in doubt", "payment_reminder_modal_content": "You can try the advanced version to enjoy more file nodes, enterprise permissions, attachment capacity, data volume, AI and other advanced features and privileges.", "payment_reminder_modal_title": "You are currently using the free version", "pending_invite": "Pending invite", "people": " member(s)", "per_person_per_year": "Per person per year", "percent_bar_chart": "Percent Bar Chart", "percent_cell_input_tips": "Please enter a number (positive or negative), decimal, or e (mathematical constant)", "percent_column_chart": "Percent <PERSON>n Chart", "percent_line_chart": "Percent Line Chart", "percent_stacked_bar_chart": "Percent Stacked Bar Chart", "percent_stacked_by_field": "100% Stacked by", "percent_stacked_column_chart": "Percent Stacked Column Chart", "percent_stacked_line_chart": "Percent Stacked Line Chart", "permission": "Permission", "permission_add_failed": "Failed to add member/team", "permission_add_success": "Member/team added ", "permission_allow_other_to_edit": "Allow others to edit outside the Space", "permission_allow_other_to_save": "Allow others to save as a copy", "permission_and_security": "Security", "permission_and_security_content": "Adjust the security level to prevent data leaks", "permission_bound": "Access level", "permission_change_success": "Permission changed", "permission_config_in_workbench_page": "[{\"key\":0,\"title\":\"About Datasheets\",\"detail\":[{\"title\":\"Edit view settings\",\"permissions\":[0,1,2]},{\"title\":\"Edit view list\",\"permissions\":[0,1,2]},{\"title\":\"Export view data\",\"permissions\":[0,1]},{\"title\":\"Add/Delete/Configure fields\",\"permissions\":[0,1]},{\"title\":\"Edit field layout (width/statistics/order)\",\"permissions\":[0,1,2]},{\"title\":\"Edit records (add/edit records)\",\"permissions\":[0,1,2,3]},{\"title\":\"Delete records\",\"permissions\":[0,1,2]},{\"title\":\"Undo/Redo actions\",\"permissions\":[0,1,2,3]},{\"title\":\"Post a comment\",\"permissions\":[0,1,2,3,4]}]},{\"key\":1,\"title\":\"About File nodes\",\"detail\":[{\"title\":\"Edit file node permissions\",\"permissions\":[0,1]},{\"title\":\"Create file nodes\",\"permissions\":[0,1]},{\"title\":\"Import files\",\"permissions\":[0,1]},{\"title\":\"Export files\",\"permissions\":[0,1]},{\"title\":\"Duplicate file nodes (Need full access to the file node and its folder)\",\"permissions\":[0,1]},{\"title\":\"Move file nodes (Need full access to the file node and its folder)\",\"permissions\":[0,1]},{\"title\":\"Rename file nodes\",\"permissions\":[0,1]},{\"title\":\"Delete file nodes\",\"permissions\":[0,1]},{\"title\":\"Share file nodes\",\"permissions\":[0,1,2]},{\"title\":\"Add file nodes description\",\"permissions\":[0,1]},{\"title\":\"Save as template\",\"permissions\":[0,1]}]}]", "permission_delete_failed": "Failed to delete member(s)/team permission", "permission_delete_success": "Member/team permissions deleted", "permission_edit_failed": "Edit failed", "permission_edit_succeed": "Edited", "permission_fields_count_up_to_bound": "Number of column max", "permission_inherit_superior": "Inherit superior permissions", "permission_management": "Roles", "permission_no_manageable_permission_access": "Unmanageable", "permission_no_permission_access": "Uneditable", "permission_removed_in_curspace_tip": "Your permission on this Space changed to a member", "permission_setting": "Edit permissions", "permission_setting_tip": "You can give permissions on this file node for members or teams in the Space", "permission_settings": "Permission settings", "permission_specific_show": "Specify visibility", "permission_switch_failed": "Failed to switch permission setting mode", "permission_switch_specified": "Enable manual setting", "permission_switch_succeed": "Permission setting mode switched", "permission_switch_to_superior": "Disable manual setting", "permission_switched_inherit_superior": "After disabled, members and teams will automatically have permission to this file node as they have to the superior folder. The already specified permissions will be cleared and cancelled.", "permission_switched_reallocate": "After enabled, you can set permissions for specific members or teams", "permission_template_visitor": "Experiencing", "permisson_model_field_owner": "Field owner", "person": " member(s)", "person_of_rest": "Remaining: ${count}", "person_upper_bound": "Maximum: ${maxMemberNumber}", "personal": "Personal", "personal_info": "Account information", "personal_invitation_code_desc1": "You can see ${text} by entering the space and clicking on the avatar at the bottom left", "personal_invitation_code_desc2": "Use to get ${text} reward with friends", "personal_invitation_code_desc2_text": "You and your inviter will both get 1000 V coins", "personal_invite_code_tip": "To get a personal invite code: obtain a code from a current user.", "personal_invite_code_usercenter": "Invite code", "personal_mode": "Method 1: the personal invitation code of your friends", "personal_nickname": "Name", "personalized_setting": "Preferences", "personalized_setting_tip": "Configure your preferences, including language, appearance, and so on. The following settings only take effect on your account.", "peru": "Peru", "philippines": "Philippines", "phone_code_err": "Invalid verification code", "phone_email_login": "Email login", "phone_err": "Wrong phone number format", "phone_number": "Phone", "pick_field_or_function": "Select field or function", "pick_one_option": "Pick a field", "pie_chart": "Pie Chart", "placeholder_add_record_default_complete": "Prefill new cells with this value", "placeholder_choose_group": "Select a team", "placeholder_email_code": "Enter verification code", "placeholder_enter_here": "Enter here", "placeholder_enter_your_description": "Enter a description", "placeholder_enter_your_verification_code": "Enter verification code", "placeholder_input": "Enter", "placeholder_input_account": "Enter your phone number or email", "placeholder_input_code": "Enter 6-digit verification code", "placeholder_input_datasheet_name": "Name the datasheet", "placeholder_input_email": "Enter your email address", "placeholder_input_member_email": "Enter an email address", "placeholder_input_member_name": "Enter member nickname", "placeholder_input_mobile": "Enter your phone number", "placeholder_input_new_nickname": "Give yourself a new name", "placeholder_input_new_password_again": "Enter your new password again", "placeholder_input_new_password_with_given_rules": "Enter a new password with 8-24 letters and numbers", "placeholder_input_new_phone": "Enter a new phone number", "placeholder_input_nickname_with_rules": "Enter ${minCount}-${maxCount} characters", "placeholder_input_password": "Enter a password", "placeholder_input_password_again": "Enter the password again", "placeholder_input_phone_last_registered": "Enter the phone number when you signed up", "placeholder_input_sso_account": "Enter SSO account", "placeholder_input_team_name": "Name the team", "placeholder_input_workspace_name": "Enter space name", "placeholder_input_workspace_new_name": "Name the Space (within ${minCount}-${maxCount} characters)", "placeholder_message_code": "Enter verification code", "placeholder_modal_normal": "Bubble format will be used if text-overflow happened", "placeholder_search_team": "Search team", "placeholder_select_report_reason": "Please select the reason for the report", "placeholder_set_password": "8-24 digits with letters and numbers", "plan_model_benefits_button": "Viewing Privilege Comparison", "plan_model_benefits_gold": "1,000 file nodes per space;\n20,000 records per datasheet;\n500  thousand api call per month;\n200 file nodes permission per space;\n6 months history to go back;\nattachment capacity increased;\nadvanced views and magic forms increased;", "plan_model_benefits_sliver": "300 file nodes per space;\n10,000 records per datasheet;\n100  thousand api call per month;\n6 months history to go back;\nattachment capacity increased;\nadvanced views and magic forms increased;", "plan_model_benefits_title": "${space_level} privilege", "plan_model_button": "Pay", "plan_model_choose_members": "Choose seats", "plan_model_choose_period": "Choose period", "plan_model_choose_space_level": "Upgrade to ${space_level}", "plan_model_members_tips": "Not enough? Click on me to switch to ${space_leve}", "plan_model_period_tips": "The due date is", "plan_model_space_member": "Space members", "planet_dwellers": "Planet dwellers", "platform_synchronization": "Synchronize in all platforms", "play_guide_video_of_gantt_view": "How to use a Gantt chart", "player_contact_us": "Contact us", "player_contact_us_confirm_btn": "Contact customer service", "player_step_ui_config_1": "{}", "player_step_ui_config_10": "{\n \"element\": \".style_linkWrapper__12Kgi  .style_urlWrapper__2IVlG button\", \n\"placement\": \"bottomRight\",\n \"title\": \"Copy and share\",\n\"description\": \"Click to copy the link and share it with members\", \"children\":\"\" \n} ", "player_step_ui_config_100": "{\n  \"element\": \"#DolphinBY_UPDATE_LOGS_HISTORY\",\n  \"shadowDirection\":\"inset\"\n}", "player_step_ui_config_101": "{\n  \"element\": \"#DolphinBY_UPDATE_LOGS_HISTORY\",\n  \"offsetY\": 23,\n  \"placement\": \"leftCenter\",\n  \"title\": \"Tips\",\n  \"description\": \"You can retrieve all the update logs right here\"\n}", "player_step_ui_config_103": "", "player_step_ui_config_105": "{\n \"element\": \".sc-jmpzUR:nth-last-of-type(2)\",\n\"placement\": \"rightCenter\",\n \"title\": \"Tips\", \n\"description\": \"The templates center offers more than 1000 business automation solutions and is free for download.\", \"children\":\"\" \n}", "player_step_ui_config_106": "{\n \"element\": \".sc-jmpzUR:nth-last-of-type(2)\",\n\"shadowDirection\":\"inset\"\n} ", "player_step_ui_config_107": "{\n \"element\": \"#WORKBENCH_SIDE_ADD_NODE_BTN\", \n\"placement\": \"bottom\",\n \"title\": \"Tips\", \n\"description\": \"Still trying to figure out your business scenarios? Give these templates a shot~\", \"children\":\"\" \n}", "player_step_ui_config_108": "{\n \"element\": \"#WORKBENCH_SIDE_ADD_NODE_BTN\"\n}", "player_step_ui_config_109": "", "player_step_ui_config_11": "{\"element\": \".style_linkWrapper__12Kgi  .style_urlWrapper__2IVlG button\",\n\"shadowDirection\": \"none\"} ", "player_step_ui_config_111": "", "player_step_ui_config_12": "{\n \"element\": \".style_addNewLink__3ALup>button\", \n\"placement\": \"bottomRight\",\n \"title\": \"Create an invitation link\", \n\"description\": \"Create a link to invite members to join the station or a designated group within the station\", \"children\":\"\" \n} ", "player_step_ui_config_124": "{\n \"element\": \".styles_controls__3Uc0- > div\",\n\"placement\": \"left\",\n \"title\": \"Expand/hide record\", \n\"description\": \"Pending records are stored here. You can expand or hide them by clicking on them\", \n\"children\": \"\"\n}", "player_step_ui_config_125": "{\n \"element\": \".styles_controls__3Uc0- > div\"\n}", "player_step_ui_config_126": "{\n \"element\": \"#DATASHEET_ORG_CHART_RECORD_LIST\",\n\"placement\": \"left\",\n \"title\": \"Create a card\", \n\"description\": \"Choose a record, and drag it to the graphic area on the left\",\n\"offsetY\": 144\n}", "player_step_ui_config_127": "{\n \"element\": \"#DATASHEET_ORG_CHART_RECORD_LIST\"\n}", "player_step_ui_config_128": "{\n\"element\": \".react-flow__node\"\n}", "player_step_ui_config_129": "{\n \"element\": \".react-flow__node\",\n\"placement\": \"bottom\",\n \"title\": \"Create a relationship between cards\", \n\"description\": \"Select another record and drag it toward the previous card to create a card hierarchy\", \n\"children\": \"\"\n}", "player_step_ui_config_13": "{\n \"element\": \".style_addNewLink__3ALup>button\"\n} ", "player_step_ui_config_131": "", "player_step_ui_config_132": "", "player_step_ui_config_133": "", "player_step_ui_config_134": "", "player_step_ui_config_135": "{\n\"element\": \".style_topRight__2hxKm\",\n\"title\": \"Set space nickname\",\n\"description\": \"What do you want the members to call you?\",\n\"placement\": \"topCenter\",\n\"posInfo\": {\n\"bottom\": \"420px\",\n\"left\": \"100px\",\n\"right\": \"\",\n\"tipNodeClasses\": [\"bottom\", \"position-center\"]\n}\n}", "player_step_ui_config_136": "{}", "player_step_ui_config_137": "", "player_step_ui_config_138": "", "player_step_ui_config_139": "{\n\"element\": \"#toolHideField\"\n}", "player_step_ui_config_14": "{\n \"placement\": \"bottomRight\",\n  \"description\": \"Welcome to Dolphin Template Center. You can find a variety of templates here. Now, let's learn about how to use a template.\"\n}", "player_step_ui_config_140": "{\n \"element\": \"#toolHideField\",\n\"placement\": \"bottom\",\n \"title\": \"Hide fields\", \n\"description\": \"Click to adjust the display fields in the task area\", \n\"children\": \"\"\n}", "player_step_ui_config_141": "{\n\"element\": \"#toolHideExclusiveField\"\n}", "player_step_ui_config_143": "{\n \"element\": \"#toolHideExclusiveField\",\n\"placement\": \"bottom\",\n \"title\": \"Hide graphic field\", \n\"description\": \"Click to adjust the display fields in the task bar\", \n\"children\": \"\"\n}", "player_step_ui_config_144": "", "player_step_ui_config_145": "", "player_step_ui_config_15": "{\n \"element\": \".style_templateItem__1UDe0\", \n\"placement\": \"bottom\",\n \"title\": \"How to use a template\", \n\"description\": \"Let's pick a template first, then click to preview it\", \"children\":\"\" \n}", "player_step_ui_config_152": "", "player_step_ui_config_153": "", "player_step_ui_config_154": "", "player_step_ui_config_155": "", "player_step_ui_config_156": "{\n  \"element\": \".permission_setting_class\",\n  \"placement\": \"leftCenter\",\n  \"title\": \"Default permissions\",\n  \"description\": \"The default permission role is displayed when no permission is specified, and you can assign permissions to members or groups directly here.\",\n  \"children\":\"\" \n }", "player_step_ui_config_157": "{\n  \"element\": \"#resetPermissionButton\",\n  \"placement\": \"topCenter\",\n  \"title\": \"Restricted permissions\",\n  \"offsetY\": -15,\n  \"description\": \"Permissions have been set here, so only the members of the list below are visible. You can click here to restore the default permissions.\",\n  \"children\":\"\" \n}", "player_step_ui_config_158": "{\n  \"element\": \"#resetPermissionButton\",\n  \"placement\": \"topCenter\",\n  \"title\": \"Specify permission role\",\n  \"offsetY\": -15,\n  \"description\": \"You have modified the permission role of this file node again, which means that this file node is only visible to the members you specified. You can click \"restore default\" to undo the previous settings.\",\n  \"children\":\"\" \n}", "player_step_ui_config_159": "", "player_step_ui_config_16": "{\n \"element\": \".style_templateItem__1UDe0\"\n}\n", "player_step_ui_config_160": "{\n  \"element\": \".permission_setting_class\"\n }", "player_step_ui_config_161": "", "player_step_ui_config_162": "", "player_step_ui_config_163": "", "player_step_ui_config_164": "", "player_step_ui_config_165": "{\n    \"headerImg\": \"https://s4.Dolphin.cn/space/2023/03/16/8374ca1295664675bd1155b077555113\",\n    \"readMoreUrl\": \"http://help.dolphintable.suanlihaiyang.com/changelog/23-03-16-updates\",\n    \"children\": \"<h3>🚀 Introduction of new functions</h3>\\n<ul><li>The mirror is upgraded again, hiding sensitive fields and collaborating with peace of mind</li><li>The time zone of the user account is online, and the time zone of the date list can be set independently, making cross-time zone cooperation smoother.</li><li>\"Global Search\" experience optimization, new search result categories</li><li>Gold-level space station benefits are increased, and the sharing form supports hiding official logos</li><li>API interface performance optimization, greatly improving call efficiency</li></ul>\"\n}", "player_step_ui_config_166": "{\n  \"title\": \"Congratulations!\",\n  \"description\": \"Your space get free trial 14 days for Enterprise. Click the button below to see the plan details.\",\n  \"listHeader\": \"Included Features:\",\n  \"listContent\": [\n    \"Up to 500,000,000 records per space\",\n    \"50GB of attachments per space\",\n    \"All APIs are accessible\"\n  ],\n  \"listFooter\": \"More details\",\n  \"url\": \"https://aitable.ai/management/upgrade\"\n}", "player_step_ui_config_167": "{\n  \"config\": [\n    {\n      \"key\": 1,\n      \"name\": \"answer1\",\n      \"title\": \"How do you want to use APITable?\",\n      \"type\": \"multiButton\",\n      \"answers\": [\n        \"IT & Support\",\n        \"Education\",\n        \"Project Management\",\n        \"Marketing\",\n        \"Product Management\",\n        \"HR & Recruiting\",\n        \"Operations\",\n        \"Finance\",\n        \"Sales & CRM\",\n        \"Software Development\",\n        \"HR & Legal\",\n        \"Design & Creative\",\n        \"Nonprofit\",\n        \"Manufacture\",\n        \"Other Things\"\n      ],\n      \"lastAllowInput\": false\n    },\n    {\n      \"key\": 2,\n      \"name\": \"answer2\",\n      \"title\": \"What best describes your current role?\",\n      \"type\": \"radio\",\n      \"answers\": [\n        \"Business Owner\",\n        \"Team Leader\",\n        \"Team Member\",\n        \"Freelancer\",\n        \"Director\",\n        \"C-level\",\n        \"VP\"\n      ],\n      \"lastAllowInput\": false\n    },\n        {\n      \"key\": 3,\n      \"name\": \"answer3\",\n      \"title\": \"How many people are on your team? \",\n      \"type\": \"radio\",\n      \"answers\": [\n        \"Just me\",\n        \"2-5\",\n        \"6-10\",\n        \"11-15\",\n        \"16-25\",\n        \"25-50\",\n        \"51-100\",\n        \"101-500\"\n      ],\n      \"lastAllowInput\": false\n    },\n    {\n      \"key\": 4,\n      \"name\": \"answer4\",\n      \"title\": \"How many people work at your company?  \",\n      \"type\": \"radio\",\n      \"answers\": [\n        \"1-19\",\n        \"20-49\",\n        \"50-99\",\n        \"100-250\",\n        \"251-500\",\n        \"501-1500\",\n        \"1500+\"\n      ],\n      \"lastAllowInput\": false\n    },\n    {\n      \"key\": 5,\n      \"name\": \"answer5\",\n      \"title\": \"How did you hear about us? \",\n      \"type\": \"checkbox\",\n      \"answers\": [\n        \"Search Engine\",\n        \"YouTube\",\n        \"Product Hunt\",\n        \"Github\",\n        \"Twitter\",\n        \"LinkedIn\",\n        \"Through a friend\"\n      ],\n      \"lastAllowInput\": false\n    },\n    {\n      \"key\": 6,\n      \"name\": \"answer6\",\n      \"title\": \"We host a Discord channel as a place for discussion with APITable fans, come and join us!\",\n      \"type\": \"joinUs\",\n      \"url\": \"https://discord.gg/2UXAbDTJTX\",\n      \"confirmText\": \"Join Our Community\",\n      \"skipText\": \"skip\",\n      \"submit\": true\n    }\n  ]\n}", "player_step_ui_config_168": "{\n\"templateKey\":\"createMirrorTip\"\n}", "player_step_ui_config_169": "{\n    \"headerImg\": \"https://s4.Dolphin.cn/space/2023/04/04/bcf0695cf35840e6b85e032a8e5ea0a1\",\n    \"readMoreUrl\": \"http://help.dolphintable.suanlihaiyang.com/changelog/23-04-10-updates\",\n    \"children\": \"<h3>🚀 Introduction of new functions</h3>\\n<ul><li>New field type \"Cascader\" is launched, making selection from a hierarchy of options on forms easier</li><li>\"Script\" widget is released, less code for more customization</li><li>Trigger Automation to send Emails, and get fast notifications</li><li>Trigger Automation to send a message to <PERSON>lack, and inform your team in time</li><li>Exploring AI: \"GPT Content Generator\" Widget Released</li></ul>\"\n}", "player_step_ui_config_17": "{\n \"element\": \"#TEMPLATE_CENTER_USE_TEMPLATE_BTN>button\", \n\"placement\": \"rightBottom\",\n\"offsetY\": 20,\n \"title\": \"How to use a template\", \n\"description\": \"Click the button on the left to use the template\", \"children\":\"\" \n}", "player_step_ui_config_176": "{\"title\":\"AITable.ai DEMO\",\"video\":\"https://www.youtube.com/embed/nbqwE21X1hc?si=HYTHEboJtarOL1w8\",\"videoId\":\"Dolphin_GUIDE_VIDEO_FOR_AI\",\"autoPlay\":true}", "player_step_ui_config_18": "{\n \"element\": \".style_usingTemplateWrapper__2vLm0 .ant-select>.ant-select-selector\", \n\"placement\": \"bottomLeft\",\n \"title\": \"How to use a template\", \n\"description\": \"Select where to put the template\", \"children\":\"\" \n}", "player_step_ui_config_19": "{\n \"element\": \".style_usingTemplateWrapper__2vLm0 .ant-select .ant-select-selector\"\n}", "player_step_ui_config_2": "{\n  \"config\": [\n    {\n      \"key\": 1,\n      \"name\": \"answer1\",\n      \"title\": \"What kind of issues are you looking forward to solved by Dolphin<PERSON>?\",\n      \"type\": \"checkbox\",\n      \"answers\": [\n        \"Work Planning\",\n        \"Customer Service\",\n        \"Project Management\",\n        \"Sourcing Supply\",\n        \"Content Production\",\n        \"E-commerce operation\",\n        \"Event Planning\",\n        \"Human Resources\",\n        \"Administration\",\n        \"Financial Management\",\n        \"Webcast\",\n        \"Educational institutes Management\",\n        \"Other\"\n      ],\n      \"lastAllowInput\": true\n    },\n    {\n      \"key\": 2,\n      \"name\": \"answer2\",\n      \"title\": \"Your job title is______\",\n      \"type\": \"radio\",\n      \"answers\": [\n        \"General manager \",\n        \"Project manager\",\n        \"Product manager\",\n        \"Designer\",\n        \"R&D engineer\",\n        \"Operator, editor\",\n        \"Sales, customer service\",\n        \"Human resource,  administration \",\n        \"Finance, accountant\",\n        \"Lawyer, legal affairs\",\n        \"Marketing\",\n        \"Teacher\",\n        \"Student\",\n        \"Other\"\n      ],\n      \"lastAllowInput\": true\n    },\n    {\n      \"key\": 3,\n      \"name\": \"answer3\",\n      \"title\": \"What is the name of your company?\",\n      \"type\": \"input\",\n      \"submit\": false\n    },\n    {\n      \"key\": 4,\n      \"name\": \"answer4\",\n      \"title\": \"Please leave your email address/ phone number/ Wechat account below so we can reach you in time when you need help.\",\n      \"type\": \"input\",\n      \"submit\": true\n    },\n    {\n      \"key\": 5,\n      \"title\": \"Thank you for filling out the form, you can add our customer service in case of need\",\n      \"platform\": {\n        \"website\": \"https://s4.Dolphin.cn/space/2023/03/02/b34e3205a9154463be16f497524f1327\",\n        \"dingtalk\": \"https://s4.Dolphin.cn/space/2023/03/02/4b16cef6602a4586a21d6346bf25d300\",\n        \"wecom\": \"https://s4.Dolphin.cn/space/2023/03/02/60119b1b69b64aa887bd1be6a2928bec\",\n        \"feishu\": \"https://u.Dolphin.cn/z9ygm\"\n      },\n      \"type\": \"contactUs\",\n      \"next\": true\n    }\n  ]\n}\n", "player_step_ui_config_20": "{\n \"element\": \"#TEMPLATE_CENTER_CONFIRM_BTN_IN_TEMPLATE_MODAL\", \n\"placement\": \"bottomLeft\",\n \"title\": \"How to use a template\", \n\"description\": \"Click \"Confirm\" to use the template\", \"children\":\"\" \n}", "player_step_ui_config_21": "", "player_step_ui_config_22": "{\n \"placement\": \"bottomLeft\",\n  \"description\": \"Congrats! Now you know how to use a template. Let's continue the journey to learn more~\"\n}", "player_step_ui_config_23": "{\n \"element\": \".style_searchPanelContainer__1_iOe\", \n\"placement\": \"leftTop\",\n \"title\": \"How to create a form\", \n\"description\": \"First, pick a datasheet to store the collected data\", \"children\":\"\" \n}", "player_step_ui_config_24": "{\n \"element\": \".style_formPreviewer__2Au6g\", \n\"placement\": \"leftTop\",\n \"title\": \"How to create a form\", \n\"description\": \"The number and order of the fields in the form is consistent with the selected view. Based on the original view, you can preview the form on the right.\", \"children\":\"\" \n}", "player_step_ui_config_25": "{\n \"element\": \"#WORKBENCH_SIDE_FORM_USE_GUIDE_BTN\", \n\"placement\": \"bottomRight\",\n \"title\": \"How to use a form\", \n\"description\": \"Click the button above for more usage guidance\", \"children\":\"\" \n}", "player_step_ui_config_26": "{\n \"element\": \"#WORKBENCH_SIDE_ADD_NODE_BTN\", \n\"placement\": \"bottom\",\n \"title\": \"Tips\", \n\"description\": \"First, Let's start by creating a blank datasheet\", \"children\":\"\" \n}", "player_step_ui_config_27": "{\n \"element\": \"#WORKBENCH_SIDE_ADD_NODE_BTN\"\n}", "player_step_ui_config_28": "{\n \"element\": \"#NODE_CONTEXT_MENU_ID .react-contexify__item:nth-of-type(1)\", \n\"placement\": \"rightCenter\",\n \"title\": \"Tips\", \n\"description\": \"Then, click 'New datasheet'\", \"children\":\"\" \n}", "player_step_ui_config_29": "{\n \"element\": \"#NODE_CONTEXT_MENU_ID > .react-contexify__item:nth-of-type(1) .react-contexify__item__content > div:nth-of-type(1)\",\n\"shadowDirection\":\"inset\"\n} ", "player_step_ui_config_3": "", "player_step_ui_config_30": "{\n \"element\": \"#DATASHEET_ADD_COLUMN_BTN\", \n\"placement\": \"leftTop\",\n \"title\": \"Tips\", \n\"description\": \"Next, let's create a new field\", \"children\":\"\" \n}", "player_step_ui_config_31": "", "player_step_ui_config_32": "{\n \"element\": \"#DATASHEET_GRID_CUR_COLUMN_TYPE\", \n\"placement\": \"bottom\",\n \"title\": \"Tips\", \n\"description\": \"dolphindata provides various types of fields. Hover on one field type to view the details.\", \"children\":\"\" \n}", "player_step_ui_config_33": "{\n \"element\": \"#DATASHEET_VIEW_TAB_BAR .style_viewBarWrapper__AJlc-\", \n\"placement\": \"bottom\",\n \"title\": \"Tips\", \n\"description\": \"A datasheet provides a variety of views, in which grouping, filtering, sorting and other features help you customize the data layout. All views of a datasheet use the same data source, so do not use the view as an Excel sheet!\", \"children\":\"\" \n}", "player_step_ui_config_34": "", "player_step_ui_config_35": "{\n \"element\": \"#DATASHEET_ADD_VIEW_BTN\", \n\"placement\": \"bottom\",\n \"title\": \"Tips\", \n\"description\": \"dolphindata supports a variety of views such as Grid, Gallery, Kanban, Gantt, Calendar, and Architecture. Different views correspond to different data visualizations and workflows. Again, views are only displayed differently, but they come from the same data source!\", \"children\":\"\" \n}", "player_step_ui_config_36": "", "player_step_ui_config_37": "{\n \"element\": \"#DATASHEET_TOOL_BAR .style_toolbarMiddle__2kxTf>button:nth-of-type(7)\", \n\"placement\": \"bottom\",\n \"title\": \"Tips\", \n\"description\": \"To share content outside the Space, use this feature to create a public link and share it\", \"children\":\"\" \n}", "player_step_ui_config_38": "", "player_step_ui_config_39": "{\n \"placement\": \"bottomLeft\",\n  \"description\": \"You're all set! If you need more guidance, click the \"Help\" icon on the left to see help documentation.\"\n}", "player_step_ui_config_4": "{\n\"title\":\"What is Table\",\n\"video\":\"space/2022/02/21/94cb82f9ffd84a5499c8931a224ad234\",\n\"videoId\":\"Dolphin_GUIDE_VIDEO_1\",\n\"autoPlay\":true\n}", "player_step_ui_config_40": "{\n \"element\": \"#DATASHEET_FORM_BTN\",\n\"placement\": \"bottomRight\",\n \"title\": \"Form\", \n\"description\": \"Want to populate data to your datasheet in a uniform way? Try the form\", \"children\":\"\" \n}", "player_step_ui_config_41": "", "player_step_ui_config_42": "{\n \"element\": \"#DATASHEET_FORM_LIST_PANEL\", \n\"placement\": \"leftTop\",\n \"title\": \"Form\", \n\"description\": \"You can quickly create a form from the current view. The number and order of the fields in the form is consistent with the view.\", \"children\":\"\" \n}", "player_step_ui_config_43": "{\n \"element\": \".style_navigation__1U5cR .style_help__1sXEA\", \n\"placement\": \"rightBottom\",\n \"title\": \"Tip\", \n\"offsetY\":5,\n\"description\": \"You can find your Do<PERSON><PERSON> assistant from the \"Help\" icon on the left\", \"children\":\"\" \n}", "player_step_ui_config_44": "{\n\"title\":\"dolphin<PERSON>'s hierarchy\",\n\"video\":\"space/2023/12/29/212a38dda62f4e52a58a92bf86657705\",\n\"videoId\":\"Dolphin_GUIDE_VIDEO_NEW_USER\",\n\"autoPlay\":true\n}\n", "player_step_ui_config_45": "", "player_step_ui_config_46": "", "player_step_ui_config_47": "{\n \"element\": \"#DATASHEET_WIDGET_BTN\",\n\"placement\": \"bottomRight\",\n \"title\": \"Widget\", \n\"description\": \"Want to use and view your data in a richer way? Try the widget!\", \"children\":\"\" \n}", "player_step_ui_config_48": "", "player_step_ui_config_49": "{\n \"element\": \".style_widgetPanelContainer__1l2ZV\",\n\"placement\": \"leftCenter\",\n \"title\": \"What is a widget\", \n\"description\": \"Dolphin widgets are an extended application of Table that features in richer data visualization, data transfer, data cleansing, and more. By installing widgets in the widget board, you can make work a lot easier.\", \"children\":\"\" \n}", "player_step_ui_config_5": "{\n\"title\":\"dolphin<PERSON>'s hierarchy\",\n\"video\":\"space/2023/12/12/8e870a50d98646f0a4cc2f76e3cd6d46\",\n\"videoId\":\"Dolphin_GUIDE_VIDEO_2\",\n\"autoPlay\":true\n}", "player_step_ui_config_50": "{\n \"element\": \".style_widgetModal__eXmdB\",\n\"placement\": \"leftTop\",\n \"title\": \"Widget Center\", \n\"description\": \"Officially recommended and custom widgets are published here. You can install any widget you want onto a dashboard or into a widget board of any datasheet.\", \"children\":\"\" \n}", "player_step_ui_config_51": "{\n \"element\": \".style_widgetModal__eXmdB .style_widgetItem__3Pl-1 button\",\n\"placement\": \"rightBottom\",\n \"title\": \"Install widget\", \n\"description\": \"Let's install this \"Chart\" widget\", \"children\":\"\" \n}", "player_step_ui_config_52": "", "player_step_ui_config_53": "{\n \"element\": \".style_widgetPanelContainer__1l2ZV\",\n\"placement\": \"leftCenter\",\n\"description\": \"Installed! We create a widget board for you, in which you can install all widgets.\", \"children\":\"\" \n}", "player_step_ui_config_54": "{\n \"element\": \".style_widgetPanelContainer__1l2ZV .style_panelHeader__3X0pG\",\n\"placement\": \"bottom\",\n \"title\": \"Widget board\", \n\"description\": \"Widget boards are containers for loading widgets. You can categorize your widgets by creating multiple widget boards.\", \"children\":\"\" \n}", "player_step_ui_config_55": "{\n \"placement\": \"bottomLeft\",\n  \"description\": \"Using/adding widgets on the dashboard can optimize its data visualization ability so that you can make a judgment or decision more clearly based on the extra information offered by statistics and graphical analysis.\"\n}", "player_step_ui_config_56": "{\n \"element\": \"#DASHBOARD_PANEL_ID .style_tabRight__4YAkM button:nth-of-type(1)\",\n\"placement\": \"bottom\",\n \"title\": \"Add widget\", \n\"description\": \"There are two ways to add a widget here, either from the Widget Center or by importing from existing widget boards\", \"children\":\"\" \n}", "player_step_ui_config_57": "", "player_step_ui_config_58": "{\n \"element\": \".style_addWidgetMenu__29bIe .style_menuItem__3Ugjz:nth-of-type(1)\",\n\"placement\": \"bottom\",\n \"title\": \"Add widget\", \n\"description\": \"Let's add a widget to the dashboard\", \"children\":\"\" \n}", "player_step_ui_config_59": "{\n \"element\": \"#DASHBOARD_PANEL_ID .style_widgetContainer__2HwEf\",\n\"placement\": \"rightTop\",\n \"title\": \"Link to datasheet\", \n\"description\": \"Some widgets need a data source, so you need to select a datasheet to link to after adding a widget\", \"children\":\"\" \n}", "player_step_ui_config_6": "{\n\"title\":\"How to use a datasheet\",\n\"video\":\"space/2020/12/21/cb7bdf6fe22146068111d46915587fb2\",\n\"videoId\":\"Dolphin_GUIDE_VIDEO_3\",\n\"autoPlay\":true\n}", "player_step_ui_config_60": "{\n \"element\": \".style_searchPanelContainer__1_iOe\",\n\"placement\": \"rightTop\",\n \"title\": \"Link to datasheet\", \n\"description\": \"Select a datasheet you want to link to, and the widget will read data from it\", \"children\":\"\" \n}", "player_step_ui_config_61": "{\n \"placement\": \"bottomLeft\",\n  \"description\": \"Congrats! You now know how to use a dashboard and add widgets. Now build your own dashboard based on your business scenarios.\"\n}", "player_step_ui_config_62": "", "player_step_ui_config_63": "", "player_step_ui_config_64": "{\n\"title\":\"How to use a Gantt chart\",\n\"video\":\"space/2021/05/26/baddaa8b7d0c4b0390b03ef9a5549c6e\"\n}\n", "player_step_ui_config_65": "", "player_step_ui_config_66": "{\n\"title\":\"How to use a Calendar view\",\n\"video\":\"space/2022/04/26/ab9d17db76064e9d8fd228889e30f1ad\"\n} ", "player_step_ui_config_67": "", "player_step_ui_config_68": "{\n \"element\": \"div[data-guide-id=WIDGET_ITEM_WRAPPER]:last-child\",\n\"placement\": \"leftCenter\",\n \"title\": \"Dev mode\", \n\"description\": \"In dev mode, you can preview the latest changes you made to the local code\", \"children\":\"\" \n}", "player_step_ui_config_69": "{\n \"element\": \"div[data-guide-id=WIDGET_ITEM_WRAPPER]:last-child [data-guide-id=WIDGET_ITEM_REFRESH]\",\n\"placement\": \"topRight\",\n \"title\": \"Refresh widget\", \n\"description\": \"Each time you change the local code, you can click here to refresh and preview the widget\", \"children\":\"\"\n}", "player_step_ui_config_7": "{\n\"title\":\"Share and invite friends\",\n\"video\":\"space/2020/12/21/b8fa92ba4c7d41c6acbd7f24469e15fc\",\n\"videoId\":\"Dolphin_GUIDE_VIDEO_4\",\n\"autoPlay\":true\n}", "player_step_ui_config_70": "{\n \"element\": \"div[data-guide-id=WIDGET_ITEM_WRAPPER]:last-child [data-guide-id=WIDGET_ITEM_MORE]\",\n\"placement\": \"topRight\",\n \"title\": \"Exit dev mode\", \n\"description\": \"Click here to exit dev mode, which breaks the connection to the local service and resumes to the latest published widget version\", \"children\":\"\"\n}", "player_step_ui_config_71": "{}", "player_step_ui_config_72": "", "player_step_ui_config_73": "", "player_step_ui_config_74": "{\n \"element\": \".style_formSpace__1_szP .style_right__DRofa #DATASHEET_FORM_CONTAINER_SETTING\",\n\"placement\": \"bottomRight\",\n \"title\": \"Collapse options\",\n \"arrowStyle\": { \"right\": \"40px\" },\n\"description\": \"Long form due to lots of options? Collapse them\" \n}", "player_step_ui_config_75": "{\n \"element\": \"div[data-guide-id=WIDGET_ITEM_WRAPPER]:last-child\",\n\"placement\": \"leftCenter\",\n \"title\": \"Unpublished widget\", \n\"description\": \"The widget is not published to the Space. You can only preview it in the dev mode.\", \"children\":\"\" \n}", "player_step_ui_config_76": "", "player_step_ui_config_77": "", "player_step_ui_config_78": "{\n \"element\": \"#DATASHEET_TOOL_BAR .style_toolbarMiddle__2kxTf\",\n\"placement\": \"bottomRight\",\n \"arrowStyle\": { \"display\": \"none\" },\n \"title\": \"Unsynchronized view\",\n\"description\": \"Temporary filtering, grouping, sorting, etc., will not be synchronized to others without being saved, and your temporary configuration will become invalid after refreshing the page.\" \n}", "player_step_ui_config_79": "{\n \"element\": \"#DATASHEET_VIEW_TAB_BAR #view_item_sync_icon\",\n\"placement\": \"bottomLeft\",\n \"arrowStyle\": { \"left\": \"6px\" },\n \"title\": \"view configuration is unsaved\",\n\"description\": \"You have just modified some view configurations (filtering, grouping, sorting...), they haven't  been saved yet. This means that they are only temporarily effective for you. You can click here to save and sync to others\" \n}", "player_step_ui_config_8": "{\n \"element\": \"#ADDRESS_INVITE_BTN\", \n\"placement\": \"bottomLeft\",\n \"title\": \"Invitation\", \n\"description\": \"The current space station supports three ways to invite members: link, email and import\", \"children\":\"\" \n} ", "player_step_ui_config_80": "{\n \"element\": \"#AUTO_SAVE_SVG_ID\",\n\"placement\": \"bottomLeft\",\n \"arrowStyle\": { \"left\": \"6px\" },\n \"title\": \"This view is now autosave\",\n\"description\": \"You now change view configuration such as filtering, grouping, sorting, etc. will be automatically saved and synchronized to others\" \n}", "player_step_ui_config_81": "", "player_step_ui_config_82": "{\n\"title\":\"How to use an Architecture view\",\n\"video\":\"space/2022/04/26/bebe4536c330427c81e6b26627263904\"\n}", "player_step_ui_config_83": "{\n \"element\": \"#DATASHEET_CREATOR_ORG_CHART\",\n\"shadowDirection\": \"none\"\n}", "player_step_ui_config_84": "{\n \"element\": \"#DATASHEET_CREATOR_ORG_CHART\",\n\"placement\": \"right\",\n \"title\": \"Architecture view\", \n\"description\": \"Let's start exploring!\", \n\"children\": \"\"\n}", "player_step_ui_config_85": "{\n \"element\": \"#DATASHEET_ORG_CHART_RECORD_LIST\",\n\"placement\": \"left\",\n \"title\": \"Link record\", \n\"description\": \"You can drag a pending record to a record card on the left, and it will become a subordinate of the record card\",\n\"offsetY\": 144\n}", "player_step_ui_config_86": "{\n \"element\": \"#DATASHEET_ORG_CHART_RECORD_LIST\",\n\"placement\": \"left\",\n \"title\": \"Unlink record\", \n\"description\": \"You can drag a record card to the pending area on the right to unlink record quickly\",\n\"offsetY\": 144\n}", "player_step_ui_config_87": "{\n \"element\": \"#DATASHEET_ORG_CHART_RECORD_LIST\"\n}", "player_step_ui_config_88": "", "player_step_ui_config_89": "{\n \"element\": \"#toolHideField\",\n\"placement\": \"bottom\",\n \"title\": \"Styling the card\", \n\"description\": \"You can customize the fields and cover effects of the card\" \n}", "player_step_ui_config_9": "{\n \"element\": \"#ADDRESS_INVITE_BTN\"\n} ", "player_step_ui_config_90": "", "player_step_ui_config_91": "{\n \"element\": \"#DATASHEET_TOOL_BAR_VIEW_SETTING\",\n\"placement\": \"bottom\",\n \"title\": \"Watch videos again\", \n\"description\": \"Click \"Settings\" and you can find the tutorial video\" \n}", "player_step_ui_config_92": "", "player_step_ui_config_93": "", "player_step_ui_config_94": "{\n  \"title\": \"Let's go start your project!\",\n  \"description\": \"Welcome to Dolphin Planet! Let's take up a task and our journey will start from here.\",\n  \"data\": [\n      {\n          \"text\": \"Rename a file node to make the index clearer\",\n          \"stopEvents\": [\n              \"onMouseDown\"\n          ],\n          \"actions\": [\n              {\n                  \"uiType\": \"element\",\n                  \"uiConfig\": {\n                      \"element\": \"#FOLDER_SHOWCASE_TITLE\",\n                      \"emitEvent\": \"click\",\n                      \"nextActions\": [\n                          {\n                              \"uiType\": \"popover\",\n                              \"uiConfig\": \"{\\\"element\\\":\\\"#FOLDER_SHOWCASE_TITLE_INPUT\\\",\\\"placement\\\":\\\"bottom\\\",\\\"title\\\":\\\"How to use a datasheet\\\",\\\"description\\\":\\\"Click here to rename the file node 👆\\\"}\",\n                              \"backdrop\": \"around_mask\"\n                          },\n                          {\n                              \"uiType\": \"element\",\n                              \"uiConfig\": {\n                                  \"element\": \"#FOLDER_SHOWCASE_TITLE_INPUT\",\n                                  \"emitEvent\": \"focus\",\n                                  \"finishTodoWhen\": [\n                                      \"blur\"\n                                  ]\n                              }\n                          }\n                      ]\n                  }\n              }\n          ]\n      },\n      {\n          \"text\": \"Modify a file node's description to help others understand it better\",\n          \"actions\": [\n              {\n                  \"uiType\": \"breath\",\n                  \"uiConfig\": \"{\\\"element\\\":\\\"#FOLDER_SHOWCASE_DESCRIPTION\\\"}\",\n                  \"backdrop\": \"around_mask\"\n              },\n              {\n                  \"uiType\": \"popover\",\n                  \"uiConfig\": \"{\\\"element\\\":\\\"#FOLDER_SHOWCASE_DESCRIPTION\\\",\\\"placement\\\":\\\"left\\\",\\\"title\\\":\\\"How to use a datasheet\\\",\\\"description\\\":\\\"Click here to view or modify description\\\"}\"\n              },\n              {\n                  \"uiType\": \"element\",\n                  \"uiConfig\": {\n                      \"element\": \"#FOLDER_SHOWCASE_DESCRIPTION\",\n                      \"finishTodoWhen\": [\n                          \"click\"\n                      ]\n                  }\n              }\n          ]\n      },\n      {\n          \"text\": \"Set access permission on a file node to prevent data leakage or misoperation\",\n          \"actions\": [\n              {\n                  \"uiType\": \"breath\",\n                  \"uiConfig\": \"{\\\"element\\\":\\\"#FOLDER_SHOWCASE_BTN_MORE\\\"}\"\n              },\n              {\n                  \"uiType\": \"element\",\n                  \"uiConfig\": {\n                      \"element\": \"#FOLDER_SHOWCASE_BTN_MORE\",\n                      \"nextActions\": [\n                          {\n                              \"uiType\": \"breath\",\n                              \"uiConfig\": \"{\\\"element\\\":\\\".sc-eFehXo div:nth-of-type(1)\\\",\\\"shadowDirection\\\":\\\"inset\\\"}\"\n                          },\n                          {\n                              \"uiType\": \"element\",\n                              \"uiConfig\": {\n                                  \"element\": \".sc-eFehXo div:nth-of-type(1)\",\n                                  \"finishTodoWhen\": [\n                                      \"click\"\n                                  ]\n                              }\n                          }\n                      ]\n                  }\n              }\n          ]\n      },\n      {\n          \"text\": \"Pick and enter a node to start collaborating with others\",\n          \"actions\": [\n              {\n                  \"uiType\": \"breath\",\n                  \"uiConfig\": \"{\\\"element\\\":\\\"#FOLDER_SHOWCASE_FIRST_NODE\\\"}\"\n              },\n              {\n                  \"uiType\": \"element\",\n                  \"uiConfig\": {\n                      \"element\": \"#FOLDER_SHOWCASE_NODES_CONTAINER > div\",\n                      \"finishTodoWhen\": [\n                          \"click\"\n                      ]\n                  }\n              }\n          ]\n      }\n  ]\n}", "player_step_ui_config_95": "", "player_step_ui_config_97": "{\n\t\"Dolphinby\": {\n\t\t\"title\": \"Hello~\",\n\t\t\"description\": \"If you have a problem, you can contact me to help you\",\n\t\t\"list\": \"<ul><li>Not sure how to use Dolphindata</li><li>What Dolphindata can do for me</li><li>Problems during use</li><li>What's new in the future</li><li>Get official invitation code</li></ul>\",\n\t\t\"tip\": \"Scan the QR code to contact us\"\n\t},\n\t\"questionnaire\": {\n\t\t\"title\": \"Hello, I'm Dolphin's Digitalization Consultant\",\n\t\t\"list\": \"[{\\\"title\\\": \\\"Report bug\\\", \\\"icon\\\": \\\"BugOutlined\\\"}, {\\\"title\\\": \\\"Give feedback\\\", \\\"icon\\\": \\\"AdviseSmallOutlined\\\"}, {\\\"title\\\": \\\"Customer support\\\", \\\"icon\\\": \\\"ServeOutlined\\\"}, {\\\"title\\\": \\\"Case recommendation\\\", \\\"icon\\\": \\\"ZanOutlined\\\"}, {\\\"title\\\": \\\"Solutions\\\", \\\"icon\\\": \\\"SolutionSmallOutlined\\\"}, {\\\"title\\\": \\\"FAQs\\\", \\\"icon\\\": \\\"InformationLargeOutlined\\\"}]\",\n\t\t\"tip\": \"Please use WeChat to scan the code to add customer service to get more exclusive services\"\n\t},\n\t\"website\": {\n\t\t\"questionnaire\": \"https://s4.Dolphin.cn/space/2023/03/02/59f4cc8e0b2b4395bf0fcd133d208e63\",\n\t\t\"Dolphinby\": \"https://s4.Dolphin.cn/space/2023/03/02/defbf55d1e9646eb929f9f5d11d5c119\"\n\t},\n\t\"dingtalk\": {\n\t\t\"questionnaire\": \"https://s4.Dolphin.cn/space/2023/03/02/964be5e3217b4fa8bfa74ef47a980093?attname=dingtalk-questionnaire.png\",\n\t\t\"Dolphinby\": \"https://s4.Dolphin.cn/space/2023/03/02/964be5e3217b4fa8bfa74ef47a980093?attname=dingtalk-Dolphinby.png\",\n\"tip\": \"Please use DingTalk to scan the code to join our group\"\n\t},\n\t\"wecom\": {\n\t\t\"questionnaire\": \"https://s4.Dolphin.cn/space/2023/03/02/1346d5efbd5043efb2bfcba0075c0ee9\",\n\t\t\"Dolphinby\": \"https://s4.Dolphin.cn/space/2023/03/02/09401a8f2d8e491a9097b9bac8b5a4e4\"\n\t},\n\t\"feishu\": {\n\t\t\"title\": \"Scan the QR code to contact us\",\n\t\t\"tip\": \"Please use Lark to scan the code and add customer service for more help\",\n\t\t\"description\": \"So that you can get service at any time when you encounter problems during use\",\n\t\t\"originUrl\": \"https://u.Dolphin.cn/z9ygm\"\n\t}\n}", "player_step_ui_config_99": "", "player_step_ui_config_automation_1": "{\n \"element\": \"#AUTOMATION_ADD_TRIGGER_BTN\", \"title\":\"title\", \"description\": \"description\" \n} ", "player_step_ui_config_button_field_action_create": "{\n \"element\": \"#CONST_ROBOT_ACTION_CREATE\", \"title\": \"\", \"placement\": \"topCenter\", \"description\": \"Your next step should be to add one or more Actions, defining what specifically should happen when the button is clicked\"  \n} ", "player_step_ui_config_button_field_bound_datasheet": "{\n \"element\": \"#AUTOMATION_BOUND_DATASHEET\", \"title\": \"\", \"description\": \"\\\"Button is Clicked\\\" needs to be linked to both a table and a field to know where the click action occurs. This has been pre-configured for you\"  \n} ", "player_step_ui_config_button_field_node": "{\n \"element\": \".TREE_NODE_ACTIVE_ONE\", \"title\": \"\", \"description\": \"An Automation Node serves as the starting point of an automation workflow, where you can set triggers and corresponding actions\" \n} ", "player_step_ui_config_button_field_node_form_active": "{\n \"element\": \"#NODE_FORM_ACTIVE\", \"title\": \"\", \"placement\":\"bottomLeft\", \"description\": \"'Button is Clicked' is a trigger that activates when a user clicks on the new button column you've created\" \n} ", "player_step_ui_config_notice_0_10_5": "", "please": "Please", "please_check": "Please check", "please_choose": "Please select", "please_contact_admin_if_you_have_any_problem": "If you have any questions, contact the Space admin.", "please_download_to_view_locally": "Please download to check", "please_note": "Note", "please_read_carefully": "Please read carefully", "please_select": "please select", "please_select_org": "Select a team", "plus_edition": "Plus", "png": "As .png format", "poc_sync_members": "Add members through organizational structure", "poland": "Poland", "portugal": "Portugal", "pr_and_communications": "PR & Communications", "pre_fill_content": "Fill in the pre-filled content in the form fields on the left, and the system will use it as parameters to pre-fill the link below. When you click the link, the form will be automatically completed with the pre-provided information. (Note: Pre-fill is not available for all fields.)", "pre_fill_copy_title": "The link with pre-filled data within the Space", "pre_fill_helper_title": "What is Form Pre-fill?", "pre_fill_share_copy_title": "The public link with pre-filled data", "pre_fill_title": "Form Pre-fill", "pre_fill_title_btn": "Pre-fill", "pre_set_node_permission": "Field permissions can only be set after the file node permissions of the current datasheet are enabled in the explorer", "precision": "Precision", "press_again_to_exit": "Press again to exit!", "preview": "Preview", "preview_cannot_preview": "This file can't be preview", "preview_click_reset_image_size": "Click to return to the original size", "preview_copy_attach_url": "Copy attachment URL", "preview_copy_attach_url_succeed": "<PERSON>pied", "preview_doc_error_no_support_in_this_station": "The current Space hasn't turn on office preview function yet. Preview failed.", "preview_doc_type_attachment_loading": "Loading\b... please wait a sec", "preview_fail_content": "", "preview_fail_title": "Preview failed", "preview_form_title": "Form", "preview_form_title_desc": "Form preview", "preview_guide_click_to_restart": "Press the button below to preview again", "preview_guide_enable_it": "Press the button below to turn on this function", "preview_guide_open_office_preview": "To preview this file, please turn on the \"office preview\" function", "preview_next_automation_execution_time": "Preview next 10 execution times", "preview_not_support_video_codecs": "Only MP4 videos with H.264 video codecs can be preview", "preview_revision": "Preview", "preview_see_more": "Want to learn more about the \"office file preview\" feature? Please click here", "preview_the_image_not_support_yet": "The file doesn't support reviewing", "preview_time_machine": "Previewing, version ${version}", "preview_tip_contact_main_admin": "Ask the admin to enable it from the Integrations page of \"Space Management\"", "preview_widget": "Preview widget", "previous_month": "last month", "previous_page": "Previous page", "previous_record": "Move to the previous record", "previous_record_plain": "Previous record", "previous_record_tips": "Previous record (${previous_record})", "previous_week": "last week", "price_bottom_secction_desc": "The black technology that allows everyone to DIY business software systems at will", "price_bottom_secction_title": "", "price_discount_activity_info": "Discount will end on 2022-07-30.", "price_question_title": "Product Price Q&A", "price_questions": "[\n\t{\n\t\t\"question\": \"What is Space in APITable?\",\n\t\t\"answers\": [\n\t\t\t\"Space is a collaboration platform for all members of your organization or team. Access your personalized managed and invited spaces by clicking on the space icon located in the upper left corner of the APITable workbench.\"\n\t\t]\n\t},\n\t{\n\t\t\"question\": \"How can I check my current space plan and usage?\",\n\t\t\"answers\": [\n\t\t\t\"You can check your current Space plan and usage by navigating to the Space settings > Overview section.\"\n\t\t],\n\t\t\"list\": []\n\t},\n\t{\n\t\t\"question\": \"How does APITable's pricing work?\",\n\t\t\"answers\": [\n\t\t\t\"APITable charges are based on a combination of fixed rates and seat rates. On APITable's Free plan, you get 5 seats for free. If you choose to subscribe, you'll be charged based on the subscription plan and the number of seats. \",\n\t\t\t\"The Plus plan includes 5 available seats, and if you need to add more seats later, you'll need to pay a rate of 5 dollars/month/seat (annually).\",\n\t\t\t\"The Pro plan includes 5 available seats, and if you need to add more seats later, you'll need to pay a rate of 12 dollars/month/seat (annually).\",\n\t\t\t\"Here, 'seats' refers to the number of members joined in the space and doesn't include non-space members who are allowed to submit forms.\"\n\t\t]\n\t},\n\t{\n\t\t\"question\": \"How do I upgrade my Space plan? \",\n\t\t\"answers\": [\n\t\t\t\"With just a few clicks, you can upgrade your existing paid Space plan by navigating to Space settings > Upgrade and selecting a paid plan.\"\n\t\t]\n\t},\n\t{\n\t\t\"question\": \"How does adding and removing members work on billing?\",\n\t\t\"answers\": [\n\t\t\t\"When you add members, you'll be charged a portion of the fee based on the time each member was added and the current billing cycle.\",\n\t\t\t\"When you remove members, you'll receive a credit for a portion of the fee based on the same calculation. These charges or credits will appear on your next billing statement.\"\n\t\t]\n\t},\n\t{\n\t\t\"question\": \"How do I downgrade or unsubscribe?\",\n\t\t\"answers\": [\n\t\t\t\"If you want to downgrade your current plan, you can do it directly in the Space settings. For unsubscribing, we will assist you with downgrading or unsubscribing your subscription. Click the purple icon in the bottom right corner and send us a message or contact <NAME_EMAIL>.\"\n\t\t]\n\t}\n]", "price_sub_title": "$1 / person/day = 100+ office work templates", "price_title1": "APITable", "price_title2": "", "primary": "Primary", "primary_admin": "Admin", "primary_admin_email": "<PERSON><PERSON>'s email address", "primary_admin_new_nickname": "New admin's account name", "primary_admin_new_phone": "New admin's phone number", "primary_admin_nickname": "<PERSON><PERSON>'s account name", "primary_admin_phone": "Admin phone number", "privacy_check_box_content": "Click to agree ${content}", "privacy_policy": "<Privacy Policy>", "privacy_policy_pure_string": "Privacy Policy", "privacy_policy_title": "Privacy policy", "privacy_protection": "\"Privacy Protection\"", "private_cloud": "Private Cloud", "private_external_person_only": " External person only", "private_help_link": "https://help.aitable.ai/docs/team-and-private-area#private-area", "private_internal_person_only": " Internal person only", "private_product_point": "Own your APITable platform with one click", "privatized_deployment": "Self-Hosted", "privatized_deployment_desc": "Host your own instance on-premise or in the cloud", "privilege_list_of_sliver": "", "pro_edition": "Pro", "process": "Process", "processed": "Read", "product_design_and_ux": "Product, design, and UX", "product_roadmap": "Product roadmap", "products_and_consumer_reviews": "Products and consumer reviews", "profession": "Pro", "professional": "Professional", "project_management": "Project Management", "proportion": "proportion", "public_cloud": "Cloud", "public_cloud_desc": "Saas offering on the APITable website", "public_link": "Share public link", "public_link_desc": "A link that everyone can access", "publish": "Publish", "publish_link_tip": "Post a link that everyone can access", "publish_share_link_with_anyone": "Publish and share it with anyone", "publish_to_dingtalk_workbench": "Publish to DingTalk Workbench", "publishing": "Publishing Industry", "puerto_rico": "Puerto Rico", "purchase_capacity": "Purchase capacity", "put_away_record_comments": "Put away the record comments panel", "put_away_record_history": "Put away the record history panel", "qatar": "Qatar", "qq": "QQ", "qq_login_button": "Login with QQ", "qrcode_help": "Scan the QR code and I will assist you to solve", "quick_close_public_link": "Disable public link", "quick_compass": "Quick Compass", "quick_free_trial": "Start your free trial now", "quick_import_widget": "Import installed widgets", "quick_login": "Quick login options", "quick_login_bind": "<%= type %>", "quick_search_intro": "Quickly obtain relevant search results by entering a keyword. Filter by file node type (e.g. datasheet, folder, form, mirror, dashboard) using category items", "quick_search_loading": "Searching...", "quick_search_not_found": "We couldn't find what you were searching for", "quick_search_placeholder": "Search for file nodes", "quick_search_shortcut_esc": "Close", "quick_search_shortcut_open": "Open", "quick_search_shortcut_select": "Select", "quick_search_shortcut_tab": "Use the Tab shortcut key to switch between filters", "quick_search_title": "Quick Search", "quick_tour": "Quick start", "quickly_create_space": "Quickly creating a space", "quit_space": "Leave Space", "quote": "Quote", "rainbow_label": "You are experiencing advanced features: rainbow tags, which can be unlocked by an upgrade.", "rating": "Rating", "re_typing_email_err": "Re-typing email here!", "reach_dashboard_installed_limit": "The maximum number of widgets has been installed", "reach_limit_installed_widget": "You have installed the maximum number of widgets", "read_agree_agreement": "I've read and agree to ${Agreement1} and ${Agreement2}", "reader_lable": "Read-only", "readonly_column": "The current field is read-only and can't be edited", "real_estate": "Real estate", "rebuild_token_value": "Generate New Token", "receive_new_folder": "Received a new folder", "received_a_new_doc": "Received a new document", "recent_installed_widget": "Recently installed widgets (${count})", "recently_used_files": "Recently visited folders", "recommend": "Hot", "recommend_album": "recommend album", "reconciled_data": "Data is being reconciled", "record": "Record", "record_activity_experience_tips": "You can view record activity of  ${day}  days", "record_archived_data": "archived record", "record_comment": "Comments only", "record_comments": "comments", "record_fail_data": "data error", "record_filter_tips": "This record has been filtered", "record_functions": "Record Function", "record_history": "Revision history only", "record_history_help_url": "https://help.aitable.ai/docs/manual-record-activity", "record_history_title": "Record history", "record_pre_filtered": "This record has been filtered and will be hidden once you click outside the record", "record_pre_move": "This record will be moved elsewhere once you click outside the record", "record_unnamed": "Unnamed record", "record_watch_mobile": "Watch this record", "record_watch_multiple": "Watch these ${count} records", "record_watch_single": "Watch this record", "records_of_count": "${count} records", "records_per_datasheet": "The number of records per datasheet", "records_per_space": "Total records of the Space", "recover_node": "Restore file node", "recover_node_fail": "Failed to restore file node", "recover_node_success": "File node restored ", "redemption_code": "Used redeem code", "redemption_code_button": "Redeem code", "redo": "Redo", "refresh": "Refresh", "refresh_and_close_page_when_automation_queue": "There are still automated tasks being queued for execution, and continuing to operate will cause some tasks to be canceled", "refresh_manually": "Refresh", "register_immediately": "Sign up now", "register_invitation_code_subTitle": "", "register_invitation_code_title": "Enter invite code", "register_means_to_agree": "By signing up you agree to ", "register_regulations": "/service-agreement/", "register_time": "Registration time", "registration_completed": "Signed up successfully", "registration_guidelines": "Log in to view more", "registration_service_agreement": "\"Registration Service Agreement\"", "reject": "Reject", "rejected": "Rejected", "related_automations_disconnect_title": "Are you sure to disconnect this automation?", "related_files": "Related file nodes", "reload_page_later_msg": "Synchronization is done. The page will refresh in 5 seconds.", "remain": "remain:", "remain_capacity": "Remaining capacity", "remaining_records": "Remaining: ${count}", "remaining_time": "Remaining day(s)", "remarks": "V coins", "remind_never_again": "Don't remind me again", "remove": "Remove", "remove_cover": "Remove cover", "remove_favorite": "Unpin", "remove_from_group": "You were removed from the \"<a class=\"teamName\"></a>\" team of the \"<a class=\"spaceName\"></a>\" Space by <a class=\"memberName\"></a>.", "remove_from_role": "You were removed from the \"<a class=\"roleName\"></a>\" role of the \"<a class=\"spaceName\"></a>\" Space by <a class=\"memberName\"></a>.", "remove_from_space": "Remove from Space", "remove_from_space_confirm_tip": "Confirm to completely remove the member from this workspace. After removal, the member's private workspace files will no longer occupy the capacity of the workspace station.", "remove_from_team": "<PERSON><PERSON><PERSON> from team", "remove_from_team_confirm_tip": "Confirm to remove the member from this team", "remove_from_the_team": "Remove", "remove_member_fail": "Remove member failed", "remove_member_from_space_confirm_content": "Are you sure to remove the selected member from the organizational structure?", "remove_member_from_space_or_team_select_content": "Do you want to remove the selected member from the group or from the organizational structure completely?", "remove_member_in_sub_team_err": "This member belongs to the subordinate group of the current group. Please remove the member from that group.", "remove_member_success": "Remove member success", "remove_members_button": "Remove forever", "remove_members_content": "Remove selected members from the organization?", "remove_members_title": "Remove members", "remove_own_permissions_desc": "You are removing your own permission, which may prevent you from viewing this file node after operation", "remove_permissions": "Remove permission", "remove_permissions_desc": "After removing permissions, the member/team may not be able to view the file node", "remove_role": "Remove permission", "removed_member_tomyself": "You removed <a class=\"involveMemberArr\"></a> from the \"<a class=\"spaceName\"></a>\" Space.", "rename": "<PERSON><PERSON>", "rename_role_success_message": "Role renamed successfully", "rename_role_title": "Modify name", "rename_team": "Rename team", "rename_team_fail": "Failed to rename the team", "rename_team_success": "Team renamed", "rename_view": "Rename view", "render_normal": "Regular rendering", "render_prompt": "Rendering optimization", "renew": "Renew immediately", "renewal": "renewal", "renewal_prompt": "Renewal prompt", "renewal_prompt_description": "Your original plan has been removed from the shelves and does not support renewal. You can upgrade to another solution before you renew it.", "renewal_seat_warning": "If you need to upgrade or increase the number of members, ${link}", "reopen": "Reopen it", "report_issues": "Report issues", "report_issues_github_url": "https://github.com/apitable/apitable/issues", "report_reason_1": "Violation of national law", "report_reason_2": "Piracy, without the legal authorization of the right holder(s)", "report_reason_3": "Leak commercial secrets", "report_reason_4": "Invasion of personal privacy", "report_reason_5": "Spam harassment", "report_success_tip": "Reported successfully", "republic_of_the_congo": "Republic Of The Congo", "request": "API request", "request_in_api_panel": "Make API requests ", "request_in_api_panel_body_warning": "Request data is numerically intensive in this datasheet, the Body parameter can't be automatically brought, you need to fill in manually after forwarding. Do you continue?", "request_in_api_panel_curl": "Make a request", "request_in_api_panel_curl_warning": "Turn the jump to Apifox (including the API token), do you continue? After clicking confirm, the next time will not be reminded.", "request_tree_node_error_tips": "Request failed, please click retry", "require_login_tip": "Editing a shared file node requires login ", "reselect": "Reselect", "reset": "Reset", "reset_password": "Reset password", "reset_password_need_message_verify_code_tip": "SMS verification needed", "reset_password_used_by_phone": "Use phone number to reset password", "reset_password_via_emai_failed": "Failed to send: ${error_message}", "reset_password_via_emai_success": "<PERSON><PERSON> successfully", "reset_password_via_email": "Reset password via email", "reset_permission": "Restore permissions", "reset_permission_content": "Members and teams will restore inherited permissions to the parent folder, and the set permissions will be closed at the same time", "reset_permission_default": "Restore default", "reset_permission_desc": "Permission has been restricted, and no longer inherit from the parent folder", "reset_permission_desc_root": "Permission has been restricted. Only the following members can access", "resource_load_failed": "Loading failed, please refresh and try again", "response_status_code": "Response to status code ", "response_status_code_desc": "Response to status code description", "rest": "Remaining", "rest_consumption": "Remaining: ${count}", "rest_storage": "Remaining: ${memory}", "restore": "Rest<PERSON>", "restore_space": "Restore Space", "restore_space_confirm_delete": "This Space will be deleted forever at this given time", "restore_success": "Restored successfully", "retail": "Retail", "retrieve_password": "Forgot password", "reunion_island": "Réunion Island", "revoke_changes": "Revoke changes", "revoke_logout": "Account Recovery", "right": "Move to the right cell", "robot": "Automation", "robot_action_config": "Action configuration", "robot_action_delete": "Delete action", "robot_action_delete_confirm_desc": "This action may stop the automation from working. Continue?", "robot_action_delete_confirm_title": "Delete action", "robot_action_guide": "Actions", "robot_action_guide_then": "Then do these actions", "robot_action_send_dingtalk_config_1": "DingTalk webhook URL", "robot_action_send_dingtalk_config_1_desc": "Specify a DingTalk automation to send a message to the chat group it's in [How to get webhook](http://help.dolphintable.suanlihaiyang.com/docs/manual-automation-robot/actions/send-message-to-dingtalk)", "robot_action_send_dingtalk_config_2": "Message type", "robot_action_send_dingtalk_config_2_desc": "Currently you can only send plain text and Markdown messages", "robot_action_send_dingtalk_config_3": "Message content", "robot_action_send_dingtalk_config_3_desc": "Enter the message to be sent to the DingTalk chat group (Input \"/\" to start inserting variables)", "robot_action_send_dingtalk_config_4": "Message title", "robot_action_send_dingtalk_config_4_desc": "The message title will appear as a summary on the left-side message list", "robot_action_send_dingtalk_desc": "When the automation starts working, it will automatically send a message to your DingTalk chat group", "robot_action_send_dingtalk_message_type_1": "Plain text", "robot_action_send_dingtalk_message_type_2": "<PERSON><PERSON>", "robot_action_send_dingtalk_title": "Send a message to DingTalk", "robot_action_send_lark_config_1": "Lark webhook URL", "robot_action_send_lark_config_1_desc": "Specify a Lark automation to send a message to the chat group it's in [How to get webhook](http://help.dolphintable.suanlihaiyang.com/docs/manual-automation-robot/actions/send-message-to-lark)", "robot_action_send_lark_config_2": "Message type", "robot_action_send_lark_config_2_desc": "Currently you can only send plain text and Markdown messages", "robot_action_send_lark_config_3": "Message content", "robot_action_send_lark_config_3_desc": "Enter the message to be sent to the Lark chat group (Input \"/\" to start inserting variables)", "robot_action_send_lark_desc": "When the automation starts working, it will automatically send a message to your Lark chat group", "robot_action_send_lark_message_markdown_error": "Can't send image to Lark. Please remove the \"![]()\" syntax.", "robot_action_send_lark_message_type_1": "Plain text", "robot_action_send_lark_title": "Send a message to Lark", "robot_action_send_mails_config_1_pleaseholder_1": "smtp.example.com", "robot_action_send_mails_config_1_pleaseholder_2": "465", "robot_action_send_mails_config_2_pleaseholder": "<EMAIL>", "robot_action_send_mails_config_3_pleaseholder": "123456", "robot_action_send_mails_config_4_pleaseholder": "<EMAIL>", "robot_action_send_mails_config_5_pleaseholder": "Example: task reminder notification", "robot_action_send_mails_config_6_pleaseholder": "Enter message content", "robot_action_send_web_request_add_formdata_button": "New key-value pair", "robot_action_send_web_request_add_header_button": "Add", "robot_action_send_web_request_body_formdata": "form-data", "robot_action_send_web_request_body_formdata_desc": "Add one or more key-value pairs in the body", "robot_action_send_web_request_body_json": "JSON", "robot_action_send_web_request_body_json_desc": "Enter JSON texts in the body", "robot_action_send_web_request_body_raw": "raw", "robot_action_send_web_request_body_raw_desc": "Enter raw texts in the body", "robot_action_send_web_request_body_text": "Text", "robot_action_send_web_request_config_1": "Request method", "robot_action_send_web_request_config_1_desc": "Send web request via the GET, POST or other methods", "robot_action_send_web_request_config_2": "Request address", "robot_action_send_web_request_config_2_desc": "The automation will send web request to the following address", "robot_action_send_web_request_config_3": "Header", "robot_action_send_web_request_config_3_desc": "Add one or more key-value pairs in the header", "robot_action_send_web_request_config_4": "Body", "robot_action_send_web_request_desc": "When the automation starts working, it will automatically send a web request to a specific URL", "robot_action_send_web_request_method_1": "GET", "robot_action_send_web_request_method_2": "POST", "robot_action_send_web_request_method_3": "PATCH", "robot_action_send_web_request_method_4": "DELETE", "robot_action_send_web_request_title": "Send a web request", "robot_action_send_wework_config_1": "WeCom automation webhook URL", "robot_action_send_wework_config_1_desc": "Specify a WeCom automaion to send a message to the chat group it's in [How to get webhook](http://help.dolphintable.suanlihaiyang.com/docs/manual-automation-robot/actions/send-message-to-wecom)", "robot_action_send_wework_config_2": "Message type", "robot_action_send_wework_config_2_desc": "Currently you can only send plain text and Markdown messages", "robot_action_send_wework_config_3": "Message content", "robot_action_send_wework_config_3_desc": "Enter the message to be sent to the WeCom chat group (Input \"/\" to start inserting variables)", "robot_action_send_wework_desc": "When the automation starts working, it will automatically send a message to your WeCom chat group", "robot_action_send_wework_message_type_1": "Plain text", "robot_action_send_wework_message_type_2": "<PERSON><PERSON>", "robot_action_send_wework_title": "Send a message to WeCom", "robot_action_type": "Action type", "robot_auto_desc": "Automation description: ", "robot_cancel_save_step_button": "Cancel", "robot_change_action_tip_content": "This action will clear up the current action configuration. Continue?", "robot_change_action_tip_title": "Change action", "robot_change_trigger_tip_content": "This action will clear up the current trigger configuration. Continue?", "robot_change_trigger_tip_title": "Change trigger", "robot_config_empty_warning": "Please fill in the required field", "robot_config_help_url": "https://help.aitable.ai/docs/manual-automation-robot/#how-to-config-automation", "robot_config_incomplete_tooltip": "Please complete the configuration", "robot_config_panel_help_tooltip": "See documentation ", "robot_config_panel_title": "Configure Automation", "robot_create_name_placeholder": "Automation", "robot_create_wizard_next": "Next", "robot_create_wizard_step_1": "Edit automation name", "robot_create_wizard_step_1_desc": "Hi, I'm your automation. Please give me a name~", "robot_create_wizard_step_2": "Select trigger", "robot_create_wizard_step_2_desc": "When should I start working? Please select a trigger", "robot_create_wizard_step_3": "Add an Action", "robot_create_wizard_step_3_desc": "What should I do when I'm started? Please select an action", "robot_create_wizard_step_4": "View automation", "robot_create_wizard_step_4_button": "View automation", "robot_create_wizard_step_4_desc": "Congrats! You've created a automation. Now you can view and configure your automation.", "robot_delete": "Delete automation", "robot_delete_confirm_desc": "You can't recover the automation after deletion. Continue?", "robot_delete_confirm_title": "Delete Automation", "robot_disable_create_tooltip": "Please apply to enable the Automation feature first", "robot_edit_desc": "Edit automation description", "robot_enable_config_incomplete_error": "Please complete the automation configuration first", "robot_enter_body_text_placeholder": "Enter a text", "robot_enter_key_placeholder": "Enter a key", "robot_enter_message_content_placeholder": "Enter message content ", "robot_enter_request_address_placeholder": "Enter request address", "robot_enter_value_placeholder": "Enter a value", "robot_enter_webhook_placeholder": "Enter a webhook URL ", "robot_feature_entry": "Automation", "robot_help_url": "https://help.aitable.ai/docs/manual-automation-robot", "robot_inserted_variable_invalid": "Invalid variable", "robot_inserted_variable_part_1": "Step ${number}", "robot_more_operations_tooltip": "More operations", "robot_new_action": "Add an action", "robot_new_action_tooltip": "Add an action", "robot_no_step_config_1": "Add a trigger", "robot_option_invalid_error": "Invalid option", "robot_panel_create_tab": "New automation", "robot_panel_help_tooltip": "See documentation ", "robot_panel_no_robot_tip": "Automation will react to data changes 24/7. Unleash your creativity with Automation!", "robot_panel_title": "Automation", "robot_reach_count_limit": "This datasheet has reached the maximum number of automations", "robot_rename": "Rename Automation", "robot_required_error": "This field is required", "robot_return": "Back", "robot_run_history_bottom_tip": "No more, only last 2 calendar months shown", "robot_run_history_desc": "You have access to all run history during the public beta <a href='https://help.aitable.ai/docs/manual-automation-robot#how-to-troubleshoot' target=\\\"_blank\\\">Trouble shooting</a>", "robot_run_history_error": "Error", "robot_run_history_fail": "Failed", "robot_run_history_fail_tooltip": "This step failed. Please check details for troubleshooting.", "robot_run_history_fail_unknown_error": "Sorry, an unknown error occurred, please check the automation configuration and try again", "robot_run_history_input": "Input", "robot_run_history_no_data": "No runs yet", "robot_run_history_no_output": "No output", "robot_run_history_old_version_tip": "Sorry, this run history was from an older version of the datasheet, which does not support viewing the details", "robot_run_history_output": "Output", "robot_run_history_returned_data": "Returned data", "robot_run_history_running": "Running", "robot_run_history_status_code": "Status code", "robot_run_history_success": "Success", "robot_run_history_title": "Run history", "robot_run_history_tooltip": "Run history", "robot_save_step_button": "Save", "robot_save_step_failed": "Fail to saved", "robot_save_step_success": "Step saved", "robot_select_option": "Select an option", "robot_select_option_invalid": "Invalid option", "robot_share_page_create_tip": "You have no permission to create automation", "robot_trigger_add_match_condition_button": "New match condition", "robot_trigger_config": "Trigger configuration", "robot_trigger_delete": "Delete trigger", "robot_trigger_form_submitted_config_1": "Select a form", "robot_trigger_form_submitted_config_1_desc": "Specify one form: When it receives a new response, the automation starts running", "robot_trigger_form_submitted_desc": "When a form receives a new response, the automation will start working", "robot_trigger_form_submitted_title": "Form is submitted", "robot_trigger_guide": "<PERSON><PERSON>", "robot_trigger_match_condition_and": "And", "robot_trigger_match_condition_or": "Or", "robot_trigger_match_condition_when": "When", "robot_trigger_or": " or ", "robot_trigger_record_created_config_1": "Select a datasheet", "robot_trigger_record_created_config_1_desc": "Specify one datasheet: When a record is created in it, the automation starts running", "robot_trigger_record_created_desc": "Once a record is created, either by manual operation, API, or form collection, the automation will start working", "robot_trigger_record_created_title": "Record is created", "robot_trigger_record_matches_condition_cannot_access_field": "You have no permission to view the field in the match condition ", "robot_trigger_record_matches_condition_config_1": "Select a datasheet", "robot_trigger_record_matches_condition_config_1_desc": "Specify one datasheet: When a record in it matches condition, the automation starts running", "robot_trigger_record_matches_condition_config_2": "Select match conditions", "robot_trigger_record_matches_condition_config_2_desc": "Note: Adding Date/Formula field to trigger at scheduled/due time is not supported [FAQ](https://help.aitable.ai/docs/manual-automation-robot#robot-scene-related-faq)", "robot_trigger_record_matches_condition_desc": "When a record matches predefined conditions, the automation will start working", "robot_trigger_record_matches_condition_invalid_field": "Invalid match condition ", "robot_trigger_record_matches_condition_title": "Record matches conditions", "robot_trigger_type": "Trigger type", "robot_unnamed": "Unnamed Automation", "robot_variables_array_flatten": "Convert to linear array", "robot_variables_array_length": "Array length", "robot_variables_breadcrumb_column_type": "${column_type} column", "robot_variables_breadcrumb_record": "Record", "robot_variables_breadcrumb_selecting": "(Please select)", "robot_variables_cant_view_field": "No permission to view", "robot_variables_creator_ID": "Creator ID", "robot_variables_creator_avatar": "Creator avatar", "robot_variables_creator_name": "Creator name", "robot_variables_datasheet_ID": "Datasheet ID", "robot_variables_datasheet_URL": "Datasheet URL", "robot_variables_datasheet_name": "Datasheet name", "robot_variables_date_to_timstamp": "Convert date to timestamp", "robot_variables_editor_ID": "Editor ID", "robot_variables_editor_avatar": "Editor avatar", "robot_variables_editor_name": "Editor name", "robot_variables_insert_button": "Insert", "robot_variables_join_array_item_property": "Join array items' property", "robot_variables_join_attachment_IDs": "Join attachment IDs", "robot_variables_join_attachment_URLs": "Join attachment URLs", "robot_variables_join_attachment_heights": "Join attachment heights", "robot_variables_join_attachment_mime_types": "Join attachment MIME types", "robot_variables_join_attachment_names": "Join attachment names", "robot_variables_join_attachment_preview_image_token": "Join attachment preview image tokens", "robot_variables_join_attachment_sizes": "Join attachment sizes", "robot_variables_join_attachment_storage_locations": "Join attachment storage locations", "robot_variables_join_attachment_thumbnail_URLs": "Join attachment thumbnail URLs", "robot_variables_join_attachment_types": "Join attachment types", "robot_variables_join_attachment_upload_token": "Join attachment upload tokens", "robot_variables_join_attachment_widths": "Join attachment widths", "robot_variables_join_color_names": "Join color names", "robot_variables_join_color_values": "Join color values", "robot_variables_join_linked_record_IDs": "Join linked record IDs", "robot_variables_join_linked_record_titles": "Join linked record titles", "robot_variables_join_member_IDs": "Join member IDs", "robot_variables_join_member_avatars": "Join member avatars", "robot_variables_join_member_names": "Join member names", "robot_variables_join_member_types": "Join member types", "robot_variables_join_option_IDs": "Join option IDs", "robot_variables_join_option_color_names": "Join option color names", "robot_variables_join_option_color_values": "Join option color values", "robot_variables_join_option_colors": "Join option colors", "robot_variables_join_option_names": "Join option names", "robot_variables_join_url_link": "Url link", "robot_variables_join_url_title": "Url title", "robot_variables_join_workdoc_id": "WorkDoc ID", "robot_variables_join_workdoc_name": "WorkDoc title", "robot_variables_more_operations": "More", "robot_variables_option_ID": "Option ID", "robot_variables_option_color": "Option color", "robot_variables_option_name": "Option name", "robot_variables_record_ID": "Record ID", "robot_variables_record_URL": "Record URL", "robot_variables_select_basics": "Basic info", "robot_variables_select_column_property": "Column property", "robot_variables_select_columns": "Column", "robot_variables_select_step": "Pre-step", "robot_variables_select_step_no_output_type": "No output (Step ${number}: ${step})", "robot_variables_select_step_record_type": "Record (Step ${number}: ${step})", "robot_variables_stringify_json": "Convert to JSON string", "robot_variables_unsupported_column_type": "Unsupported column type", "robot_variables_user_ID": "${column_type} ID", "robot_variables_user_icon": "${column_type} icon", "robot_variables_user_name": "${column_type} name", "role_context_item_delete": "Delete", "role_context_item_rename": "<PERSON><PERSON>", "role_item": "${count} item(s)", "role_member_table_empty": "There are no members in this role. You can ", "role_member_table_header_name": "Member / Team", "role_member_table_header_team": "Team", "role_name_input_placeholder": "Please enter the name of the role", "role_permission_manage_integration": "Manage integrations", "role_permission_manage_main_admin": "Manage admins", "role_permission_manage_member": "Manage members", "role_permission_manage_normal_member": "Manage members", "role_permission_manage_role": "Manage roles", "role_permission_manage_security": "Manage security settings", "role_permission_manage_space": "Manage Space information", "role_permission_manage_sub_admin": "Manage sub-admins", "role_permission_manage_team": "Manage teams", "role_permission_manage_template": "Manage templates", "role_permission_manage_widget": "Manage Widget Center", "role_permission_manage_workbench": "Manage file node permissions", "rollback": "Rollback", "rollback_fail_content": "The current version has data conflicts and does not support rollback, <a href=\"${url}\" target=\"_blank\">Learn more</a>", "rollback_fail_tip": "${type} Failed, please try again!", "rollback_fail_title": "Rollback failed", "rollback_history_empty": "No available operation history", "rollback_operator_field": "Operators：", "rollback_revision": "Rest<PERSON>", "rollback_time_field": "Time：", "rollback_tip": "The rollback operation has been executed, please view the data in the datasheet", "rollback_title": "Rollback to ${revision} version", "rollback_version_field": "Version：", "rollbacking": "Loading, please wait a moment...", "rollup_choose_field": "Select a field from linked datasheet", "rollup_choose_table": "Select a linked datasheet ", "rollup_choose_table_description": "You can select the datasheet which linked by <PERSON> in current datasheet. If you couldn't find the datasheet that you want to link, please create a new One-way Link or Two-way Link field first.", "rollup_conditions_num": "${ORDER_BY_NUM} sorting conditions and  ${FILTER_NUM} filtering conditions", "rollup_field": "Column referenced in the related datasheet", "rollup_filter_sort": "Filter and sort the linked field", "rollup_filter_sort_description": "Add condition", "rollup_filter_sort_popup_setting": "Sort setting", "rollup_formula": "Reference method", "rollup_limit": "How many records to roll up", "rollup_limit_option_1": "All", "rollup_limit_option_2": "First", "rollup_sort_description": "You can sort data order by fields (Temporarily only supports setting one field)", "romania": "Romania", "rotate": "Spin", "rotate_upgrade_txt": "true", "row": " Row(s)", "row_height": "Row height", "row_height_extra_tall": "Extra tall", "row_height_medium": "Medium", "row_height_setting": "Row height setting", "row_height_short": "Short", "row_height_tall": "Tall", "rows_limit_5000_limit_tips": "Your datasheet has the maximum number of records", "rows_per_datasheet": "The maximum number of records per datasheet", "runlog": "Run history", "russia": "Russia", "rwanda": "Rwanda", "safety_tip": "Security warning", "safety_verification": "Security verification", "safety_verification_tip": "Please drag the slider below to complete the verification", "saint_kitts_and_nevis": "Saint Kitts and Nevis", "saint_lucia": "Saint Lucia", "saint_maarten_dutch_part": "<PERSON> (Dutch Part)", "saint_pierre_and_miquelon": "Saint Pierre and Miquelon", "saint_vincent_and_the_grenadines": "Saint Vincent and The Grenadines", "sales_and_customers": "Sales & Customers", "samoa": "Samoa", "san_marino": "San Marino", "sao_tome_and_principe": "Sao Tome and Principe", "saudi_arabia": "Saudi Arabia", "save": "Save", "save_action_desc": "You can save this file node as a copy", "save_as_template": "Save as template", "save_document": "Save as copy", "save_template_disabled": "Cannot save as template", "save_this_modified": "Save changes", "save_to_space": "Save to my Space", "save_view_configuration": "Save", "scan_code_to_join_team": "<PERSON>an the code to join the team", "scan_to_login": "<PERSON><PERSON> to login", "scan_to_login_by_method": "Please scan ${method} to follow official account to login", "scatter_chart": "Scatter Chart", "schedule_day_tips": "The cycle calculation begins counting from the first day of each month. If we assume that it repeats every 10 days, then it will be triggered on the 1st, 11th, 21st and 31st day of each month", "schedule_hour_tips": "The cycle calculation begins at midnight (0:00) each day. Assuming it repeats 0 minutes every three hours, it will occur at midnight (0:00), 3 AM, 6 AM, 9 AM, noon (12 PM), \n\n3 PM，6 PM, and finally at nightfall (9 PM) daily", "schedule_start_day": "Starting from the 1st day of the month, ", "schedule_start_month": "Starting from January each year, every", "schedule_type": "Schedule Type", "schedule_year_tips": "The cycle calculation begins counting from the first month of each year. Assuming an interval of first day 3 months, triggers will be activated at midnight on the first day of January, April, July, and October every year.", "science_and_technology": "Science and technology", "scroll_screen_down": "Scroll one screen down", "scroll_screen_left": "Scroll one screen left", "scroll_screen_right": "Scroll one screen right", "scroll_screen_up": "Scroll one screen up", "search": "Search", "search_associate_record": "Find a record to link to", "search_field": "Search", "search_fields": "Search column", "search_folder_or_form": "Find a folder or form ", "search_folder_or_sheet": "Find a folder or datasheet ", "search_new_admin": "Search", "search_node_pleaseholder": "Search for file nodes (${shortcutKey})", "search_node_tip": "Quick search (${shortcutKey})", "search_or_add": "Find or add an option", "search_role_placeholder": "Search roles", "seats": "Seats", "second_prize": "", "second_prize_name": "", "second_prize_number": "", "section1_desc": "", "section1_tip": "", "section1_title": "", "section1_title_highligh": "", "section2_sub_title1": "", "section2_sub_title2": "", "section2_tips": "", "section2_title": "", "section2_title_highligh": "", "section3_step1": "", "section3_step2": "", "section3_step3": "", "section3_title": "", "section4_nickname": "Name", "section4_title": "", "section5_empty": "", "section6_desc": "APITable, not a spreadsheet, is a new generation of data productivity platform. The creator of multi-dimensional tables, achieves the in-depth customization of enterprise/team systems without coding. As the \"next necessary skill for the workplace\", it is easier to use than Excel and more practical than Python. APITable can achieve:", "section6_list_item1": "", "section6_list_item2": "", "section6_list_item3": "", "section6_list_item4": "", "section6_list_item5": "", "section6_title": "What is APITable?", "security_address_list_isolation": "Hide other teams and members in Contacts", "security_address_list_isolation_describe": "Members can only view their teams and members(including sub-teams) in Contacts", "security_address_list_isolation_description": "Members can only view their teams and members(including sub-teams) in Contacts", "security_advanced_tip": "Advanced space exclusive features", "security_disabled_apply_join_space": "Prevent users from applying to join the Space in the sharing page ", "security_disabled_apply_join_space_describle": "The \"Apply to join this Space\" entry is no longer displayed on the sharing page, and users cannot apply to join", "security_disabled_apply_join_space_modal_describle": "Allow visitors to request for joining the Space from the sharing page (After the admin approves the request, they can automatically join in)", "security_disabled_apply_join_space_modal_title": "Allow requests to join the Space", "security_disabled_copy_cell_data": "Prevent read-only users from copy cell data out of the Space", "security_disabled_copy_cell_data_describle": "Users with \"read-Only\" permission cannot copy data in cells", "security_disabled_copy_cell_data_modal_describle": "Users with \"read-Only\" permission can copy data in cells, both on and off the Space", "security_disabled_copy_cell_data_modal_title": "Allow read-only users to copy data ", "security_disabled_copy_cell_date": "Prohibit read-only members from copying data to outside the space", "security_disabled_copy_cell_date_tip": "Prohibit all members from copying the data of the datasheet to other software such as Excel", "security_disabled_download_file": "Prevent read-only users from downloading attachments", "security_disabled_download_file_describle": "Users with \"read-Only\" permission cannot download attachments in cells, both on and off the Space", "security_disabled_download_file_modal_describle": "Users with \"read-Only\" permission can download attachments in cells, both on and off the Space", "security_disabled_download_file_modal_title": "Allow read-only users to export file nodes", "security_disabled_download_file_tip": "Users with \"read-Only\" permission cannot download attachments in cells", "security_disabled_export": "Prohibition of file node sharing", "security_disabled_export_data": "Prevent members from exporting datasheet or view", "security_disabled_export_data_describle": "All members cannot export datasheet or view data locally", "security_disabled_export_data_modal_describle": "If members have the \"manageable\" permission to a datasheet, they can export it to a local file.", "security_disabled_export_data_modal_title": "Allow to export file nodes", "security_disabled_export_tip": "Prohibit all members from sharing file nodes outside the space", "security_disabled_invite_member": "Prevent members from inviting user", "security_disabled_invite_member_describle": "No one can invite users to join the Space except Space admin,and the generated invitation link will be invalid", "security_disabled_invite_member_modal_describle": "Once switched on, all members can invite new members from the contacts panel", "security_disabled_invite_member_modal_title": "Allow to invite members", "security_disabled_share": " Disable sharing file nodes", "security_disabled_share_describle": "All members cannot creat the public link of the file node, and the generated public link becomes invalid", "security_disabled_share_modal_describle": "members can open the public link of the file node", "security_disabled_share_modal_title": "Allow to share file nodes", "security_features": "Security", "security_setting_address_list_isolation": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_apply_join_space": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_apply_join_space_describle": "After the function is disabled,the \"Apply to join this Space\" entry is no longer displayed on the sharing page, and users cannot request for joining", "security_setting_apply_join_space_description": "The \"Apply to join this Space\" entry is no longer displayed on the sharing page, and users cannot request for joining", "security_setting_apply_join_space_title": "Prevent users from applying to join the Space in the sharing page ", "security_setting_catalog_management": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_catalog_management_describle": "Members can create new nodes to the root of the Working catalog. After the function is disabled, only Space admin can create.", "security_setting_catalog_management_description": "Members can't create new file nodes at the root of the catalog. After toggling this on, only space admins can create new file nodes at the root of the catalog.", "security_setting_catalog_management_title": "Prevent members from creating new file nodes at the root of the catalog", "security_setting_copy_cell_data": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_copy_cell_data_describle": "After the function is disabled,users with \"read-Only\" permission cannot copy cell data, both on and off the Space", "security_setting_copy_cell_data_description": "Users with \"Read-only\" permission cannot copy data in cells", "security_setting_copy_cell_data_title": "Prevent \"Read-only\" users from copying data", "security_setting_download_file": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_download_file_describle": "Users with \"Read-only\" permission cannot download attachments in cells", "security_setting_download_file_description": "Users with \"Read-only\" permission cannot download attachments in cells", "security_setting_download_file_title": "Prevent \"Read-only\" users from downloading attachments", "security_setting_export": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_export_data_describle": "Specify members to export data.If the function is disabled,only space andmin can export data.", "security_setting_export_data_description": "Specify members to export data.If the function is disabled,only space andmin can export data.", "security_setting_export_data_editable": "above \"Editor\"", "security_setting_export_data_manageable": "above \"Manager\"", "security_setting_export_data_read_only": "above \"Read-only\"", "security_setting_export_data_title": "Specify members to export datasheet and views", "security_setting_export_data_tooltips": "You must first enable this feature", "security_setting_export_data_updatable": "above \"Update-only\"", "security_setting_invite_member": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_invite_member_describle": "After the function is disabled,no one can invite users to join the Space except Space admin,and the generated invitation link will be invalid", "security_setting_invite_member_description": "No one can invite users to join the Space except Space admin,and the generated invitation link will be invalid", "security_setting_invite_member_title": "Prevent members from inviting user", "security_setting_mobile": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_share": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "security_setting_share_describle": "All members cannot creat the public link of the file node, and the generated public link becomes invalid", "security_setting_share_description": "All members cannot creat the public link of the file node, and the generated public link becomes invalid", "security_setting_share_title": "Prevent members from creating public links", "security_show_mobile": "Show phone numbers", "security_show_mobile_describle": "Show all members' phone numbers in contacts", "security_show_mobile_description": "Show all members' phone numbers in contacts", "security_show_mobile_modal_describle": "The mobile phone number is not displayed in the  contacts", "security_show_mobile_modal_title": "Show phone numbers", "security_show_watermark": "Show watermarks", "security_show_watermark_describle": "The workbench and the contacts support watermarks to prevent members from leaking enterprise information. A watermark includes the name and phone number suffix/email prefix.", "security_show_watermark_description": "The workbench and the contacts support watermarks to prevent members from leaking enterprise information. A watermark includes the name and phone number suffix/email prefix.", "security_show_watermark_modal_describle": "The watermarks is not displayed in the contacts", "security_show_watermark_modal_title": "Show watermarks", "see_more": "See more", "select": "Select", "select_all": "Select all\n", "select_all_fields": "Select all\n", "select_automation_node": "Select Automation Node", "select_axis_sort": "Select an axis to sort by", "select_bar_chart_x_axis": "select X-axis", "select_bar_chart_y_axis": "select X-axis", "select_chart_category": "Dimension", "select_chart_type": "Select chart type", "select_chart_values": "Values", "select_column_chart_x_axis": "Select X axis", "select_column_chart_y_axis": "Select Y axis", "select_data_source": "Function settings", "select_end_date": "Select an end time", "select_form_panel_title": "Select a form", "select_layout": "Choose layout", "select_link_data_number": "Select the number of linked records", "select_link_data_number_all": "Link all records", "select_link_data_number_first": "<PERSON> the first record", "select_local_file": "Drag and drop attachments or click here to upload", "select_one_field": "Select field type", "select_phone_code": "Select country and region", "select_sort_rule": "Select sort rule", "select_space_save": "Select the space to save", "select_start_date": "Select a start time", "select_theme_color": "Select theme color", "select_view": "Select a view as the data source", "select_wdget_Import_widget": "Select a datasheet to import widget", "select_widget_Import_widget": "Import widget from the datasheet below", "select_y_axis_field": "Count a field", "selected": "Selected", "selected_with_workdoc_no_copy": "The selection with the WorkDoc field cannot be copied", "selection_to_down": "Select cells downwards", "selection_to_down_edge": "Select cells downwards to the bottom", "selection_to_left": "Select cells towards the left", "selection_to_left_edge": "Select cells to the left-most", "selection_to_right": "Select cells towards the right", "selection_to_right_edge": "Select cells to the right-most", "selection_to_up": "Select cells upwards", "selection_to_up_edge": "Select cells upwards to the top", "self_hosting": "Self Hosting", "send": "Send", "send_again_toast": "The user has not completed the registration. Resend the email invitation?", "send_code_again": "Resend", "send_comment_tip": "Enter: send", "send_verification_code_to": "Send verification code to ${mobile}", "send_widget_to_dashboard_success": "Widget sent to the dashboard", "send_widget_to_dashboard_success_link": "Widget sent to dashboard", "senegal": "Senegal", "senior_field": "Advanced column type", "serbia": "Serbia", "server_error_page_bg": "/space/2022/05/26/5c9cf8bc47074c8e95600147d818c11b?attname=500_en%402x.png", "server_error_tip": "The server is under maintenance. Please wait for a few minutes and refresh again.", "server_pre_publish": "", "set_alarm_disable": "You won't get any reminders", "set_alarm_fail_tips": "Failed setting remiders because records were deleted", "set_alarm_field_delete_tips": "Current field was deleted", "set_alarm_menu": "Set ${number}  task reminder(s)", "set_alarm_success_tips": "Successfully setted ${number} reminders", "set_alarm_switch": "Reminder(s)", "set_alarm_title": "Reminder settings", "set_as_the_template": "Set as template", "set_field": "Field setting", "set_field_permission_modal_title": "Set field permissions to ${name}", "set_field_permission_no_access": "You have no access to view and edit permissions of this field", "set_field_required": "Set as required on forms", "set_field_required_tip_1": "1. When you expand the record, the field will show \"*\" (means \"required\")", "set_field_required_tip_2": "2. Forms must be submitted after the field is filled", "set_field_required_tip_title": "Once set as required:", "set_filter": "Filter setting", "set_gallery_card_style": "Customize cards", "set_graphic_field": "Graphic field settings", "set_grouping": "Group setting", "set_new_password": "Set a new password", "set_nickname": "Set nickname", "set_password": "Password", "set_permission": "Granting Permissions", "set_permission_add_member_modal_search": "Search for team or member", "set_permission_add_member_modal_title": "Add \"${node_role}\"Member", "set_permission_include_oneself_tips_description": "Since the team you're editing includes yourself, your permission may also update after this setting is saved.", "set_permission_include_oneself_tips_title": "Permission change", "set_permission_modal_add_node_role": "Add member of ${node_role}", "set_permission_modal_help": "https://help.aitable.ai/docs/faq-permission-settings", "set_permission_modal_radio_1": "Inherit superior permission", "set_permission_modal_radio_1_description": "Member and team inherit the permissions of the superior folder\"${parent_node_name}\"", "set_permission_modal_radio_2": "File node permissions", "set_permission_modal_radio_2_description": "Separately specify permission of member and team for the current ${node_type}", "set_permission_modal_title": "Assign permissions to members in ${name}", "set_permission_success_tips": "Permission set successfully", "set_record": "Set record", "set_sort": "Sort setting", "setting_nickname_sub_title": "Get yourself a name", "setting_nickname_title": "Congratulations on landing on APITable", "setting_permission": "Set permissions", "seychelles": "Seychelles", "share": "Share", "shareAndPermission_illustration": "shareAndPermission illustration", "share_and_collaboration": "Sharing and collaboration", "share_and_editable_desc": "Others can edit the content of the datasheet within the shared page", "share_and_editable_title": "Share with others for editing", "share_and_permission_member_detail": "Sharing with ${count} member(s)", "share_and_permission_open_share_tip": "After turning on, users outside the Space can view the content of the datasheet.", "share_and_permission_open_share_title": "Enable sharing", "share_and_permission_popconfirm_title": "Change sharing option", "share_and_permission_share_link": "Public link (valid permanently)", "share_and_save_desc": "Others can view your datasheet and save it as a copy", "share_and_save_title": "Share and allow others to save as a copy", "share_card_tips": "Scan the code to view content", "share_code_desc": "Simpler, but more powerful", "share_configuration": "Sharing setting", "share_copy_url_link": "Copy link", "share_edit_exist_member_tip": "The current datasheet has ${content}. Confirm only if you allow others to view the organizational chart of the Space.", "share_edit_tip": "The file node is read-only before you log in. Please log in and try again.", "share_editor": "Log in to edit", "share_editor_label": "Log in to edit", "share_email_invite": "Invite collaborators into your Space", "share_embed": "Embed", "share_exist_something_tip": "You're sharing a datasheet that contains ${content}. Others can view it via the sharing page. Please make sure you don't disclose any sensitive information. Linked datasheets are:\n", "share_fail_og_description_content": "The shared public link has been closed and is temporarily unavailable", "share_failed": "Sharing failed", "share_field_shortcut_link_tip": "Want to create a public link? Click here", "share_file": "Share outside the Space", "share_file_desc": "Share outside the Space via public links", "share_form_edit_tip": "Please log in first to submit the form", "share_form_login_tip": "Log in", "share_form_title": "Fill in the form via the public link", "share_invite_no_permission": "You do not have permission to invite members", "share_link_text": "", "share_login_tip": "Log in to edit file node", "share_mobile_friendly_tip": "88% of people get a better experience by editing on the computer", "share_modal_desc": "Copy the exclusive link below~ <br/>Invite friends to use APITable<br/>Share with friends", "share_modal_title": "Share", "share_node_number_err_content": "share_node_number_err_content", "share_only_desc": "External users outside the space can only view the file node", "share_only_title": "Share with others for reading-only", "share_permisson_model_link_datasheet_label": "Related external file nodes", "share_permisson_model_link_datasheet_label_desc": "The current file node (folder) is related with an external Datasheet", "share_permisson_model_node_owner": "File node owner", "share_permisson_model_node_owner_desc": "The member has enabled manual permission setting for this file node", "share_permisson_model_open_share_false_1": "Failed to share. Someone else has enabled the sharing.", "share_permisson_model_open_share_label": "Sharing enabled", "share_permisson_model_open_share_label_desc": "${member_name} enables sharing, and external users can access the content via the public link", "share_permisson_model_setting_role_label": "Specify visible", "share_permisson_model_setting_role_label_desc": "Except for the specified member(s), other member(s) cannot access", "share_permisson_model_space_admin": "Space admin", "share_permisson_model_space_admin_desc": "This Space has the admin(s) with the permission of \"workbench management\"", "share_permisson_model_space_admin_tip": "You can change the admin", "share_qr_code_tips": "Share via QR code", "share_reader": "read-only", "share_reader_label": "This shared content can be viewed only", "share_save": "Save as copy", "share_save_label": "This shared content can be saved as a copy", "share_setting": "Share outside the Space", "share_settings_tip": "Sharing settings updated ${status}", "share_succeed": "File node shared", "share_tips": "Publish a share link for this file node. Users who obtain the link on the Internet can view, edit, or copy the file node based on their permissions.", "share_tips_title": "Publish share link", "share_title": "Share \"${node}\"", "share_with_offsite_users": "Share with users outside the Space", "shared_link_copied": "Template link copied", "sharing_guidelines": "Click on the upper right corner to share", "shelf_manage": "Shelf management", "shortcut_key": "Shortcuts", "shortcut_key_redo": "Action redone", "shortcut_key_redo_nothing": "No action to redo", "shortcut_key_undo": "Action undone", "shortcut_key_undo_nothing": "No action to undo", "should_not_empty": "${name} can not be empty", "show_all_fields": "Show all", "show_data_tips": "Show data labels", "show_data_tips_describle": "Show data labels", "show_empty_values": "Show empty values", "show_empty_values_describle": "Show empty values", "show_field_desc": "Show field description", "show_hidden_field_within_mirror": "", "show_hidden_fields_by_count": "Expand ${count} hidden field(s)", "show_name": "Show name", "show_record_history": "Show revision history", "show_smooth_line": "Show smooth curve", "sierra_leone": "Sierra Leone", "sign_up": "Sign up", "signin_idaas_official_account": "Dolphin Planet", "silver": "Silver", "silver_grade": "Silver", "silver_grade_6months_time_machine": "Upgrade to the Silver-Pro Space to view the last 6-month historical revision record", "silver_grade_desc": "For teams with rapid business growth", "silver_grade_unlimited": "Upgrade to the Silver-Pro Space to enjoy unlimited upload.", "silver_img": "URL", "silver_seat_100_desc": "100", "silver_seat_2_desc": "2(50% OFF)", "singapore": "Singapore", "single_color_gradient_theme": "Monochrome gradient theme", "single_record_comment_mentioned": "<a class=\"memberName\"></a> mentioned you in the \"<a class=\"recordTitle\"></a>\" record of the \"<a class=\"nodeName\"></a>\" datasheet.", "single_record_member_mention": "<a class=\"memberName\"></a> mentioned you at \"<a class=\"fieldName\"></a>\" of the \"<a class=\"recordTitle\"></a>\" record in the \"<a class=\"nodeName\"></a>\" datasheet.", "single_sign_on": "Single Sign-On (SSO)", "siwtch_to_invite_tab": "Want to invite members? Click here", "six_months": "6 months", "skip": "<PERSON><PERSON>", "skip_guide": "<PERSON><PERSON>\n", "slider_verification_tips": "Please hold the slider and drag to the right", "slovakia": "Slovakia", "slovenia": "Slovenia", "social_dingtalk_single_record_comment_mention": "", "social_dingtalk_single_record_member_mention": "### 🔔 You're mentioned in a record\n\n** record: ** {recordTitle}\n\n** Mentioned by: ** {memberName}\n\n** Datasheet: ** {nodeName}\n\n** Operation time: ** {createdAt}", "social_dingtalk_subscribed_record_cell_updated": "### Someone has changed the record you are watching in\n\n**Record:** {recordTitle}\n\n**Before:** {oldDisplayValue}\n\n**After:** {newDisplayValue}\n\n**Member:** {memberName}\n\n**Datasheet:** {nodeName}", "social_dingtalk_subscribed_record_cell_updated_title": "Records modified by others", "social_dingtalk_subscribed_record_commented": "### The record you are watching received a new comment\n\n**Record:** {recordTitle}\n\n**Comment:** {content}\n\n**Member:** {memberName}\n\n**Datasheet:** {nodeName}", "social_dingtalk_subscribed_record_commented_title": "Record received a new comment", "social_dingtalk_task_reminder": "### 🔔The record in the datasheet has reached the reminder time\n\n** Record title: ** {recordTitle}\n\n** Expiration time: ** {taskExpireAt}\n\n** Datasheet: ** {nodeName}", "social_lark_task_reminder": "** Record title: ** {recordTitle}\n** Expiration time: ** {taskExpireAt}\n** Datasheet: ** {nodeName}", "social_lark_task_reminder_title": "** The record in the datasheet has reached the reminder time * *", "social_media": "New media operations", "social_notification_url_title": "view", "social_open_card_btn_text": "Detail", "social_plat_bind_space_bound_err": "This space had bound other.", "social_plat_bind_space_seats_err": "Your Space can have a maximum of ${count} members. Contact us if you want to invite more. <a href='https://help.aitable.ai/docs/how-contact-service' target=\"_blank\">", "social_plat_space_list_item_seats_msg": "(oversize:${max}）", "social_task_reminder_title": "🔔 Reminder", "social_wecom_single_record_member_mention": "Record：{recordTitle}\nMentioned by：{memberName}\nDatasheet：{nodeName}", "social_wecom_subscribed_record_cell_updated": "Record：{recordTitle}\nBefore：{oldDisplayValue}\nAfter：{newDisplayValue}\nMember：{memberName}\nDatasheet：{nodeName}", "social_wecom_subscribed_record_commented": "Record：{recordTitle}\nComment：{content}\nMember：{memberName}\nDatasheet：{nodeName}", "social_wecom_task_reminder": "Record title: {recordTitle}\nExpiration time: {taskExpireAt}\nDatasheet: {nodeName}", "socket_error_network": "Oops! A network communication problem occurred, please refresh page", "socket_error_server": "Oops! Something went wrong，please refresh page", "software_development": "Software development", "solomon_islands": "Solomon Islands", "solution": "Solutions", "somalia": "Somalia", "some_day_after": "number of days from now…", "some_day_before": "number of days ago", "some_one_lock_view": "${uer_name}已锁定视图配置", "something_went_wrong": "Something went wrong", "something_wrong": "Please try again later.", "sort": "Sort", "sort_apply": "Apply", "sort_by_option_order": "Sort ascending", "sort_by_option_reverse": "Sort descending", "sort_count_tip": "Sorted by ${count} field(s)", "sort_desc": "Sort ${from} → ${to}", "sort_help_url": "https://help.aitable.ai/docs/manual-sort", "sort_link_data": "Sort linked records", "sort_rules": "Sort condition", "sorting_conditions_setting_description": "You can select a field in the linked datasheet as a sorting condition", "south_africa": "South Africa", "south_korea": "South Korea", "space": "Space", "space_add_primary_admin": "You become the admin of the \"<a class=\"spaceName\"></a>\" Space.", "space_add_sub_admin": "<a class=\"memberName\"></a> added you as the sub-admin of the \"<a class=\"spaceName\"></a>\" Space.", "space_admin": "Space admin", "space_admin_info": "With your Space's subscription plan, you can assign ${count} sub-admins", "space_admin_level": "Subscription plan", "space_admin_limit": "The maximum number of sub-admins to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_admin_limit_email_title": "Usage reminders for the number of sub-admins in the \"${SPACE_NAME}\" space", "space_admins_3_up": "Your Space has the maximum number of admins", "space_admins_unlimited_upgrade": "Upgrade to Silver plan to add more admins", "space_api_limit": "The maximum number of api usages per month to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_api_limit_email_title": "Usage reminders for the number of api usages per month in the \"${SPACE_NAME}\" space", "space_assigned_to_group": "<a class=\"memberName\"></a> assigned you to the \"<a class=\"teamName\"></a>\" team in the \"<a class=\"spaceName\"></a>\" Space.", "space_assigned_to_role": "<a class=\"memberName\"></a> assigned you to the \"<a class=\"roleName\"></a>\" role in the \"<a class=\"spaceName\"></a>\" Space.", "space_calendar_limit": "The maximum number of calendar views to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_calendar_limit_email_title": "Usage reminders for the number of calendar views in the \"${SPACE_NAME}\" space", "space_capacity": "Attachments Storage", "space_capacity_1g_limit_tips": "The size of the attachment space has been exceeded, and you can enjoy a large space after upgrading to a premium space", "space_certification_fail_notify": "Sorry, your space certification failed. The reason may be: < br >\n1. This space does not exist or has been deleted < br >\n2. You are not a member of this space< br >", "space_certification_notify": "Congratulation. You have completed the certification of space. Go through 「Settings-Overview-Attachments storage」 to receive your <a class=\"specification\"></a>GB capacity package.", "space_changed_ordinary_user": "<a class=\"memberName\"></a> changed your role of the \"<a class=\"spaceName\"></a>\" Space to \"Member\".", "space_configuration": "Space configuration", "space_corp_certified": "Certified", "space_corp_uncertified": "Uncertified", "space_corp_uncertified_tooltip": "Complete space information to get a free 5G attachment storage package", "space_dashboard_contact": "Contact the advisor", "space_dashboard_contact_desc": "Want to know more about upgrade renewal, order invoice, after-sales service, etc.? Please contact me", "space_dashboard_contact_title": "Contact your personal advisor", "space_dingtalk_notify": "The Space is using the enterprise-level feature \"DingTalk integration\". Please upgrade to continue using it.", "space_dingtalk_notify_email_title": "The \"{SPACE_NAME}\" Space is using the enterprise-level feature \"DingTalk integration\"", "space_exist_dashboard": "Send widget to dashboard", "space_field_permission_limit": "The maximum number of field permissions to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_field_permission_limit_email_title": "Usage reminders for the number of field permissions in the \"${SPACE_NAME}\" space", "space_file_permission_limit": "The maximum number of file node permissions to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_file_permission_limit_email_title": "Usage reminders for the number of file node permissions in the \"${SPACE_NAME}\" space", "space_form_limit": "The maximum number of forms to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_form_limit_email_title": "Usage reminders for the number of forms in the \"${SPACE_NAME}\" space", "space_free_capacity_expansion": "Free expansion", "space_gantt_limit": "The maximum number of gantt views to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_gantt_limit_email_title": "Usage reminders for the number of gannt views in the \"${SPACE_NAME}\" space", "space_guide_step_one_desc": "You Space will be set up in a minute🔨.\nShall we choose a name and icon for it now?", "space_guide_step_one_tip": "Tip: The Space's name and logo can be changed later in \"Space management\"", "space_guide_success_tip": "Your Space is all set🎉～<br/> \nPlease fasten your seat belt and get started!🚀", "space_has_been_deleted": "The \"<a class=\"spaceName\"></a>\" Space has been deleted.", "space_has_been_recover": "The \"<a class=\"spaceName\"></a>\" Space has been restored.", "space_id": "Space ID", "space_info": "Overview", "space_info_del_confirm1": "1. Deleting this Space will clean up the following data:", "space_info_del_confirm2": "2. The Space will be deleted completely after 7 days. You can restore the Space before then.", "space_info_feishu_desc": "You are using a third-party integration. To delete the Space, please disable the third-party integration first.  ", "space_info_feishu_label": "Integrations", "space_join_apply": "<a class=\"userName\"></a> requested to join the \"<a class=\"spaceName\"></a>\" Space.", "space_join_apply_approved": "Your request to join the \"<a class=\"spaceName\"></a>\" Space has been approved.", "space_join_apply_refused": "Your request to join the \"<a class=\"spaceName\"></a>\" Space has been rejected.", "space_lark_notify": "The space is using \"Lark Integration\", a feature exclusive to enterprise space, please upgrade to continue using it", "space_lark_notify_email_title": "The \"{SPACE_NAME}\" space is using the \"Lark Integration\" feature exclusive to enterprise space", "space_list": "Spaces", "space_log_action_time": "Action time", "space_log_action_type": "Action types", "space_log_actions": "Actions", "space_log_date_range": "Date range", "space_log_download_button": "Download logs", "space_log_file_name": "File node name", "space_log_operator": "Operator", "space_log_title": "Space logs", "space_log_trial_button": "Try now", "space_log_trial_desc1": "You can view the operation logs of the current space station on the current page.", "space_log_trial_desc2": "Click the button below to upgrade or add our customer service to learn more about the features", "space_log_trial_desc3": "An exclusive feature only for enterprise spaces, unlock after upgrading the space", "space_logo": "space_logo", "space_logs": "Space logs", "space_manage_choose_new_primary_admin": "Assign new admin", "space_manage_confirm_del_sub_admin_content": "After deleting the sub-admin, ${memberName} will no longer have the assigned permissions.", "space_manage_confirm_del_sub_admin_title": "Delete sub-admin", "space_manage_infomation_text": "Your role has been changed to \"Member\", so you can't manage the Space now", "space_manage_menu_feishu": "Lark integration", "space_manage_menu_social": "Integrations", "space_manage_menu_wecom": "Wecom integration", "space_manage_verify_primary_admin": "Verify the current admin", "space_members_limit": "Your Space has the maximum number of members (<a class=\"usage\"></a>/<a class=\"specification\"></a>). You can upgrade the plan to get more usage.", "space_members_limit_email_title": "", "space_mirror_limit": "The maximum number of mirrors to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_mirror_limit_email_title": "Usage reminders for the number of mirrors in the \"${SPACE_NAME}\" space", "space_name": "Space name", "space_name_length_err": "Space name must be between 2 and 100 characters in length", "space_not_access": "You are not a member of this Space, so you can't access it. Please contact the Space admin to join in.", "space_origin": "Original space", "space_overview": "Overview", "space_paid_notify": "You have successfully purchased \"<a class=\"planName\"></a>\". The amount paid is <a class=\"payFee\"></a>.", "space_rainbow_label_limit": "The space is using the advanced function \"Rainbow Label\". Please upgrade to continue using it.", "space_rainbow_label_limit_email_title": "", "space_record_limit": "The maximum number of total records to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_record_limit_email_title": "Usage reminders for the number of total records in the \"${SPACE_NAME}\" space", "space_search_empty": "Search space not found", "space_seat_info": "Number of members in the space: ${num} ", "space_seats_limit": "The maximum number of seats to be used in the \"<a class=\"spaceName\"></a>\" Space is <a class=\"specification\"></a> . So far, <a class=\"usage\"></a> has been used, and you can upgrade to get higher usage.", "space_seats_limit_email_title": "Usage reminders for the number of seats in the \"${SPACE_NAME}\" space", "space_setting": "Space settings", "space_setting_social_ad_btn": "Learn more", "space_setting_social_ad_decs": "Click here to learn more", "space_subscription_notify": "Congratulations! You have successfully subscribed \"<a class=\"planName\"></a>\". The expiration time is <a class=\"expireAt\"></a>.", "space_template": "Custom", "space_time_machine_limit": "Your Space supports <a class=\"specification\"></a> days of record history. You can upgrade the plan to see longer history.", "space_time_machine_limit_email_title": "", "space_trash_limit": "Your Space supports keeping deleted file nodes in trash within <a class=\"specification\"></a> days. You can upgrade the plan to see more.", "space_trash_limit_email_title": "", "space_trial": "Congrats! Your \"<a class=\"spaceName\"></a>\" Space has upgraded to the <a class=\"plan\"></a> (on trial). The plan will expire at <a class=\"deadline\"></a>.", "space_watermark_notify": "The Space is using the enterprise-level feature \"Global Watermarks\". Please upgrade to continue using it.", "space_watermark_notify_email_title": "The {SPACE_NAME} Space is using the enterprise-level feature \"Global Watermarks\"", "space_wecom_api_trial_end": "", "space_wecom_notify": "The space is using the enterprise-level space exclusive function \"Enterprise WeCom Integration\", please upgrade to continue using", "space_wecom_notify_email_title": "", "space_yozooffice_notify": "The space is using the enterprise-level space exclusive function \"Yongzhong Office Integration\", please upgrade to continue using", "space_yozooffice_notify_email_title": "", "spain": "Spain", "specifical_member": "specific member", "specifical_member_field": "specific member by field", "specified_fields": "Select fields...", "split_multiple_values": "Split multiple values", "sports_and_games": "Sports and games", "sri_lanka": "Sri Lanka", "sso_account": "SSO Account", "sso_login": "Login with SSO", "sso_password": "SSO password", "stacked_bar_chart": "Stacked Bar Chart", "stacked_by_field": "Stacked by", "stacked_column_chart": "Stacked Column Chart", "stacked_line_chart": "Stacked Line Chart", "standard": "Standard", "start_automation_workflow": "Start an automation workflow", "start_download_loading": "DownLoading\b... please wait a sec", "start_field_name": "Start time", "start_onfiguration": "Start configuration", "start_time": "Start time", "start_use": "Start using", "starting_from_midnight": "Starting from midnight (12:00 AM) every day, ", "startup": "Startup", "startup_company_support_program": "Startup support Program", "stat_average": "Average", "stat_checked": "Checked", "stat_count_all": "Total", "stat_date_range_of_days": "Date range (days)", "stat_date_range_of_months": "Date range (months)", "stat_empty": "Empty", "stat_fill": "Filled", "stat_max": "Max", "stat_max_date": "Latest date", "stat_min": "Min", "stat_min_date": "Earliest date", "stat_none": "None", "stat_percent_checked": "Percent Checked", "stat_percent_empty": "Percent Empty", "stat_percent_filled": "Percent Filled", "stat_percent_un_checked": "Percent <PERSON>checked", "stat_percent_unique": "Percent Unique", "stat_sum": "Sum", "stat_un_checked": "Unchecked", "stat_uniqe": "Unique", "statistical_link_data": "Roll up values from the linked records", "statistics": "Summary", "status_code_inviter_space_member_limit": "The capacity of the inviter's space has reached the upper limit and can no longer add new member temporally.", "status_code_link_invalid": "The invitation link has expired", "status_code_nvc_fail": "The current environment is at risk, please try again later!", "status_code_phone_validation": "The current operating environment is abnormal, please refresh the page and try again", "status_code_space_limit": "Your number of Space has reached the upper limit. You cannot join any new Space temporarily.", "status_code_space_not_exist": "The invited Space does not exist", "stay_tuned_for_more_features": "Stay tuned for more features", "steps_choose_reset_mode": "Select a reset method", "steps_validate_identities": "Verify identity", "stop_dingtalk_h5_modal_content": "The self-built application will be unbound from this space. Please confirm .", "storage_per_seats": "", "storage_per_space": "Storage usage", "strikethrough": "Strikethrough", "styling_upgrade_tips_description": "Upgrade to the Silver-Pro Space to enjoy more colors and icons", "styling_upgrade_tips_title": "More colors and icons can be found in PRO version~", "sub_admin": "Sub-admin", "sub_admin_add": "Add sub-admin", "sub_admin_edit": "Edit sub-admin", "sub_admin_view": "View sub-admin", "subject_capacity_full": "Full Space Storage Reminder", "subject_change_admin": "Space admin transfer", "subject_datasheet_remind": "{MEMBER_NAME} mentioned you in a datasheet", "subject_invite_notify": "{USER_NAME} invited you to join the Space \"{SPACE_NAME}\"", "subject_pay_success": "Your purchase is successful", "subject_record_comment": "You're mentioned in a comment", "subject_register_verify": "New Account Email Verification", "subject_remove_member": "You left the Space \"{SPACE_NAME}\"", "subject_space_apply": "{USER_NAME} is applying to join your Space \"{SPACE_NAME}\"", "subject_transfer_widget_notify": "The ownership of your widget \"{WIDGET_NAME}\" has been transferred", "subject_unpublish_widget_notify": "Your widget \"{WIDGET_NAME}\" has been unpublished", "subject_verify_code": "Email Verification", "submit": "OK", "submit_filter_success": "Filter conditions saved", "submit_questionnaire_success": "You have successfully submitted the questionnaire!", "submit_requirements": "Submit requirements", "subscribe": "Subscribe", "subscribe_credit_usage_over_limit": "The number of credits in the current space exceeds the limit, please upgrade your subscription.\n", "subscribe_demonstrate": "Request demos", "subscribe_disabled_seat": "The number of people cannot be lower than the original program", "subscribe_grade_business": "Business", "subscribe_grade_free": "Free", "subscribe_grade_plus": "Plus", "subscribe_grade_pro": "Pro", "subscribe_grade_starter": "Starter", "subscribe_label_tooltip": "Advanced space features", "subscribe_new_choose_member": "Supports up to ${member_num} members", "subscribe_new_choose_member_tips": "This plan supports 1~${member_num} members to enter the space", "subscribe_seats_usage_over_limit": "The number of seats in the current space exceeds the limit, please upgrade your subscription.\n（Seats include number of members and AI agents）", "subscribe_success_desc": "Congrats! Your subscription is activated and starts from today. Go to Overview to check your benefits.", "subscribe_success_title": "Payment successful", "subscribe_upgrade_choose_member": "${old_member_num} members are upgraded to ${new_member_num} members", "subscribe_upgrade_choose_member_tips": "Your original plan supports a maximum of ${old_member_num} members, but this upgrade will be expanded to ${new_member_num} members.", "subscribe_welcome_tip": "Notice: During the public beta, you can invite 100 members and create 1,000 datasheets at most.", "subscribed_record_archived": "<a class=\"memberName\"></a> archived <a class=\"recordTitle\"></a> in <a class=\"nodeName\"></a>", "subscribed_record_cell_updated": "<a class=\"memberName\"></a> modified the \"<a class=\"recordTitle\"></a>\" contents from the \"<a class=\"nodeName\"></a>\" datasheet：\nReplace \"<a class=\"oldDisplayValue\"></a>\" with \"<a class=\"newDisplayValue\"></a>\"", "subscribed_record_commented": "<a class=\"memberName\"></a> made comments from the \"<a class=\"recordTitle\"></a>\" in \"<a class=\"nodeName\"></a>\" datasheet：\"<a class=\"content\"></a>\"", "subscribed_record_unarchived": "<a class=\"memberName\"></a> unarchived <a class=\"recordTitle\"></a> in <a class=\"nodeName\"></a>", "subscription_expire_error": "Your subscription status has expired", "subscription_fee": "Subscription fee: ${fee}", "subscription_grades_checklist": "{\n  \"Usages\": [\n    {\n      \"group\": \"Usages\",\n      \"id\": \"member_num\",\n      \"title\": \"Seats\",\n      \"bronze\": \"Incl. 5 seats\",\n      \"silver\": \"Incl. 5 seats\",\n      \"gold\": \"Incl. 5 seats\",\n      \"enterprise\": \"Incl. 5 seats\",\n      \"community\":\"Unlimited\",\n      \"custom\":\"Custom\"\n    },\n    {\n      \"group\": \"Usages\",\n      \"id\": \"datasheet_number\",\n      \"title\": \"File nodes \",\n      \"bronze\": \"30\",\n      \"silver\": \"300\",\n      \"gold\": \"1,000\",\n      \"enterprise\": \"10,000\",\n      \"community\":\"Unlimited\",\n      \"custom\":\"Unlimited\"\n    },\n    {\n      \"group\": \"Usages\",\n      \"id\": \"datasheet_rows\",\n      \"title\": \"Records per datasheet\",\n      \"bronze\": \"5,000 \",\n      \"silver\": \"10,000\",\n      \"gold\": \"20,000\",\n      \"enterprise\": \"50,000\",\n      \"community\":\"50,000\",\n      \"custom\":\"50,000\"\n    },\n    {\n      \"group\": \"Usages\",\n      \"id\": \"space_rows\",\n      \"title\": \"Records per space\",\n      \"bronze\": \"20,000\",\n      \"silver\": \"3,000,000\",\n      \"gold\": \"20,000,000\",\n      \"enterprise\": \"500,000,000\",\n      \"community\":\"Unlimited\",\n      \"custom\":\"Unlimited\"\n    },\n    {\n      \"group\": \"Usages\",\n      \"id\": \"space_capacity\",\n      \"title\": \"Attachments storage per space\",\n      \"bronze\": \"1GB \",\n      \"silver\": \"Seats * 5GB\",\n      \"gold\": \"Seats * 7GB\",\n      \"enterprise\": \"Seats * 10GB\",\n      \"community\":\"Unlimited\",\n      \"custom\":\"Unlimited\"\n    }\n  ],\n  \"Features\": [\n    {\n      \"group\": \"Features\",\n      \"id\": \"star_marker\",\n      \"title\": \"Pin\",\n      \"bronze\": true,\n      \"silver\": true,\n      \"gold\": true,\n      \"enterprise\": true,\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"album_view\",\n      \"title\": \"Gallery view\",\n      \"bronze\": true,\n      \"silver\": true,\n      \"gold\": true,\n      \"enterprise\": true,\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"kanban_view\",\n      \"title\": \"Kanban view\",\n      \"bronze\": true,\n      \"silver\": true,\n      \"gold\": true,\n      \"enterprise\": true,\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"org_chart_view\",\n      \"title\": \"Architecture view\",\n      \"bronze\": true,\n      \"silver\": true,\n      \"gold\": true,\n      \"enterprise\": true,\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"gantt_view\",\n      \"title\": \"Gantt view\",\n      \"bronze\": \"10 per space\",\n      \"silver\": \"50 per space\",\n      \"gold\": \"200 per space\",\n      \"enterprise\": \"Unlimited\",\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"calendar_view\",\n      \"title\": \"Calendar view\",\n      \"bronze\": \"5 per space\",\n      \"silver\": \"50 per space\",\n      \"gold\": \"200 per space\",\n      \"enterprise\": \"Unlimited\",\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"collection_table\",\n      \"title\": \"Form\",\n      \"bronze\": \"20 per space\",\n      \"silver\": \"100 per space\",\n      \"gold\": \"300 per space\",\n      \"enterprise\": \"Unlimited\",\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"mirror\",\n      \"title\": \"Mirror（records permission）\",\n      \"bronze\": \"5 per space\",\n      \"silver\": \"50 per space\",\n      \"gold\": \"100 per space\",\n      \"enterprise\": \"Unlimited\",\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"widget\",\n      \"title\": \"Widget\",\n      \"bronze\": \"Free for a limited time\",\n      \"silver\": \"Free for a limited time\",\n      \"gold\": \"Free for a limited time\",\n      \"enterprise\": \"Free for a limited time\",\n      \"community\":\"Free for a limited time\",\n      \"custom\":\"Free for a limited time\"\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"dashboard\",\n      \"title\": \"Dashboard\",\n      \"bronze\": \"5 per space\",\n      \"silver\": \"50 per space\",\n      \"gold\": \"100 per space\",\n      \"enterprise\": \"Unlimited\",\n      \"community\":true,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"custom_embedding\",\n      \"title\": \"Custom embedding\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": true,\n      \"enterprise\": true,\n      \"community\":false,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"rainbow_tags\",\n      \"title\": \"Rainbow tags\",\n      \"bronze\": \"Basic color\",\n      \"silver\": \"Advanced color\",\n      \"gold\": \"Advanced color\",\n      \"enterprise\": \"Advanced color\",\n      \"community\":\"Basic color\",\n      \"custom\":\"Advanced color\"\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"space_capacity\",\n      \"title\": \"Record activity\",\n      \"bronze\": \"14 days\",\n      \"silver\": \"3 months\",\n      \"gold\": \"6 months\",\n      \"enterprise\": \"24 months\",\n      \"community\": true,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"recycle_bin\",\n      \"title\": \"Trash\",\n      \"bronze\": \"14 days\",\n      \"silver\": \"3 months\",\n      \"gold\": \"6 months\",\n      \"enterprise\": \"24 months\",\n      \"community\": true,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"api_limits\",\n      \"title\": \"API\",\n      \"bronze\": \"Free for a limited time\",\n      \"silver\": \"Free for a limited time\",\n      \"gold\": \"Free for a limited time\",\n      \"enterprise\": \"Free for a limited time\",\n      \"community\": true,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Features\",\n      \"id\": \"robot\",\n      \"title\": \"Automation\",\n      \"bronze\": \"Free for a limited time\",\n      \"silver\": \"Free for a limited time\",\n      \"gold\": \"Free for a limited time\",\n      \"enterprise\": \"Free for a limited time\",\n      \"community\": true,\n      \"custom\": true\n    }\n  ],\n  \"Management and security\": [\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"column_permission\",\n      \"title\": \"Field permissions\",\n      \"bronze\": \"10 per space\",\n      \"silver\": \"50 per space\",\n      \"gold\": \"200 per space\",\n      \"enterprise\": \"Unlimited\",\n      \"community\":false,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"folder_permission\",\n      \"title\": \"File node permissions\",\n      \"bronze\": \"10 per space\",\n      \"silver\": \"50 per space\",\n      \"gold\": \"200 per space\",\n      \"enterprise\": \"Unlimited\",\n      \"community\":false,\n      \"custom\":true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"admin_counts\",\n      \"title\": \"Space admin\",\n      \"bronze\": \"3\",\n      \"silver\": \"5\",\n      \"gold\": \"10\",\n      \"enterprise\": \"Unlimited\",\n      \"community\": false,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"watermark\",\n      \"title\": \"Watermark\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": false,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"security_disabled_export_data\",\n      \"title\": \"Disable data export\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": true,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"security_disabled_share\",\n      \"title\": \"Disable opening public links\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": false,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"security_disabled_copy\",\n      \"title\": \"Disable copying datas\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": false,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"security_disabled_download_file\",\n      \"title\": \"Disable downloadimg files\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": false,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    },\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"security_disabled_invite_members\",\n      \"title\": \"Disable inviting off-space user\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": false,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    }\n    ,\n    {\n      \"group\": \"Management and security\",\n      \"id\": \"space_log\",\n      \"title\": \"Space log\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": false,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    }\n  ],\n  \"Professional Services\": [\n    {\n      \"group\": \"Professional Services\",\n      \"id\": \"professional_services\",\n      \"title\": \"Consultant Services\",\n      \"bronze\": \"Community\",\n      \"silver\": \"Community\",\n      \"gold\": \"Community\",\n      \"enterprise\": \"Customer Success Manager\",\n      \"community\": \"Community\",\n      \"custom\": \"Customer Success Manager\"\n    },\n    {\n      \"group\": \"Professional Services\",\n      \"id\": \"enterprise_consulting_training\",\n      \"title\": \"Enterprise-level consulting training\",\n      \"bronze\": false,\n      \"silver\": false,\n      \"gold\": false,\n      \"enterprise\": true,\n      \"community\": false,\n      \"custom\": true\n    }\n  ]\n}", "subscription_grades_checklist_mobile_saas": "[\n    {\n        \"grade\": \"free\",\n        \"btnText\": \"Start Now\",\n        \"btnHref\": \"/login\",\n        \"primaryContent\": [\n            \"30 file nodes\",\n            \"5,000 records per datasheet\",\n            \"1G attachments storage per sapce\"\n        ],\n        \"details\": [\n            {\n                \"name\": \"Usage\",\n                \"rules\": [\n                    \"30 file nodes\",\n                    \"5,000 records per datasheet\",\n                    \"20,000 records per sapce\",\n                    \"1G attachments storage per sapce\"\n                ]\n            },\n            {\n                \"name\": \"Features\",\n                \"rules\": [\n                    \"Pin file nodes\",\n                    \"Gallery view,Kanban view and Architecture view are unlimited\",\n                    \"10 gantt views per sapce\",\n                    \"5 calendar views per sapce\",\n                    \"20 forms per sapce\",\n                    \"5 mirrors per sapce\",\n                    \"5 dashbords per sapce\",\n                    \"Widget is free for a limited time\",\n                    \"Basic colors for rainbow tags\",\n                    \"Record activity goes back 14 days\",\n                    \"Trash goes back 14 days\",\n                    \"API is free for a limited time\",\n                    \"Automation is free for a limited time\"\n                ]\n            },\n            {\n                \"name\": \"Management and security\",\n                \"rules\": [\n                    \"10 field permissions per sapce\",\n                    \"10 file node permissions per sapce\",\n                    \"3 admins per sapce\"\n                ]\n            },\n            {\n                \"name\": \"Professional Services\",\n                \"rules\": [\n                    \"Community\"\n                ]\n            }\n        ]\n    },\n    {\n        \"grade\": \"plus\",\n        \"btnText\": \"Choose Plus\",\n        \"btnHref\": \"/login\",\n        \"primaryContent\": [\n            \"300 file nodes\",\n            \"10,000 records per datasheet\",\n            \"5G/seats attachments storage per sapce\"\n        ],\n        \"details\": [\n            {\n                \"name\": \"Usage\",\n                \"rules\": [\n                    \"300 file nodes\",\n                    \"10,000 records per datasheet\",\n                    \"3,0000,000 records per sapce\",\n                    \"5G/seats attachments storage per sapce\"\n                ]\n            },\n            {\n                \"name\": \"Features\",\n                \"rules\": [\n                    \"Pin file nodes\",\n                    \"Gallery view,Kanban view and Architecture view are unlimited\",\n                    \"50 gantt views per sapce\",\n                    \"50 calendar views per sapce\",\n                    \"100 forms per sapce\",\n                    \"50 mirrors per sapce\",\n                    \"50 dashbords per sapce\",\n                    \"Widget is free for a limited time\",\n                    \"Advanced colors for rainbow tags\",\n                    \"Record activity goes back 3 months\",\n                    \"Trash goes back 3 months\",\n                    \"API is free for a limited time\",\n                    \"Automation is free for a limited time\"\n                ]\n            },\n            {\n                \"name\": \"Management and security\",\n                \"rules\": [\n                    \"50 field permissions per sapce\",\n                    \"50 file node permissions per sapce\",\n                    \"5 admins per sapce\"\n                ]\n            },\n            {\n                \"name\": \"Professional Services\",\n                \"rules\": [\n                    \"Community\"\n                ]\n            }\n        ]\n    },\n    {\n        \"grade\": \"pro\",\n        \"btnText\": \"Choose Pro\",\n        \"btnHref\": \"/login\",\n        \"primaryContent\": [\n            \"1,000 file nodes\",\n            \"20,000 records per datasheet\",\n            \"7G/seats attachments storage per sapce\",\n            \"Custom embedding\"\n        ],\n        \"details\": [\n            {\n                \"name\": \"Usage\",\n                \"rules\": [\n                    \"1,000 file nodes\",\n                    \"20,000 records per datasheet\",\n                    \"20,000,000 records per sapce\",\n                    \"7G/seats attachments storage per sapce\"\n                ]\n            },\n            {\n                \"name\": \"Features\",\n                \"rules\": [\n                    \"Pin file nodes\",\n                    \"Gallery view,Kanban view and Architecture view are unlimited\",\n                    \"200 gantt views per sapce\",\n                    \"300 calendar views per sapce\",\n                    \"300 forms per sapce\",\n                    \"100 mirrors per sapce\",\n                    \"100 dashbords per sapce\",\n                    \"Widget is free for a limited time\",\n                    \"Advanced colors for rainbow tags\",\n                    \"Custom embedding\",\n                    \"Record activity goes back 6 months\",\n                    \"Trash goes back 6 months\",\n                    \"API is free for a limited time\",\n                    \"Automation is free for a limited time\"\n                ]\n            },\n            {\n                \"name\": \"Management and security\",\n                \"rules\": [\n                    \"200 field permissions per sapce\",\n                    \"200 file node permissions per sapce\",\n                    \"10 admins per sapce\",\n                    \"Disable data export\"\n                ]\n            },\n            {\n                \"name\": \"Professional Services\",\n                \"rules\": [\n                    \"Community\"\n                ]\n            }\n        ]\n    },\n    {\n        \"grade\": \"enterprise\",\n        \"btnText\": \"Contacts us\",\n        \"btnHref\": \"\",\n        \"primaryContent\": [\n            \"10,000 file nodes\",\n            \"50,000 records per datasheet\",\n            \"10G/seats attachments storage per sapce\",\n            \"Unlimited permission and security settings\",\n            \"Customer Success Manager\"\n        ],\n        \"details\": [\n            {\n                \"name\": \"Usage\",\n                \"rules\": [\n                    \"10,000 file nodes\",\n                    \"50,000 records per datasheet\",\n                    \"50,0000,000 records per sapce\",\n                    \"10G/seats attachments storage per sapce\"\n                ]\n            },\n            {\n                \"name\": \"Features\",\n                \"rules\": [\n                    \"Pin file nodes\",\n                    \"Gallery view,Kanban view and Architecture view are unlimited\",\n                    \"Gantt views are unlimited\",\n                    \"Calendar views are unlimited\",\n                    \"Forms are unlimited\",\n                    \"Mirrors are unlimited\",\n                    \"Dashbords are unlimited\",\n                    \"Widget is free for a limited time\",\n                    \"Advanced colors for rainbow tags\",\n                    \"Custom embedding\",\n                    \"Record activity goes back 24 months\",\n                    \"Trash goes back 24 months\",\n                    \"API is free for a limited time\",\n                    \"Automation is free for a limited time\"\n                ]\n            },\n            {\n                \"name\": \"Management and security\",\n                \"rules\": [\n                    \"Field permissions are unlimited\",\n                    \"File node permissions are unlimited\",\n                    \"Admins are unlimited\",\n                    \"Watermark\",\n                    \"Disable data export\",\n                    \"Disable opening public links\",\n                    \"Disable copying datas\",\n                    \"Disable downloadimg files\",\n                    \"Disable inviting off-space user\",\n                    \"Space log\"\n                ]\n            },\n            {\n                \"name\": \"Professional Services\",\n                \"rules\": [\n                    \"Customer Success Manager\",\n                    \"Custom function development (additional cost)\",\n                    \"Enterprise-level consulting training\"\n                ]\n            }\n        ]\n    }\n  ]", "subscription_grades_checklist_mobile_selfhost": "[\n  {\n    \"grade\": \"community\",\n    \"btnText\": \"Dowload\",\n    \"primaryContent\": [\n      \"10,000 file nodes\",\n      \"50,000 records per datasheet\",\n      \"Unlimitied attachments storage per sapce\"\n    ],\n    \"details\": [\n      {\n        \"name\": \"Usage\",\n        \"rules\": [\n          \"10,000 file nodes\",\n          \"50,000 records per datasheet\",\n          \"50,0000,000 records per sapce\",\n          \"Unlimitied attachments storage per sapce\"\n        ]\n      },\n      {\n        \"name\": \"Features\",\n        \"rules\": [\n          \"Pin file nodes\",\n          \"Gallery view,Kanban view and Architecture view are unlimited\",\n          \"Gantt views are unlimited\",\n          \"Calendar views are unlimited\",\n          \"Forms are unlimited\",\n          \"Mirrors are unlimited\",\n          \"Dashbords are unlimited\",\n          \"Widget is free for a limited time\",\n          \"Basic colors for rainbow tags\",\n          \"Record activity goes back 24 months\",\n          \"Trash goes back 24 months\",\n          \"API is free for a limited time\",\n          \"Automation is free for a limited time\"\n        ]\n      },\n      {\n        \"name\": \"Management and security\",\n        \"rules\": [\n          \"Not supported\"\n        ]\n      },\n      {\n        \"name\": \"Professional Services\",\n        \"rules\": [\n          \"Community\"\n        ]\n      }\n    ]\n  },\n  {\n    \"grade\": \"custom\",\n    \"btnText\": \"Contacts us\",\n    \"primaryContent\": [\n      \"10,000 file nodes\",\n      \"50,000 records per datasheet\",\n      \"10G/seats attachments storage per sapce\",\n      \"Unlimited permission and security settings\",\n      \"Customer Success Manager\"\n    ],\n    \"details\": [\n      {\n        \"name\": \"Usage\",\n        \"rules\": [\n          \"10,000 file nodes\",\n          \"50,000 records per datasheet\",\n          \"50,0000,000 records per sapce\",\n          \"Unlimited attachments storage per sapce\"\n        ]\n      },\n      {\n        \"name\": \"Features\",\n        \"rules\": [\n          \"Pin file nodes\",\n          \"Gallery view,Kanban view and Architecture view are unlimited\",\n          \"Gantt views are unlimited\",\n          \"Calendar views are unlimited\",\n          \"Forms are unlimited\",\n          \"Mirrors are unlimited\",\n          \"Dashbords are unlimited\",\n          \"Widget is free for a limited time\",\n          \"Advanced colors for rainbow tags\",\n          \"Custom embedding\",\n          \"Record activity goes back 24 months\",\n          \"Trash goes back 24 months\",\n          \"API is free for a limited time\",\n          \"Automation is free for a limited time\"\n        ]\n      },\n      {\n        \"name\": \"Management and security\",\n        \"rules\": [\n          \"Field permissions are unlimited\",\n          \"File node permissions are unlimited\",\n          \"Admins are unlimited\",\n          \"Watermark\",\n          \"Disable data export\",\n          \"Disable opening public links\",\n          \"Disable copying datas\",\n          \"Disable downloadimg files\",\n          \"Disable inviting off-space user\",\n          \"Space log\"\n        ]\n      },\n      {\n        \"name\": \"Professional Services\",\n        \"rules\": [\n          \"Customer Success Manager\",\n          \"Custom function development (additional cost)\",\n          \"Enterprise-level consulting training\"\n        ]\n      }\n    ]\n  }\n]", "subscription_information": "Subscription plan", "subscription_level": "Plan: ${level}", "subscription_product_seats": "<PERSON><PERSON>", "subscription_type": "Type: ${type}", "success": "Success", "success_invite_number": "", "success_invite_person_number": "", "sudan": "Sudan", "summarize": "Summary", "summary_return_field_value_of_row": "Returns the value to the cells of the ${name} columns.", "summary_widget_add_describle": "Add summary description", "summary_widget_add_target": "Add target value", "summary_widget_select_field": "Select a way for summarizing", "summary_widget_select_view": "Select a view as the data source", "summary_widget_setting": "Widget settings", "summary_widget_setting_help_tips": "Setting help", "summary_widget_setting_help_url": "http://help.dolphintable.suanlihaiyang.com/docs/intro-widget-summary", "superior_team": "Superior team", "support": "Help", "support_access_to_editors": "You are allowed to access the current space to edit.", "support_attachment_formats": "Support JPG, PNG, and GIF images under 1 GB", "support_features": "Support", "support_image_formats": "Support images", "support_image_formats_limits": "Support JPG, PNG, and GIF images under ${number} MB", "suriname": "Suriname", "swagger_constants_desc": "Combination of grid and datasheet and built-in API, helping you to establish application in a blink based on this powerful database-spreadsheet hybrid", "swaziland": "Swaziland", "sweden": "Sweden", "switch_avatar": "Change avatar", "switch_to_catalog": "Focus on Catalog", "switch_view_next": "Move to the next view", "switch_view_prev": "Move to the previous view", "switzerland": "Switzerland", "sync_failed": "Synchronization failed", "sync_success": "Synchronization succeeded", "syncing": "Synchronizing", "syria": "Syria", "system_configuration_company_copyright": "copyright @ 2019-2021 深圳表格科技有限公司.All rights reserved.", "system_configuration_company_name_short": "APITable", "system_configuration_company_official_account": "Official account", "system_configuration_product_name": "APITable", "system_message": "System message", "system_theme": "Use system setting", "tab_add_view_datasheet": "Create a datasheet instead", "tab_org": "Organization", "tab_role": "Role", "table": "Table", "table_link_err": "Please add a Link field first", "tag": "label", "taiwan": "Taiwan, China", "tajikistan": "Tajikistan", "take_photos_or_upload": "Take photo or upload from Gallery", "tanzania": "Tanzania", "task_completed": "Task completed", "task_list": "To-do list", "task_progress": "Task progress", "task_reminder": "[Reminder] Your task \"<a class=\"recordTitle\"></a>\" in the datasheet \"<a class=\"nodeName\"></a>\" will be due in <a class=\"taskExpireAt\"></a>", "task_reminder_app_enable_settings": "Reminder settings", "task_reminder_app_enable_switch": "Enable task reminder", "task_reminder_enable_member": "<PERSON>ter", "task_reminder_entry": "Task reminder", "task_reminder_hover_cell_tooltip": "Enable task reminder with one click", "task_reminder_notify_column_member": "field member", "task_reminder_notify_date": "Notify date", "task_reminder_notify_date_option_15_minutes_before": "15 minutes before", "task_reminder_notify_date_option_1_hour_before": "1 hour before", "task_reminder_notify_date_option_2_hours_before": "2 hours before", "task_reminder_notify_date_option_30_minutes_before": "30 minutes before", "task_reminder_notify_date_option_5_minutes_before": "5 minutes before", "task_reminder_notify_date_option_exact": "Reach the exact date", "task_reminder_notify_date_option_one_day_before": "One day before", "task_reminder_notify_date_option_one_month_before": "One month before", "task_reminder_notify_date_option_one_week_before": "One week before", "task_reminder_notify_date_option_six_months_before": "Six months before", "task_reminder_notify_date_option_three_month_before": "Three month before", "task_reminder_notify_date_option_two_day_before": "Two days before", "task_reminder_notify_date_option_two_months_before": "Two months before", "task_reminder_notify_date_option_two_weeks_before": "Two weeks before", "task_reminder_notify_member": "member ${member}", "task_reminder_notify_time": "Notify time", "task_reminder_notify_time_warning": "Invalid option because the time is not showed in the cell. It will notify when reaching the date.", "task_reminder_notify_tooltip": "Will notify ${remind_who} on ${remind_date} ${remind_time}", "task_reminder_notify_who": "Notify who", "task_reminder_notify_who_error_empty": "Won't notify anyone because no member is specified", "task_reminder_notify_who_error_not_exist": "The Member field was deleted", "task_reminder_tips": "The task reminder time is base on the UTC time zone, and the time display here is base on the account time zone.", "task_timeout": "The task is timeout.", "team": "Team", "team_is_exist_err": "team_is_exist_err", "team_length_err": "team_length_err", "teamwork": "Start team work", "teamwork_click_here": "Want to create a public link? Click here", "teamwork_desc": "Invite collaborators", "teamwork_number_tip": "${number} collaborator(s)", "template": "Template", "template_advise_tip": "Don't have the template you want? Feel free to send us suggestions !", "template_album_share_success": "The sharing link has been copied successfully", "template_center_use_to_create_datasheets": "Use template to create datasheet", "template_centre": "Templates", "template_centre_create_Dolphin_used_by_template": "Use this template to create datasheet(s)", "template_centre_using_template_data": "Use data in the template", "template_centre_using_template_permission_tip": "This location cannot be selected without permmission for the root catalog", "template_centre_using_template_tip": "Choose where to put the datasheet(s)", "template_created_successfully": "Template created", "template_creation_failed": "Failed to create template", "template_detail_tip": "Use the template first, then you can modify or write data", "template_experience": "", "template_feedback": "<PERSON><PERSON><PERSON>", "template_go_back": "Templates - ${category}", "template_has_been_deleted": "Current template does not exist or has been deleted", "template_has_been_deleted_title": "Note", "template_management": "Templates", "template_name": "Template Name", "template_name_limit": "Template name cannot exceed 100 characters", "template_name_repetition_content": "Replacing it will overwrite the current Space template.", "template_name_repetition_title": "\"${templateName}\" already exists. Do you want to replace it?", "template_no_template": "No templates", "template_not_found": "Can't find templates you want? Tell us", "template_recommend_title": "🌟 Hot", "template_type": "Template", "terms_of_service": "<Terms of Service>", "terms_of_service_pure_string": "Terms of service", "terms_of_service_title": "Service Agreement", "test": "test", "test_function": "Experimental features", "test_function_btncard_btntext_apply": "Apply", "test_function_btncard_btntext_open": "Enable", "test_function_btnmodal_btntext": "Apply", "test_function_card_info_async_compute": "Async computing gives you a smoother editing experience in a data-heavy datasheet", "test_function_card_info_render_normal": "Regular rendering gives you the best UI experience with a mildly degraded performance", "test_function_card_info_render_prompt": "In the optimized rendering mode, scrolling to view a datasheet with a lot of data can be smoothier", "test_function_card_info_robot": "Automation help you automate repetitive actions in the datasheets and free up your team's productivity", "test_function_card_info_view_manual_save": "View configurations modified by members only take effect temporarily for themselves", "test_function_card_info_widget": "Use widget SDK to develop custom widgets to meet more business needs", "test_function_desc": "Experimental features may be adjusted due to changes in product strategy or billing strategy, please read the scope of impact of the features carefully before enabling them", "test_function_exit_experiencing": "Exit", "test_function_experiencing": "Experience", "test_function_form_submit_tip": "After applying, you will receive a notification soon that the feature is enabled for your Space", "test_function_modal_info_async_compute": "After the async computing mode is enabled, adding, editing, and deleting operations in a datasheet with a large amount of data can be smoother (the latency will be lower)", "test_function_modal_info_render_normal": "The \"rendering optimization\" mode is turned on by default in the datasheet, which improves performance and causes a small amount of interaction to be lost. Enabling the regular mode, you can restore to the most complete interactive function experience.", "test_function_modal_info_render_prompt": "After the optimized rendering mode is enabled, scrolling to view a datasheet with a large amount of data can be smoothier (the latency will be lower)", "test_function_modal_info_robot": "<p>Dolphin Automation are the guardians for your work efficiency on the Dolphin Planet. Human beings are meant to do more creative things! <a href=\"http://help.dolphintable.suanlihaiyang.com/docs/manual-automation-robot\" target =\"_blank\"> View documentation</a></p>\n\n<p>Example Automations:</p>\n\n<ul><li> Automation Alpha: When a record is created,  Alpha will automatically send a message to the Slack channel</li>\n\n<li>Automation Beta: When a specific form is submitted, Beta will automatically send a message to the Lark chat group</li>\n\n<li>Automation Delta: when a record meets a specific condition, Delta will automatically update another record in another datasheet </li> </ul>", "test_function_modal_info_view_manual_save": "<p>Conflicts often arise when members modify filtering, grouping, or sorting in the same view, as each wants to configure it to suit their needs. After enabling this feature, temporary view configuration is supported in \"Datasheet\" and \"Mirror\" to reduce conflicts. </p>\n\n<p>After this feature is enabled, it affects:</p>\n<ul>\n<li>After members in \"View\" modify the view configuration, it will only take effect temporarily for themselves. After refreshing, the default configuration will be restored. Members can choose to manually save the view configuration and sync it to other members</li>\n<li>Temporary view configurations are also supported on Mirror. Special case: The filter conditions of the source datasheet view cannot be modified in the mirror</li>\n<li>Users with \"read-only\" permission are also allowed to temporarily modify the view configuration.</li>\n<li>View configuration includes: filter, group, sort, hide fields, style, layout, field order, statistics, etc.</li>\n</ul>", "test_function_modal_info_widget": "<p>As Dolphindata is essentially a visualized database, the widget is the best helper to quickly turn the database into different applications. Now, Dolphindata supports custom widgets, allowing developers to make full use of widget SDK to develop widgets that meet business scenarios and personal needs. <a href=\"https://Dolphin.cn/developers/widget/introduction\" target=\"_blank\">See documentation</a></p>\n\n<p>Examples of custom widgets:</p>\n\n<ul><li>Cell viewer: help you easily browse data from different column types, such as One-way Link, Two-way Link, Lookup, and Attachments</li>\n\n<li>Dolphin map: help you quickly render the addresses from a datasheet in a Google map </li>\n\n<li>URL preview: help you preview the webpage content from a URL</li></ul>", "test_function_normal_modal_close_content": "Disabled. Please refresh the page.", "test_function_normal_modal_open_content": "Enabled. Please refresh the page.", "test_function_note_async_compute": "*It may cause data to be out of sync. Suggest enabling it only for non-sensitive data.", "test_function_note_render_normal": "*The performance of the table will be reduced after it is enabled, but you can experience the most complete functional interaction", "test_function_note_render_prompt": "*When enabled, some interactive animation and UI may get lost. We are on it for optimization.", "test_function_note_robot": "*After your application is approved, your submitted Space will automatically enable this feature", "test_function_note_view_manual_save": "*After enabled, all views and mirrors in the Space will default to the out-of-sync mode", "test_function_note_widget": "*After your application is approved, the current space station will automatically enable this function", "test_function_space_level_desc": "After you enable an experimental feature at the Space level, it will take effect on all members in the Space", "test_function_space_level_title": "Space level", "test_function_user_level_desc": "After you enable an experimental feature at the member level, it will only take effect on yourself", "test_function_user_level_title": "Member level", "test_huanghao": "", "text": "Text", "text_button": "Text But<PERSON>", "text_editor_tip_end": "\"Enter\" to end editing", "text_functions": "String Function", "thailand": "Thailand", "the_button_field_is_misconfigured": "The button field is misconfigured, please check and try again", "the_current_automation_workflow_has_no_related_files_you_can_establish_a_link_by_adding_trigger_conditions_and_actions_on_the_left_side": "The current automation workflow has no related file nodes. You can establish a link by adding trigger conditions and actions on the left side", "the_current_button_column_has_expired_please_reselect": "The current button column has expired, please reselect", "the_last_7_days": "the past 7 days", "the_last_month": "last 30 days", "the_last_week": "last 7 days", "the_next_month": "next 30 days", "the_next_week": "next 7 days", "theme_blue": "Blue", "theme_brown": "<PERSON>", "theme_color": "Select color", "theme_color_1": "Theme Color 1", "theme_color_2": "Theme Color 2", "theme_color_3": "Theme Color 3", "theme_color_4": "Theme Color 4", "theme_deepPurple": "Dark purple", "theme_green": "Green", "theme_indigo": "Indigo", "theme_orange": "Orange", "theme_pink": "Pink", "theme_purple": "Purple", "theme_red": "Red", "theme_setting": "Theme", "theme_tangerine": "Orange", "theme_teal": "Blue-green", "theme_yellow": "Yellow", "then": "Then", "there_are_attachments_being_uploaded": "Some attachment still under processing", "there_are_unsaved_content_in_the_current_step": "There are unsaved content in the current step", "these_columns_you_chose_would_be_deleted": "Your selected ${count} fields will be deleted", "third_party_edit_space_name_err": "This Space has been bound to a third-party integration. Please contact the admin to edit your name.", "third_party_integration_info": "You are using a third-party integration. To delete the Space, please disable the third-party integration first.  ", "third_party_logins": "Third-party login", "third_prize": "", "third_prize_name": "", "third_prize_number": "", "this_feature_is_not_yet_available": "This feature is not available yet", "this_field_no_reference_data_yet": "No data", "this_month": "this month", "this_week": "this week", "this_year": "this year", "tile": "Card", "time": "Date", "time_format": "Time format", "time_format_month_and_day": "MMM D HH:mm", "time_format_today": "[Today] HH:mm", "time_format_year_month_and_day": "MMM D, YYYY HH:mm", "time_format_year_month_and_day_for_dayjs": "MMM D, YYYY", "time_format_yesterday": "[Yesterday] HH:mm", "time_machine": "Time Machine", "time_machine_action_title": "Operation History", "time_machine_unlimited": "Unlimited Time Machine Histories", "time_zone_inconsistent_tips": "When time zones are inconsistent, the time zone of the start time will be used by default", "timemachine_add": "added ${name}", "timemachine_add_field": "added ${name} column(s)", "timemachine_add_record": "added ${count} rows of record(s)", "timemachine_add_widget": "added a new widget", "timemachine_delete_comment": "deleted comment(s)", "timemachine_delete_field": "deleted ${record_count} column(s)", "timemachine_delete_record": "deleted ${count} rows of record(s)", "timemachine_delete_views": "deleted ${type}", "timemachine_delete_widget": "deleted widget", "timemachine_delete_widget_panel": "deleted widget panel", "timemachine_freeze_column_count": "frozen to column ${count}", "timemachine_help_url": "https://help.aitable.ai/docs/manual-timemachine", "timemachine_manual_save_view": "synced view configuration", "timemachine_modify_alarm": "modified to", "timemachine_modify_view": "modified the view configuration", "timemachine_modify_widget_panel": "modified widget panel name", "timemachine_move_row": "moved ${record_count} row(s)", "timemachine_move_view": "moved view", "timemachine_move_widget": "modified widget panel sorting", "timemachine_paste_set_field": "pasted column(s)", "timemachine_paste_set_record": "pasted ${record_count} data item(s)", "timemachine_set_alarm": "${status} reminder on ${date_time}", "timemachine_set_auto_head_height": "set auto line break", "timemachine_set_calender_style": "modified the calendar view configuration", "timemachine_set_columns_property": "set column width", "timemachine_set_gallery_style": "modified album view layout settings", "timemachine_set_org_chart_style": "modified the architecture view configuration", "timemachine_set_record": "edited ${record_count} data item(s)", "timemachine_set_row_height": "set row height", "timemachine_set_view_auto_save": "auto-saved the view configuration", "timemachine_set_view_lock_info": "set view lock", "timemachine_undo_add_field": "undid adding column(s)", "timemachine_undo_add_view": "undid adding view", "timemachine_undo_auto_head_height": "undid auto line break", "timemachine_undo_delete_view": "undid deleting view", "timemachine_undo_freeze_column_count": "undid: frozen to column ${count}", "timemachine_undo_modify_view": "undid modify the view configuration", "timemachine_undo_move_column": "undid moving column(s)", "timemachine_undo_move_view": "undid moving view", "timemachine_undo_paste_set_record": "undid: pasting ${record_count} record(s)", "timemachine_undo_set_alarm": "undid: ${status} reminder on ${date_time}", "timemachine_undo_set_column_property": "undid modify the field configuration", "timemachine_undo_set_group": "undid group setting", "timemachine_undo_set_row_height": "undid setting row height", "timemachine_undo_set_sort_info": "undid the sorting configuration", "timemachine_undo_set_view_filter": "undid the filtering configuration", "timemachine_undo_view_lock_info": "undid setting view lock", "timemachine_update_comment": "updated comment(s)", "times_per_month_unit": "call(s)/month", "times_unit": " call(s)", "timing_rules": "Timing", "timor_leste": "Timor-Leste", "tip_del_success": "You can restore your Space within 7 days", "tip_do_you_want_to_know_about_field_permission": "Want to encrypt field data? Learn about field permissions", "tip_primary_field_frozen": "As a unique identifier for each record, ${tag}the primary field can't be hidden, moved, or deleted", "tip_setting_nickname": "If you'd like, I'd love to know what nickname you would give me?", "tip_setting_nickname_distribute": "\"${nickname}\" is a random nickname~", "tip_shift_scroll": "Shift + Mousewheel to scroll horizontally", "tiral_3days_1dollar": "1 RMB to experience the features of the Silver-Pro version for 3 days.", "title_select_sorting_fields": "Select sort field", "to_be_paid": "To be paid", "to_filter_link_data": "Filtered records will not be looked up", "to_new_main_admin_tip_after_change": "Admin have full access to the Space, such as assigning sub-admins and transferring ownership of the Space", "to_old_main_admin_tip_after_change": "Remove all admin privileges and downgrade him to a member", "to_select_tip": "The value of this field will be converted into the following options.", "to_view_dashboard": "Go check it out", "toast_add_field_success": "Field inserted", "toast_cell_fill_success": "Cell(s) filled successfully ", "toast_change_option_success": "Option name edited", "toast_copy_cell_by_count": "${count} cell(s) copied", "toast_copy_record_by_count": "${count} records copied", "toast_ctrl_s": "Changes will be saved to the cloud in real time and no need to save manually. ", "toast_cut_cell_by_count": "${count} cells cut", "toast_cut_record_by_count": "${count} records cut", "toast_delete_option_success": "Option deleted", "toast_duplicate_field_success": "Field duplicated", "toast_field_configuration_success": "Field configuration edited  ", "toast_insert_field_success": "Field inserted", "today": "today", "toggle_catalog_panel": "Expand Workbench", "toggle_comment_pane": "${status} activity", "toggle_widget_dev_mode": "Toggle dev mode", "toggle_widget_panel": "Expand widget", "togo": "Togo", "token_value": "API Token", "tomorrow": "tomorrow", "tonga": "Tonga", "tool_bar_hidden": "<PERSON>de", "tooltip_cannot_create_widget_from_dashboard": "You can only create a widget from a datasheet rather than a dashboard", "tooltip_edit_form_formula_field": "The field value can't be edited because it's a formula result", "tooltip_edit_form_lookup_field": "The field value can't be edited because it's a lookup result", "tooltip_edit_form_workdoc_field": "It's unavailable to edit a WorkDoc field on the mobile", "tooltip_primary_field_type_select": "The primary field is meant to be unique, so it only supports limited field types", "tooltip_workspace_up_to_bound_no_new": "You've created the maximum number of Spaces", "total": "Total", "total_capacity": "Total capacity", "total_error_records_count": "A total of ${errorCount} records", "total_import_employee_by_count": "${rowCount} employee information imported this time", "total_records": "Total Records", "total_saving": "Save", "total_storage": "Maximum: ${memory}", "training_add_data_source_btn_text": "Add DataSource", "training_data_source_table_column_1": "Source", "training_data_source_table_column_2": "Last Training", "training_data_source_table_column_3": "Status", "training_data_source_table_column_4": "Operation", "training_data_source_title": "通过 \"dataDource\"，您可以上传、导入和添加数据到数据集中。您可以将各种类型的数据文件包括图像、文本、等添加到数据集中，以满足您的训练需求。", "transfer_to_public": "Transfer ", "trash": "Trash", "trash_over_limit_tip": "\"Silver\" space can view ${day} days of historical file nodes, limited-time open experience", "trash_tip": "You can restore file nodes deleted in the past ${day} days from the Trash. You can only view file nodes with \"manager\" permissions", "travel_and_outdoors": "Travel and outdoors", "tree_level": "Lv ${level}", "trial_expires": "Your free trial has expired", "trial_subscription": "Trial", "trigger_binding_pre_configured": "\"Button is Clicked\" needs to be linked to both a table and a field to know where the click action occurs. This has been pre-configured for you", "trinidad_and_tobago": "Trinidad and Tobago", "try_my_best_effort_to_reconnect": "Reconnecting...", "tunisia": "Tunisia", "turkey": "Turkey", "turkmenistan": "Turkmenistan", "turks_and_caicos_islands": "Turks and Caicos Islands", "twelve_hour_clock": "12 hour", "twenty_four_hour_clock": "24 hour", "type": "Type", "uganda": "Uganda", "ukraine": "Ukraine", "un_bind_email": "Unbind email", "un_bind_mobile": "Unbind phone number", "un_bind_success": "Unbound", "un_lock": "Unlock", "un_lock_view": "View locked", "unaccess_notified_message": "will not be notified, because the user does not have permission to access this datasheet", "unactive_space": "Unactive Space", "unarchive_notice": "The current record is being unarchived. Once unarchived, it will appear again in tables and search results and will start to be recalculated. Continue?", "unarchive_record_in_activity": " unarchived this record", "unauthorized_operation": "You do not have sufficient permissions to perform this operation", "unavailable_og_title_content": "Temporarily unavailable", "unbind": "Unbind", "unbind_third_party_accounts_desc": "After unbinding, you can still use the application but can't use the ${mode} account to log in", "unbind_wechat_desc": "*", "unbound": "Unbound", "under_line": "Underline", "under_use_restrictions": "Experiencing", "understand_and_accept": "I understand and accept", "undo": "Undo", "uneditable_check_info": "This field can't be edited", "unit_ge": " ", "unit_piece": " ", "united_arab_emirates": "United Arab Emirates", "united_kingdom": "United Kingdom", "united_states": "United States", "unlimited": "Unlimited", "unlimited_search_tips": "Upgrade to silver plan, you will get the feature of global searching", "unlink": "Unlink", "unlock_forever": "Upgrade to the Silver-Pro Space to unlock this feature", "unnamed": "Anonymous", "unordered_list": "Bulleted list", "unpaid_order_status": "No payment received yet, again ${action}", "unprocessed": "Unread", "unresolved_message": "This message is unable to parse", "unshow_record_history": "The modification history of the records in this table has been hidden", "up": "Move to the cell above", "update_description_fail": "Network error, save failed.", "update_invitation_link_content": "After the update, the invitation link will be invalid", "update_invitation_link_title": "Update the invitation link", "update_node_share_link_content": "After the update, the generated share link will be invalid", "update_node_share_link_title": "Update share link", "update_rate_error_notify": "The value you entered exceeds the maximum value of the rating", "update_space_fail": "Failed to edit Space information", "update_space_success": "Space information edited", "upgrade": "Upgrade", "upgrade_expire_time_warning": "The expiration time is consistent with the original plan.", "upgrade_guide": "How to upgrade", "upgrade_now": "Upgrade now", "upgrade_pure": "Upgrade", "upgrade_should_in_dingtalk_msg": "DingTalk company administrator please search \"表格\" in DingTalk App Store, and click the \"立即购买\" button. When your space successfully upgraded, you will get more usage.", "upgrade_space": "Upgrade", "upgrade_success": "Upgrade Success", "upgrade_success_1_desc": "Congratulations on your ${orderType}. Check out ${position} for the latest benefits", "upgrade_success_2_desc": "We have assigned you an exclusive consultant, contact me for more convenient after-sales service", "upgrade_success_button": "I've finished adding", "upgrade_success_model": "${orderType} success", "upgrade_success_tip": "Congrats, the space has been upgraded successfully, and you can check your benefits in the Overview.", "upgrade_to_silver_get_unlimited_search": "Upgrade to silver plan, you will get the feature of global searching", "upload_again": "re-upload", "upload_avatar": "Upload avatar", "upload_canceled": "Upload cancelled", "upload_fail": "Upload failed", "upload_later": "Upload later", "upload_on_your_phone": "Upload from mobile", "upload_success": "Uploaded", "url": "URL", "url_batch_recog_failure_message": "Fail to display titles of some URLs", "url_cell_edit": "Customize the URL cell", "url_jump_link": "Go to the URL", "url_preview_limit_message": "You can display title of no more than 300 URLs at a time.", "url_preview_setting": "Auto display title when added URL", "url_recog_failure_message": "Fail to display the title of URL", "uruguay": "Uruguay", "usage_overlimit_alert_title": "Over limits", "use_the_template": "Use template", "used": "used:", "used_space_capacity": "Used space capacity", "used_storage": "Used: ${memory}", "user_avatar": "Avatar", "user_center": "My Settings", "user_feedback": "Give feedback", "user_log_out": "Delete Account", "user_mentioned_in_record": "{{memberName}} mentioned in ${record}", "user_menu_tooltip_member_name": "My nickname in the Space\"${spaceName}\"", "user_profile": "Profile settings", "user_profile_setting": "Profile", "user_removed_by_space_toadmin": "<a class=\"involveMemberArr\"></a> was removed by <a class=\"memberName\"></a> from the \"<a class=\"spaceName\"></a>\" Space.", "user_removed_by_space_touser": "You were removed from the \"<a class=\"spaceName\"></a>\" Space by <a class=\"memberName\"></a>.", "user_setting": "My Settings", "user_setting_time_zone_title": "Time zone", "user_space_member_limited_tips": "Hey, your ${space} Space has reached the upper limit of ${spec} datasheets", "user_space_member_unlimited_tips": "Upgrade to Silver-Pro Space to enjoy the unlimited creation", "user_token": "User token", "using_btn": "", "using_template_title": "Create a datasheet based on this template", "using_templates_successful": "Template used", "uzbekistan": "Uzbekistan", "v_500": "500 V coins", "v_coins": "V coins", "v_coins_1000": "1000", "vanuatu": "Vanuatu", "vb_1000": "", "vb_2000": "2,000 V coins", "venezuela": "Venezuela", "venture_capital": "Venture Capital", "verification_code": "Verification code", "verification_code_error_message": "Please click button on the right to get verification code ", "verification_code_login": "Log in with verification code", "verify_account_title": "Verify account", "verify_via_email": "Identity verification by email", "verify_via_phone": "Identity verification by SMS", "video": "Video", "video_not_support_play": "The current video format does not support playing online", "vietnam": "Vietnam", "view": "View", "view_by_person": "View all", "view_changed": "The view has changed", "view_collaborative_members": "View the ${number} collaborating member", "view_configuration_changes_have_been_reversed": "View configuration changes have been reversed", "view_configuration_tooltips": "View configuration not saved only for yourself", "view_count": "${count} view(s)", "view_count_over_limit": "This datasheet has the maximum number of views", "view_detail": "View details", "view_export_to_excel": "Export view", "view_field": "Field", "view_field_permission": "View field permissions (Beta)", "view_field_search_not_found_tip": "Column named  \"${value}\" unfound", "view_find": "Find a view", "view_foreign_form": "Form of the current view", "view_foreign_form_count": "${count} form(s)", "view_foreign_form_empty": "Want to populate data to your datasheet in a uniform way? Form is the best choice", "view_form": "Form", "view_form_field_changed_tip": "The form has a new field or type change", "view_full_catalog": "View the full catalog", "view_has_locked_not_deletes": "The view is locked and can't be deleted", "view_list": "View list", "view_lock": "Lock view", "view_lock_command_error": "The current view is locked and you can't edit it", "view_lock_desc_placeholder": "Remind others why you lock the view", "view_lock_note": "This view is locked, please unlock it before you edit it and save the view settings", "view_lock_setting_desc": "This view is locked, please unlock it before you edit it and save the view settings", "view_manual_save": "Unsynchronized view", "view_mirror_count": "${count} mirror(s)", "view_name_length_err": "The name must be between 1 and ${maxCount} characters in length", "view_name_repetition": "The view name already exists", "view_permission": "Permissions of view", "view_permission_description": "Permissions overview", "view_permissions": "Check permissions", "view_property_sync_content": "Saving will update the view for everyone.", "view_property_sync_content_2": "View configuration includes filtering, grouping, sorting, hiding fields, layout, styles, etc.", "view_property_sync_success": "The view has been updated for everyone", "view_property_sync_title": "This view has unsaved changes", "view_record_comments": "View this record comments", "view_record_history": "View the modification history of this record", "view_record_history_mobile": "record history", "view_restrictions": "<a href=\"https://aitable.ai/pricing/\">View restrictions</a>", "view_sort_and_group_disabled": "This field does not support setting as \"group\" or \"sort item\".", "view_sort_help": "https://help.aitable.ai/docs/manual-sort", "view_sync_property_close_tip": "View configuration synchronization is turned off, the configuration will not be synchronized to other members, and it will be cleared after refresh", "view_sync_property_tip": "Your changes to the settings will impact others' views", "view_sync_property_tip_close_auto_save": "The view changes has not been saved, which will only take effect for you", "view_sync_property_tip_open_auto_save": "Your changes to the settings wiil be auto-saved and synchronized to others", "view_sync_property_tip_short": "Your changes to the settings will impact others' views", "view_un_lock_success": "View unlocked", "viewers_per_space": "Viewers per Space", "views_per_datasheet": "Views per Datasheet", "Dolphin": "Dolphin", "Dolphin_column": "Field", "Dolphin_community": "Dolphin Community", "Dolphin_copyright": "copyright @ 2019-2021 深圳表格科技有限公司.All rights reserved.", "Dolphin_form": "Form", "Dolphin_form_change_tip": "The form has a new field or type change", "Dolphin_invite_link_template": "From ${sysName}: You were invited to join the \"${spaceName}\" Space by ${nickName}.", "Dolphin_official_accounts": "Official account", "Dolphin_privacy_policy": "<Privacy Policy>", "Dolphin_share_link_template": "From ${sysName}: ${nickName} has shared the \"${nodeName}\" file node with you.", "Dolphin_small_classroom": "Video tutorials", "Dolphin_star": "Dolphin Planet", "Dolphinby_activity_train_camp": "Time-limited welfare", "Dolphinby_helper": "<PERSON><PERSON><PERSON> assistant", "Dolphinby_menu_beginner_task": "Beginner tasks", "Dolphinby_menu_hidden_Dolphinby": "<PERSON><PERSON>", "Dolphinby_menu_releases_history": "Update history", "Dolphinby_todo_menu1": "What is ${sysName}?", "Dolphinby_todo_menu2": "dolphindata's hierarchy", "Dolphinby_todo_menu3": "How to use a datasheet", "Dolphinby_todo_menu4": "Share and invite friends", "Dolphinby_todo_menu5": "Smart onboarding", "Dolphinby_todo_menu6": "Bind email", "Dolphinby_todo_tip1": "Welcome to ${sysName}", "Dolphinby_todo_tip2": "Follow the guide and start your journey in ${sysName}~", "dolphindata": "dolphindata", "virgin_islands_british": "Virgin Islands, British", "virgin_islands_us": "Virgin Islands, US", "visit": "Visit", "vomit_a_slot": "Give feedback", "waiting_for_upload": "Waiting for upload", "wallet_activity_reward": "\"${name}\" reward", "waring_coloring_when_filter_empty": "Please add filters before saving this rule", "warning": "Warning", "warning_can_not_remove_yourself_or_primary_admin": "You can't remove yourself or the admin from the organization", "warning_coloring_records_when_no_single_field": "There is no single select field in current view, please create one and try again.", "warning_confirm_to_del_option": "Are you sure to delete this option?", "warning_exists_sub_team_or_member": "The current team has subordinate teams or members currently. Please delete them first before deleting the team.", "warning_filter_limit": "The limit of filter number is 10", "warning_rule_limit": "The limit of rule number is 10", "watch_out": "Note", "watch_record": "Watch", "watch_record_button_tooltips": "Watch the record and you will be notified when a new comment is made or the content of the record is modified", "watch_record_success": "Watch record successfully", "watermark": "You are experiencing the ${grade} space exclusive feature, which will be unlocked when you upgrade", "watermark_content": "All pages visited by members inside or outside the Space will show watermarks", "watermark_title": "Show watermarks", "way_to_create_dashboard": "How to create a dashboard", "we_already_received_your_apply": "We have received your account deletion request", "web_publish": "", "web_publish_refresh": "Please click to refresh", "wechat": "WeChat", "wechat_bind": "Please use WeChat to scan the code and follow us", "wechat_login": "Use WeChat account to log in", "wechat_login_btn": "Use WeChat scan code to log in", "wechat_payment": "WeChat Pay", "wechat_qr_code": "wechat QR code", "wecom": "WeCom", "wecom_admin_desc": "For WeCom organization's adminstrators managing spaces", "wecom_admin_title": "WeCom space admin panel", "wecom_api_intercept_notification_text": "", "wecom_app_desc": "<p>Using Table on WeCom, you can</p>\n\n<ul><li>Access Table's space and access space data directly on WeCom without logging in;</li><li>Receive notifications from the space through WeCom;</li><li>Set app visibility on WeCom and automatically sync WeCom's organization structure;</li></ul>", "wecom_app_intro": "Allow you to log in to Table through WeCom, receive your Space's notifications in WeCom, and sync organizational chart from WeCom", "wecom_app_notice": "* Note: One Space can only bind one WeCom self-built application", "wecom_base": "Basic Plan with WeCom", "wecom_enterprise": "Ultimate Plan with WeCom", "wecom_grade_desc": "Spaces have three kinds of billing plans: Basic Plan with WeCom(permanently free), Standard Plan with WeCom ,Profession Plan with WeCom and Enterprise Plan with WeCom. Members in the Spaces of different billing plans enjoy different rights and benefits.", "wecom_integration_desc_check": "I know that all members will be removed from the space station and the relevant permission information will be clear", "wecom_integration_domain_check": "Please do not close or refresh the page while the configuration is in progress", "wecom_invite_member_browser_tips": "Inviting members in the space is not supported in the browser. Please go to \"WeCom > Workbench > Dolphin Application\" to invite members.", "wecom_invite_member_version_tips": "Can't invite members possibly because of the low version of WeCom. Please go to \"Wecom > About > Check for updates\" to upgrade and try again.", "wecom_login": "WeCom login", "wecom_login_Internet_error": "Network exception, please go back and try again", "wecom_login_application_uninstall": "Please check whether the application is deleted", "wecom_login_btn_text": "Please scan the wecom code to log in", "wecom_login_fail_button": "Back to home page", "wecom_login_fail_tips_title": "<PERSON><PERSON> failed", "wecom_login_out_of_range": "Please check whether you are removed from the visible range of the WeCom application.", "wecom_login_tenant_not_exsist": "Your organization didn't install the WeCom third-party application. If you're the user of WeCom self-built application, please log in with the enterprise's exclusive domain.", "wecom_logo_unauthorized_error": "The tenant user is not authorized. Please contact the administrator to synchronize the address book and try the operation again", "wecom_new_tab_tooltip": "Use this domain name to go to the space station", "wecom_not_complete_bind_content": "The master administrator of the space has not completed the binding of enterprise wechat, please contact the master administrator", "wecom_not_complete_bind_title": "Failed to enter the space", "wecom_org_manage_reject_msg": "This Space's contacts are managed by the WeCom integration. Please go to the WeCom admin backend to invite members. <a href='/wecom-integration/#question' target=\\\"_blank\\\">How to invite？</a>", "wecom_profession": "Pro Plan with WeCom", "wecom_single_record_comment_mentioned": "Datasheet：{nodeName}\nRecord：{recordTitle}\n\n\"{commentContent}\"\n", "wecom_single_record_comment_mentioned_title": " You're mentioned in a record comment", "wecom_single_record_member_mention_title": " You're mentioned in a record", "wecom_social_deactivate_tip": "If you need to disable the application, please go to WeCom Admin Panel -> Applications -> 表格 -> Delete application.", "wecom_space_list_item_tag_info": "WeCom binding", "wecom_standard": "Standard Plan with WeCom", "wecom_subscribed_record_cell_updated_title": "Someone has changed the record you are watching in", "wecom_subscribed_record_commented_title": "The record you are watching received a new comment", "wecom_sync_address_error": "You have been removed from the visible range of wecom application. Synchronizing the address book will cause the space station to lose the master administrator. Please change the master administrator and synchronize the address book again", "wecom_upgrade_go": "View guidance", "wecom_upgrade_guidance": "Want to upgrade or renew your plan?<br/>\nPlease go to WeCom Admin Panel to make a subscription. You also can contact our customer service staff if you need help.", "wecom_widget_created_by_field_name": "${fieldName}(Created By field isn't supported)", "wecom_widget_last_edited_by_field_name": "${fieldName}(Last Edited By field isn't supported)", "wecom_widget_member_field_name": "${fieldName}(Member field isn't supported)", "weixin_share_card_desc": "", "weixin_share_card_title": "", "welcome_interface": "Welcome screen", "welcome_module1": "What is APITable?", "welcome_module2": "Quick start in one minute", "welcome_module3": "How to use a datasheet", "welcome_module4": "Content Calendar", "welcome_module5": "Project Management", "welcome_module6": "Real Estate CRM", "welcome_module7": "FAQs", "welcome_module8": "Product manual", "welcome_module9": "Developer center", "welcome_more_help": "More", "welcome_sub_title1": "Watch videos to learn about Table", "welcome_sub_title2": "Start building from templates", "welcome_sub_title3": "Learn more from Help Center", "welcome_title": "Welcome to APITable!", "welcome_use": "Welcome  to use", "welcome_workspace_tip1": "You can create a datasheet in a blink and start the journey with us!", "welcome_workspace_tip2": "Welcome to APITable, let's start our journey to \"building blocks-like multimedia database-spreadsheet hybrid\"!", "when": "When", "when_meet_the_following_filters": "When meet the following filters", "where": "Where", "whether_bind_with_invited_email": "Confirm whether to bind with the invited account ${inviteEmail}", "who_shares": " shared with you", "widget": "Widget", "widget_center": "Widget Center", "widget_center_create_modal_desc": "First give your widget a name, and then choose which component template to start developing < a href = \"\" /> learn more", "widget_center_create_modal_title": "Create a widget", "widget_center_create_modal_widget_agreement": "I have read and agreed to the <a href = \"\"> \"wig watch developer agreement\"", "widget_center_create_modal_widget_name": "Widget name", "widget_center_create_modal_widget_name_placeholder": "Enter a widget name", "widget_center_create_modal_widget_template": "Start from a widget template", "widget_center_create_modal_widget_template_link": "View source", "widget_center_help_tooltip": "See documentation", "widget_center_install_modal_content": "Custom widgets may read from or write to the datasheet. Please be aware of the risks before installing the widget.", "widget_center_install_modal_title": "Install the custom widget", "widget_center_menu_transfer": "Transfer widget ownership", "widget_center_menu_unpublish": "Unpublish widget", "widget_center_official_introduction": "Recommended widgets are published by officially certified developers and can be installed in all Spaces", "widget_center_publisher": "Publisher: ", "widget_center_space_introduction": "Custom widgets are developed and published by your Space member and can be installed only in this Space", "widget_center_tab_official": "Recommended", "widget_center_tab_space": "Custom", "widget_cli_upgrade_tip": "Your widget-cli tool has a low version. Please execute \"npm i -g @apitable/widget-cli\" in your terminal to upgrade it.", "widget_collapse_tooltip": "Collapse widget", "widget_connect_error": "Unable to connect to your local widget. Please make sure the address is correct", "widget_continue_develop": "Toggle dev mode", "widget_cret_invalid_error_content": "<p>1. Open \"chrome://flags/#allow-insecure-localhost\" in your browser </p><p> 2. Change the highlighted option from \"Disabled\" to \"Enabled\" </p> <p> 3. Restart the browser to make the new configuration take effect</p>", "widget_cret_invalid_error_title": "Please disable your browser's security restrictions on loading local code", "widget_datasheet_has_delete": "The related datasheet was deleted", "widget_dev_config_content": "Please go to the project folder of your widget first, then execute the ${startCmd} command, and paste the returned URL below", "widget_dev_url": "Development address: ", "widget_dev_url_input_placeholder": "Please fill in the URL returned from the terminal", "widget_developer_novice_guide": "Developer guide", "widget_disable_fullscreen": "Exit fullscreen", "widget_enable_fullscreen": "Enter fullscreen", "widget_env_error": "This applet is not supported in ${errorEnv}, please go to ${expectEnv}", "widget_expand_tooltip": "Expand widget", "widget_filter_add_filter": "Add filter", "widget_filter_condition_numbers": "{FILTER_NUM} filter", "widget_filter_tips": "Filtered data is not read by widget", "widget_has_be_delete": "The widget has been deleted", "widget_hide_settings_tooltip": "Hide settings panel", "widget_homepage_tooltip": "Widget homepage", "widget_import_from_airtable_done": "${ROW_NUM} records were successfully imported.", "widget_import_from_airtable_done_button": "Import again", "widget_import_from_airtable_field_name_suffix": "-from_Airtable", "widget_import_from_airtable_start_import": "configuration & Import", "widget_import_from_airtable_start_import_description": "Please click on the button below or make this app bigger", "widget_import_from_airtable_step_1_title": "1. Please enter the required information from airtable", "widget_import_from_airtable_step_1_viewid": "View ID（Optional）", "widget_import_from_airtable_step_1_warning_content": "Please confirm that the content of the filling is correct", "widget_import_from_airtable_step_2_description": "Please select the field type for the airtable field. When it completed, fields will be created and filled.", "widget_import_from_airtable_step_2_fields": "Fields", "widget_import_from_airtable_step_2_fields_type": "Field types", "widget_import_from_airtable_step_2_title": "2. Configure field type", "widget_import_from_airtable_step_2_waring_file_upload": "The attachment column is detected. The widget currently only supports the import of attachments that do not exceed 10MB. Do you continue?", "widget_import_from_airtable_step_3_button": "Stop", "widget_import_from_airtable_step_3_description": "Importing data ……", "widget_import_from_airtable_step_3_import_num": "Imported ${ROW_NUM} records", "widget_import_from_airtable_tutorial": "tutorial", "widget_import_from_airtable_wrong_button": "Reconfigure", "widget_install_error_env": "This applet does not support the installation and use of ${errorEnv} in the table. Please go to ${expectEnv} for installation and use", "widget_install_error_title": "Installation failed", "widget_item_build": "Custom", "widget_item_developing": "Dev mode", "widget_load_error": "Disconnected from the server, please re-execute the \"widget-cli start\" command, then refresh the widget to continue development", "widget_load_error_ban_btn": "Apply for recovering", "widget_load_error_ban_content": "The widget violates relevant treaties and has been banned and temporarily unavailable", "widget_load_error_ban_title": "The widget is banned", "widget_load_error_load_error": "Disconnected from the server, please re-execute the \"widget-cli start\" command, then refresh the widget to continue development", "widget_load_error_not_match": "The local component package ID is inconsistent with the component package ID of the current component board. Please confirm that you have not modified the parameter value of packageId in the local code", "widget_load_error_published": "Please contact the widget publisher @${authorName} for troubleshooting", "widget_load_error_title": "Error occurred when running the widget", "widget_load_url_error_not_match": "Unable to connect to the local widget, please check if there are any errors in your code, or make sure the packageId in your code is \"${widgetPackageId}\"", "widget_loader_developing_content": "Click the button below to continue development", "widget_loader_developing_title": "This widget is not published", "widget_loader_error_cret_invalid": "Please first release the browser's security restrictions on loading local code, otherwise the widget cannot be loaded smoothly", "widget_loader_error_cret_invalid_action_text": "How to cancel", "widget_more_settings": "More settings", "widget_more_settings_tooltip": "More settings", "widget_name": "Custom widget", "widget_name_length_error": "The name must be between 1 and 30 characters in length", "widget_no_access_datasheet": "No permission to access the data", "widget_num": "${count} widget(s)", "widget_operate_delete": "Delete widget", "widget_operate_enter_dev": "Toggle dev mode", "widget_operate_exit_dev": "Exit dev mode", "widget_operate_publish_help": "How to publish widget", "widget_operate_refresh": "Refresh widget", "widget_operate_rename": "<PERSON><PERSON> widget", "widget_operate_send_dashboard": "Send to dashboard", "widget_operate_setting": "Configure widget", "widget_panel": "Widget board", "widget_panel_count_limit": "This datasheet has the maximum number of widget boards.", "widget_panel_title": "Widget board ${count}", "widget_per_space": "Widget", "widget_publish_help_desc": "After the widget is published, members in your Space can install it in the Widget Center", "widget_publish_help_step1": "1. After you've finished development and make sure the preview is correct, tap ${cmd} in the terminal", "widget_publish_help_step2": "2. Then execute the following command to publish:", "widget_publish_modal_content": "The result after the release command is executed on the terminal + the released widget appears in the component center", "widget_publish_modal_title": "How to publish widget", "widget_reference": "From \"${dst_name}\"", "widget_runtime_desktop": "desktop", "widget_runtime_mobile": "mobile", "widget_scripting_edit_code": "Edit code", "widget_scripting_edit_console": "<PERSON><PERSON><PERSON>", "widget_scripting_edit_examples": "Examples", "widget_scripting_edit_finish": "Finish editing", "widget_scripting_edit_stop": "Stop", "widget_scripting_run": "Run", "widget_scripting_start": "Start", "widget_show_settings_tooltip": "Show settings panel", "widget_something_went_wrong": "Something went wrong", "widget_start_dev": "Start preview", "widget_step_dev": "Preview", "widget_step_dev_content_label": "Enter the widget development address:", "widget_step_dev_desc": "With the URL address of the local service, you can preview the widget in real time", "widget_step_dev_title": "Preview widget", "widget_step_init": "Initialize", "widget_step_init_content_desc": "Note: this command will ask you to enter your API token and space station ID < a > how to get it</a>", "widget_step_init_content_label": "Then execute the following command: ", "widget_step_init_desc": "Verify your developer identity and create project files for your new widget", "widget_step_init_title": "Initialize widget", "widget_step_install": "Install", "widget_step_install_content_label1": "1. Open the terminal on your computer\n\n", "widget_step_install_content_label2": "2. Execute the following command in the terminal", "widget_step_install_desc": "You can use the widget CLI  tool to initialize, run, publish, and unpublish widgets", "widget_step_install_title": "Install widget CLI", "widget_step_start": "Start", "widget_step_start_content_desc2": "Note: this command will generate a URL that will be used in the next step", "widget_step_start_content_label1": "1. Go to the project folder of the new widget and start development:", "widget_step_start_content_label2": "2. After development, execute the following command to start the widget", "widget_step_start_desc": "Follow the steps below to start the widget's local service and enter development mode", "widget_step_start_title": "Start widget", "widget_tip": "Widget", "widget_transfer_modal_content": "<ul><li><p>Current publisher: ${oldOwner}</p><p>The member can no longer publish or unpublish the widget in the Widget Center via the CLI tool.</p></li><li> <p>New publisher: ${newOwner}</p> <p>The member can publish and unpublish the widget in the Widget Center via the CLI tool.</p></li></ul>", "widget_transfer_modal_title": "Transfer ownership of \"${widgetPackageName}\"", "widget_transfer_success": "Transferred", "widget_unknow_err": "Unknown error ${info}", "widget_unpublish_modal_content": "Are you sure to unpublish the widget?", "widget_website": "Widget website", "widgets_per_dashboard": "widgets per Dashboard", "width_limit_waring": "The maximum column width limit of the first column has been reached so the adjustment will not take effect", "without_day": "indefinite", "wizard_14_success_message": "V coins rewarded", "wizard_15_success_message": "V coins rewarded", "wizard_16_success_message": "V coins rewarded", "wizard_17_success_message": "V coins rewarded", "wizard_18_success_message": "V coins rewarded", "wizard_20_success_message": "V coins rewarded", "wizard_reward": "Completed smart onboarding tasks", "wizard_video_reward": "Watched an onboarding video", "work_data": "Datasheets", "workbench_instruction_of_all_member_setting_and_node_permission": "Controls what can do and what can't do from the workbench", "workbench_setting": "Workbench Permission", "workbench_setting_all": "Global settings", "workbench_setting_cannot_export_datasheet_describle": "If turn on, datasheets of the workbench can be imported to local computer when member has the permission of the file nodes.", "workbench_setting_cannot_export_datasheet_tips": "If members have the \"manageable\" permission to a datasheet, they can export it to a local file.", "workbench_setting_cannot_export_datasheet_title": "Allow exporting datasheets", "workbench_setting_show_watermark_tips": "The workbench and the contacts support watermarks to prevent members from leaking enterprise information. A watermark includes the name and phone number suffix/email prefix.", "workbench_setting_show_watermark_title": "Show watermarks", "workbench_share_link_template": "From ${sysName}: ${nickName} has shared the \"${nodeName}\" file node with you.", "workbench_side_space_template": "Custom templates", "workbenck_shortcuts": "Workbench", "workdoc_attach_uploading": "Uploading...", "workdoc_authentication_failed": "Authentication failed", "workdoc_background_title": "Text background", "workdoc_code_placeholder": "Please enter code block", "workdoc_collapsed": "<PERSON>de", "workdoc_color_default": "Restore default", "workdoc_color_title": "Text color", "workdoc_create": "Create WorkDoc", "workdoc_expanded": "Expand the table of contents", "workdoc_image_max_size": "The image size cannot exceed ${size}", "workdoc_info": "WorkDoc Info", "workdoc_info_create_time": "Created at", "workdoc_info_creator": "Created by", "workdoc_info_last_modify_people": "Last modified by", "workdoc_info_last_modify_time": "Last modified at", "workdoc_link_placeholder": "Please enter link", "workdoc_only_image": "Only image is allowed", "workdoc_only_video": "Only video is allowed", "workdoc_text_placeholder": "Enter \"/\" to start", "workdoc_title_placeholder": "Please enter title", "workdoc_unnamed": "Unnamed WorkDoc", "workdoc_unsave_cancel": "Back", "workdoc_unsave_content": "Are you sure you want to exit without saving?", "workdoc_unsave_ok": "Discard changes", "workdoc_unsave_title": "The WorkDoc has not been saved", "workdoc_upload_failed": "Upload failed", "workdoc_video_max_size": "The video size cannot exceed ${size}", "workdoc_ws_connected": "Connected", "workdoc_ws_connecting": "Connecting...", "workdoc_ws_disconnected": "Disconnected", "workdoc_ws_reconnecting": "Reconnecting...", "workflow_execute_failed_notify": "<a class=\"automationName\"></a> failed to execute at <a class=\"endAt\"></a>. Please review the execution history to troubleshoot the issue. If you need any assistance, please contact our customer service team.", "workspace_data": "Space data", "workspace_files": "Workbench data", "workspace_list": "Spaces", "wrap_text": "Wrap text", "wrong_url": "Wrong URL", "x_axis_field_date_range": "Set date range", "x_axis_field_date_range_by_day": "By day", "x_axis_field_date_range_by_quarter": "By quarter", "x_axis_field_date_range_by_week": "By week", "x_axis_field_date_range_by_year": "By year", "y_axis_field_average": "AVERAGE", "y_axis_field_max": "MAX", "y_axis_field_min": "MIN", "y_axis_field_sum": "SUM", "year": "Year", "year_month_day_hh_mm": "Year/Month/Day Hour:Minute", "year_month_day_hyphen": "Year-Month-Day", "year_month_day_slash": "Year/Month/Day", "year_season_hyphen": "Year-Season", "year_week_hyphen": "Year-Week", "yemen": "Yemen", "yesterday": "yesterday", "you": "You", "your_account_will_destroy_at": "Your account will be deleted at ${time}", "zambia": "Zambia", "zimbabwe": "Zimbabwe", "zoom_in": "zoom in", "zoom_out": "zoom out", "seat_nums_over_limit": "Number of space station seats exceeded", "file_nums_over_limit": "Number of files exceeded", "mirror_nums_over_limit": "Number of mirrors exceeded", "form_nums_over_limit": "Number of forms exceeded", "admin_nums_over_limit": "Number of administrators exceeded", "node_role_nums_over_limit": "Number of node roles exceeded", "field_role_nums_over_limit": "Number of field roles exceeded", "row_nums_over_limit": "Space station record count exceeds the limit", "szkz_feishu_login": "Digital Science <PERSON><PERSON><PERSON>", "zkzd_feishu_login": "<PERSON><PERSON><PERSON>", "oper_options_label_others": "Others", "oper_options_label_add": "Add", "oper_options_label_edit": "Edit", "oper_options_label_del": "Delete", "oper_options_label_output": "Output", "oper_options_label_input": "Input", "oper_name": "Operator Name", "oper_type": "Type of Operation", "oper_address": "Operation Address", "oper_location": "Operation Location", "oper_status": "Operation Status", "oper_status_succerr": "Success", "oper_status_fail": "Fail", "oper_time": "Operation Time", "oper_tips": "Operation Tip", "operate_content_del": "Are you sure you want to delete all selected data?", "delete_fail_retry": "Delete failed, please retry later", "batch_delete": "<PERSON><PERSON> Delete", "placeholder_search_input": "Please enter content", "placeholder_search_change": "Please select content", "system_management": "System Management", "system_member_management": "Member Management", "system_member_management_info": "Can set up user functions for Dolphin Table, including viewing all users, prohibiting/allowing login, etc.", "admin_login_operate_tips": "Are you sure you want to ${isPausedText} ${nickName}to login?", "operate_fail_retry": "Operation Failed, Please Retry Later", "admin_member_name": "User Name", "admin_member_email": "User Email", "admin_member_status": "Status (Allow/Prohibit)", "admin_member_create_time": "Creation Time", "login_allow": "Allow Login", "login_puase": "Prohibit Login", "admin_member_login_logs": "Member <PERSON><PERSON>", "admin_member_login_logs_intr": "System Member Login <PERSON>gs", "admin_member_login_logs_address": "Login Address", "admin_member_login_logs_location": "Login Location", "admin_member_login_logs_browser": "Browser", "admin_member_login_logs_system": "Operating System", "admin_member_login_logs_login_time": "Login Time", "admin_member_login_logs_del_tips": "Are you sure you want to delete all selected data?", "shortcut": "Shortcut", "shortcut_add": "Add Shortcut", "history": "History", "history_file_name": "File Name", "history_modified_time": "Modified Time", "history_createdby": "Created by", "history_recent": "Recent", "dolphin_table": "Dolphin Table", "doc_page_outline_tips": "You are currently in offline mode, changes will be synced after reconnecting", "unknown_user": "Unknown user", "new_doc": "New Document", "allow": "Allow", "ban": "Ban", "auth_error_title": "Authorization Error", "auth_error_content": "You do not have permission to perform this action, please obtain permission first.", "disabled_status": "Disabled status", "ws_connect_error": "Network connection is abnormal, please edit in a good network  environment.", "choose_filefloder_root_error": "Please select a folder that is not the root directory", "sysLogo": "Logo", "sysIcon": "Icon", "sysSetting": "System Setting", "sysName": "System Name", "uploadSysImg": "Upload Images", "sysSettingInfo": "You can set the system name, system logo, and system icon", "sysLogoTips": "The default image size is 200*200. If no image is uploaded, the default dolphin table LOGO will be used.", "sysIconTips": "The default image size is 200*200. If no image is uploaded, the default dolphin table Icon will be used."}