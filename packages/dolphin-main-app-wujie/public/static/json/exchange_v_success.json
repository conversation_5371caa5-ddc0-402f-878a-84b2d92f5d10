{"v": "5.6.10", "fr": 30, "ip": 0, "op": 161, "w": 440, "h": 224, "nm": "Pre synthesis 1", "ddd": 0, "assets": [{"id": "0", "w": 54, "h": 54, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAA2CAMAAAC7m5rvAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAANlBMVEX///////////////////////////9HcEz////////////////////////////////////////xvgS0AAAAEnRSTlO1ZTiILQtxAE+qFoeTkiIXfXwD6zkmAAABA0lEQVRIx73W3Q7CIAyG4Y+/2o459f5vVjfUaGarNAZOyXPyskFxUFcp+h70rZxdjNnFQnCxlFxMxMMKMDlYBqKDMcAOFgBysATMDiaA9LNbSCMljJBGShghjZQqo5VRN0srm7uZrEx62QJYKWGF1FPCCqmn1NilMepkqbH5dzZFphn3lc6cly9sA4LdkkScywemgXccnhglc0hfwB5vrPawurH73/UDliNxnD4k0fArUA9gyXypD7ED9nEfm6qdXwk1dh7zKcfGcic7NbaMuRRayuq78MKo6zVaIXW2pSyjHqo1ZXUwMkL+/8mPRkiDnYyQ/x+eDqmOHAydY6hz6LVG7CstK0bKtUQ57gAAAABJRU5ErkJggg==", "e": 1}, {"id": "1", "w": 39, "h": 39, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACcAAAAnCAMAAAC7faEHAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAQlBMVEX/7iP/7iP/7SD/7yL/7iP/7yP/7SRHcEz/7ST/7ST/7iP/7SP/7iP/7SL/7SP/7ST/7ST/7ST/7ST/7SH/7SL/7R4JfW5OAAAAFnRSTlPgxDh+0ow4AA4cmoy2fpqaKlRiVGIqrAm5HQAAANRJREFUOMut1FkOgzAMBNAhW+2wL73/VQuopQVMYqnMZ/RkZbPxOCcIaxDWKp3rwCo3YlK5iEblHJzGBQBG4fzsbN7xzOAo65ZyQkFI5YQdHhwVb1dQ0kV84lPOAxfw19EvAyLJzhTYp2DBkcU5ng/OWAcx3myOp3iB1rg4PmdnKuRThble17fJeu3A2/5CeYHqcDgvS9KScH98ur9w8R7t/kpI9b429V++sLnl/63dtoRz/VGvrMz2ETmpnNC/tVTuj3mgnS/qeTWgVzlGd+t8Fuf9CxSSIO2AIHwwAAAAAElFTkSuQmCC", "e": 1}, {"id": "2", "w": 320, "h": 320, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "3", "w": 188, "h": 188, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "4", "w": 27, "h": 28, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "5", "w": 25, "h": 30, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "6", "w": 36, "h": 24, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAYCAMAAAClZq98AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAS1BMVEVHcEz/cXH/cHD/cHD/cnL/cHD/cHD/cHD/cXH/cXH/cHD/cXH/cHD/cnL/cXH/cnL/cXH/cHD/cXH/cnL/cXH/cHD/cHD/cnL/cXF2J9tDAAAAGHRSTlMAz4C/n2AQIN/vQKBwf5CPbzBfgL+fkK/e9aOAAAAAk0lEQVQoz42S2RbCIAxEKTQkdHPX+f8vtWpPlRJJ7yv3ZDKAcxrUsjMRwNQILwxNAFMjrPzVWABT65EhI1fDFq5lWCqkW32hD7zDuexwmo3TKY7kYfEA0yFlDNLpN2hMqMwh79uHasw7c6XQwhS/tRtdSUPWSrXC9p7v5Zhz+fJDvnnyUf1ovam8tWOYe0qYuvLsCVQKI1ZLYrsoAAAAAElFTkSuQmCC", "e": 1}, {"id": "7", "w": 17, "h": 27, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAbCAMAAACtKJSuAAAAYFBMVEVHcEyI7yKI7yKI7yGI7yKK7yCH7SKH7yCI7yKA7yCI7yGH7CCH7yGD8B+H7yCH7ySI7iGG7yCH7yKI7yKJ7SKH7COI7yGF7x+G7CCH7SGJ7yOI7SKI7yOJ7COJ7yKI7yLQ3GB8AAAAH3RSTlMA7t6/3zCAIO8Q32DOIUBAsFCAj3Bg3kFQoF+Qn1CP9ntYsAAAAI9JREFUGNNl0dkSgyAMBVCoUoFabavdl/v/f1lZEoPkiTlzB5KgVKqxU0W5ny3BeDQFvACcJDwWwEdA57HJPAOgFWKjwDGMCXBlOWRpKsFQCYe+JJhpJBZLw3qmNtMbKx2jDKjISroH2QvBLkzjREiny0XonFvi0IX6ptC07qiP0MvN6vBM8WVGb2Chm8mnP47hF1dw80xQAAAAAElFTkSuQmCC", "e": 1}, {"id": "8", "w": 20, "h": 14, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAMAAAAYGszCAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAPFBMVEVHcEyBye+AyO6Aye6AyO6AyO+Av++Ax++AyO6Ax+uAx+2Ax+6AyO+ByO+Ax++AyO6Axu2AyO2Ax+2AyO6cF23nAAAAE3RSTlMAf+/Pv58QIN9AgM6Pb0DekJ+fGDWFFwAAAFhJREFUGNNtkEcWwCAIBTEW1PRw/7tG0yDiLAceHwBgEmgmrxRaiq0yAxEZYXysprIjBxDj5uUpbPTnkmHsSMBVOvumi974pYVDu0LuuDLC1ZWwPTXl+ycnFbcICZ4rAHoAAAAASUVORK5CYII=", "e": 1}, {"id": "9", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON>pe Layer 10", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17, "s": [86]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 63, "s": [86]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 86, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 109, "s": [86]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 132, "s": [0]}, {"t": 155, "s": [86]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [884.75, 492.75, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": -6, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 17, "s": [73.997, 73.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 40, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 63, "s": [65.997, 65.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 86, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 109, "s": [65.997, 65.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 132, "s": [0, 0, 100]}, {"t": 155, "s": [65.997, 65.997, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.976, 0.267, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": -6, "op": 294, "st": -6, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.001, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 79, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 102.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 125, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 148.001, "s": [0]}, {"t": 171, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [663.25, 372.75, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 33.001, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 56, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 79, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 102.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 125, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 148.001, "s": [0, 0, 100]}, {"t": 171, "s": [60.997, 60.997, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.976, 0.267, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 10, "op": 310, "st": 10, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 9", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 53.001, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 99, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 122.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 145, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 168, "s": [0]}, {"t": 191, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [557.25, 522.25, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 53.001, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 76, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 122.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 145, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 168, "s": [0, 0, 100]}, {"t": 191, "s": [60.997, 60.997, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.984, 0.486, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 30, "op": 330, "st": 30, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "S<PERSON>pe Layer 5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 57, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 80, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 103, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 126, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 149.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 172, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 195.001, "s": [0]}, {"t": 218, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [854.75, 480.25, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 57, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 80, "s": [16.997, 16.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 103, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 126, "s": [16.997, 16.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 149.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 172, "s": [16.997, 16.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 195.001, "s": [0, 0, 100]}, {"t": 218, "s": [16.997, 16.997, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.976, 0.267, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 57, "op": 357, "st": 57, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 47, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70.001, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 93, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 116, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 139.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 162, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 185.001, "s": [0]}, {"t": 208, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [851.75, 392, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 47, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 70.001, "s": [80, 80, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 93, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 116, "s": [80, 80, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 139.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 162, "s": [80, 80, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 185.001, "s": [0, 0, 100]}, {"t": 208, "s": [42, 42, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.984, 0.486, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 47, "op": 347, "st": 47, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 67, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 113, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 136.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 159, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 182.001, "s": [0]}, {"t": 205, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [630.25, 552, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 67, "s": [77, 77, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 90, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 113, "s": [77, 77, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 136.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 159, "s": [77, 77, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 182.001, "s": [0, 0, 100]}, {"t": 205, "s": [39, 39, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.984, 0.506, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 44, "op": 344, "st": 44, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32.001, "s": [75]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 78, "s": [75]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 101.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 124, "s": [75]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 147.001, "s": [0]}, {"t": 170, "s": [75]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [877.25, 518.5, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 32.001, "s": [121, 121, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 55, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 78, "s": [121, 121, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 101.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 124, "s": [121, 121, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 147.001, "s": [0, 0, 100]}, {"t": 170, "s": [83, 83, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.98, 0.435, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 9, "op": 309, "st": 9, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50.001, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 73, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 96, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 119.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 142, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 165, "s": [0]}, {"t": 188, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [580.75, 440.25, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 27, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 50.001, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 73, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 96, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 119.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 142, "s": [60.997, 60.997, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 165, "s": [0, 0, 100]}, {"t": 188, "s": [60.997, 60.997, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.984, 0.541, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 27, "op": 327, "st": 27, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 69, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 92.001, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 115, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 138.001, "s": [0]}, {"t": 161, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [567.25, 452, 0], "ix": 2}, "a": {"a": 0, "k": [-128.5, -23.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [121, 121, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 46, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 69, "s": [121, 121, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 92.001, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 115, "s": [121, 121, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 138.001, "s": [0, 0, 100]}, {"t": 161, "s": [83, 83, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 5, "ix": 6}, "is": {"a": 0, "k": 132, "ix": 8}, "or": {"a": 0, "k": 13.6, "ix": 7}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "Multilateral star path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.98, 0.373, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-128.5, -23.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [66.162, 66.162], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Multilateral star 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "Pre synthesis 4", "refId": "a", "sr": 1, "ks": {"o": {"a": 0, "k": 41, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [507, 492, 0], "ix": 2}, "a": {"a": 0, "k": [720, 512, 0], "ix": 1}, "s": {"a": 0, "k": [118, 118, 100], "ix": 6}}, "ao": 0, "w": 1440, "h": 1024, "ip": 32.75, "op": 332.75, "st": 32.75, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "Pre synthesis 2", "refId": "b", "sr": 1, "ks": {"o": {"a": 0, "k": 64, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [599.5, 465, 0], "ix": 2}, "a": {"a": 0, "k": [590.802, 455.511, 0], "ix": 1}, "s": {"a": 0, "k": [144, 144, 100], "ix": 6}}, "ao": 0, "w": 1440, "h": 1024, "ip": 81.5, "op": 381.5, "st": 81.5, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "Pre synthesis 3", "refId": "c", "sr": 1, "ks": {"o": {"a": 0, "k": 67, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [700, 528, 0], "ix": 2}, "a": {"a": 0, "k": [720, 512, 0], "ix": 1}, "s": {"a": 0, "k": [97, 97, 100], "ix": 6}}, "ao": 0, "w": 1440, "h": 1024, "ip": 129.75, "op": 429.75, "st": 129.75, "bm": 0}]}, {"id": "a", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector 88", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [848.338, 554.838, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, -1.061], [1.061, 1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 80", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector 87", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [848.389, 547.111, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, 1.061], [1.061, -1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 78", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Path 2", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector 86", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [844.5, 545.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 1.5], [0, -1.5]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 76", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector 85", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [839, 551, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.5, 0], [-1.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 74", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector 84", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [840.56, 547.061, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, -1.061], [1.061, 1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 79", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Vector 83", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [840.611, 554.889, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, 1.061], [1.061, -1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 77", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Vector 82", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [844.5, 556.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 1.5], [0, -1.5]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 75", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 3", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Vector 81", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [850, 551, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.5, 0], [-1.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.443, 0.443, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 73", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [100]}, {"t": 33.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24.375, "s": [0]}, {"t": 33.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}]}, {"id": "b", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector 96", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [594.588, 459.088, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, -1.061], [1.061, 1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 80", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector 95", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [594.639, 451.361, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, 1.061], [1.061, -1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 78", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Path 2", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector 94", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [590.75, 449.75, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 1.5], [0, -1.5]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 76", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector 93", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [585.25, 455.25, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.5, 0], [-1.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 74", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector 92", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [586.81, 451.311, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, -1.061], [1.061, 1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 79", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Vector 91", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [586.861, 459.139, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, 1.061], [1.061, -1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 77", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Vector 90", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [590.75, 460.75, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 1.5], [0, -1.5]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 75", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 3", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Vector 89", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [596.25, 455.25, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.5, 0], [-1.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376, 0.596, 0.953, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 73", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 50.25, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33.75, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [0]}, {"t": 50.25, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 33.75, "op": 183.75, "st": 33.75, "bm": 0}]}, {"id": "c", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector 96", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [840.838, 416.338, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, -1.061], [1.061, 1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 80", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector 95", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [840.889, 408.611, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, 1.061], [1.061, -1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 78", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Path 2", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector 94", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [837, 407, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 1.5], [0, -1.5]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 76", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector 93", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [831.5, 412.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.5, 0], [-1.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 74", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector 92", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [833.06, 408.561, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, -1.061], [1.061, 1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 79", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Vector 91", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [833.111, 416.389, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.061, 1.061], [1.061, -1.061]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 1.061, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 77", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Vector 90", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [837, 418, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 1.5], [0, -1.5]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 75", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 3", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Vector 89", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [842.5, 412.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [120, 120, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.5, 0], [-1.5, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "hd": false}, {"ty": "rd", "nm": "Circular 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.671, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 73", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [100]}, {"t": 30.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22.875, "s": [0]}, {"t": 30.75, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Path 1", "hd": false}], "ip": 15, "op": 165, "st": 15, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Presynthesis 5", "refId": "9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [220, 161, 0], "ix": 2}, "a": {"a": 0, "k": [720, 512, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1440, "h": 1024, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "Star 6.png", "cl": "png", "refId": "0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [190, 84.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [190, 89.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [190, 84.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [190, 89.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [190, 84.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [190, 89.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 160, "s": [190, 84.75, 0]}], "ix": 2}, "a": {"a": 0, "k": [27, 27, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42.007, "s": [25, 25, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61.488, "s": [40.3, 40.3, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 81.19, "s": [25, 25, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100.893, "s": [40.3, 40.3, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 120.596, "s": [25, 25, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 140.297, "s": [40.3, 40.3, 100]}, {"t": 160, "s": [25, 25, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "Star 1.png", "cl": "png", "refId": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [270, 160.75, 0], "ix": 2}, "a": {"a": 0, "k": [19.5, 19.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42.007, "s": [15, 15, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61.488, "s": [24, 24, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 81.19, "s": [15, 15, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100.893, "s": [24, 24, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 120.596, "s": [15, 15, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 140.297, "s": [24, 24, 100]}, {"t": 160, "s": [15, 15, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "Star 1.png", "cl": "png", "refId": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [264, 63.25, 0], "ix": 2}, "a": {"a": 0, "k": [19.5, 19.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42.007, "s": [13, 13, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61.488, "s": [22, 22, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 81.19, "s": [13, 13, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100.893, "s": [22, 22, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 120.596, "s": [13, 13, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 140.297, "s": [22, 22, 100]}, {"t": 160, "s": [13, 13, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "Star 1.png", "cl": "png", "refId": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [128.75, 134, 0], "ix": 2}, "a": {"a": 0, "k": [19.5, 19.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42.007, "s": [15, 15, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61.488, "s": [24, 24, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 81.19, "s": [15, 15, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100.893, "s": [24, 24, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 120.596, "s": [15, 15, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 140.297, "s": [24, 24, 100]}, {"t": 160, "s": [15, 15, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "Star 1.png", "cl": "png", "refId": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [142, 144.75, 0], "ix": 2}, "a": {"a": 0, "k": [19.5, 19.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42.007, "s": [25, 25, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61.488, "s": [35, 35, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 81.19, "s": [25, 25, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 100.893, "s": [35, 35, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 120.596, "s": [25, 25, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 140.297, "s": [35, 35, 100]}, {"t": 160, "s": [25, 25, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 1", "td": 1, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [69]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [100]}, {"t": 94, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 135, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [144.857, 58.253, 0], "to": [20.917, 18.333, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [270.357, 168.253, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 89, "s": [144.857, 58.253, 0], "to": [0, 0, 0], "ti": [-20.917, -18.333, 0]}, {"t": 108, "s": [270.357, 168.253, 0]}], "ix": 2}, "a": {"a": 0, "k": [3.857, -53.247, 0], "ix": 1}, "s": {"a": 0, "k": [100, 47.988, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [195.023, 62.623], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "RectanglePath 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 62, "ix": 5}, "r": 1, "bm": 3, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-11.109, -73.304], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 119.417], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "Group 379.png", "cl": "png", "tt": 1, "refId": "2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 2.786, "s": [0]}, {"t": 10.214, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20.429, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 41.786, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 160, "s": [220, 121, 0]}], "ix": 2}, "a": {"a": 0, "k": [160, 160, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 2.786, "s": [-2, -2, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14.365, "s": [36.303, 36.303, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [26.3, 26.3, 100]}, {"t": 30.56, "s": [31.3, 31.3, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 3}, {"ddd": 0, "ind": 9, "ty": 2, "nm": "Group 379.png", "cl": "png", "refId": "2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 2.786, "s": [0]}, {"t": 10.214, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20.429, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 41.786, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [220, 126, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 160, "s": [220, 121, 0]}], "ix": 2}, "a": {"a": 0, "k": [160, 160, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 2.786, "s": [-2, -2, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14.365, "s": [36.303, 36.303, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [26.3, 26.3, 100]}, {"t": 30.56, "s": [31.3, 31.3, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 2, "nm": "Group 380.png", "cl": "png", "refId": "3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [45]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42.007, "s": [90]}, {"t": 160, "s": [360]}], "ix": 10}, "p": {"a": 0, "k": [220, 117.76, 0], "ix": 2}, "a": {"a": 0, "k": [93.91, 93.91, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 2, "nm": "Layer 4 2.png", "cl": "png", "refId": "4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [2]}, {"t": 42.007, "s": [49]}], "ix": 11}, "r": {"a": 0, "k": 106, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [220, 121, 0], "to": [-18.729, -8.646, 0], "ti": [18.729, 9.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [107.625, 69.125, 0], "to": [-0.104, -4.875, 0], "ti": [0, 0.917, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [107.625, 64, 0], "to": [0, -0.917, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [107.625, 69, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [107.625, 64, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [107.625, 69, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [107.625, 64, 0], "to": [0, 0, 0], "ti": [0, -0.833, 0]}, {"t": 160, "s": [107.625, 69, 0]}], "ix": 2}, "a": {"a": 0, "k": [13.65, 14.15, 0], "ix": 1}, "s": {"a": 0, "k": [72.96, 72.99, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 2, "nm": "Group 1 1.png", "cl": "png", "refId": "5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [220, 121, 0], "to": [-20.167, 8.667, 0], "ti": [20.167, -7.833, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [99, 173, 0], "to": [-0.042, -3.042, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [99, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [99, 173, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [99, 168, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [99, 173, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [99, 168, 0], "to": [0, 0, 0], "ti": [0, -0.833, 0]}, {"t": 160, "s": [99, 173, 0]}], "ix": 2}, "a": {"a": 0, "k": [12.48, 14.82, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 2, "nm": "Vector 35.png", "cl": "png", "refId": "6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [345.625, 87.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [345.625, 92.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [345.625, 87.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [345.625, 92.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [345.625, 87.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [345.625, 92.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 160, "s": [345.625, 87.875, 0]}], "ix": 2}, "a": {"a": 0, "k": [18, 12, 0], "ix": 1}, "s": {"a": 0, "k": [33.3, 33.3, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 2, "nm": "Vector 36.png", "cl": "png", "refId": "7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [220, 121, 0], "to": [-9.583, -8.625, 0], "ti": [9.583, 7.792, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [162.5, 69.25, 0], "to": [0.042, 5.208, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [162.5, 74.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [162.5, 69.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [162.5, 74.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [162.5, 69.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [162.5, 74.25, 0], "to": [0, 0, 0], "ti": [0, 0.833, 0]}, {"t": 160, "s": [162.5, 69.25, 0]}], "ix": 2}, "a": {"a": 0, "k": [8.5, 14, 0], "ix": 1}, "s": {"a": 0, "k": [33.3, 33.3, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 2, "nm": "Vector 37.png", "cl": "png", "refId": "8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [220, 121, 0], "to": [3.917, 12.208, 0], "ti": [-3.917, -13.042, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [243.5, 194.25, 0], "to": [0.042, 5.292, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [243.5, 199.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [243.5, 194.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [243.5, 199.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [243.5, 194.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [243.5, 199.25, 0], "to": [0, 0, 0], "ti": [0, 0.833, 0]}, {"t": 160, "s": [243.5, 194.25, 0]}], "ix": 2}, "a": {"a": 0, "k": [10, 7, 0], "ix": 1}, "s": {"a": 0, "k": [33.3, 33.3, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 2, "nm": "Layer 4 2.png", "cl": "png", "refId": "4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [0]}, {"t": 42.007, "s": [30]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [220, 121, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [298.5, 44.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [298.5, 39.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [298.5, 44.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [298.5, 39.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [298.5, 44.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [298.5, 39.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 160, "s": [298.5, 44.25, 0]}], "ix": 2}, "a": {"a": 0, "k": [13.65, 14.15, 0], "ix": 1}, "s": {"a": 0, "k": [51.94, 51.96, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 2, "nm": "Layer 4 2.png", "cl": "png", "refId": "4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [0]}, {"t": 42.007, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [220, 121, 0], "to": [16.713, 6.25, 0], "ti": [0.042, 0.417, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42.007, "s": [315.781, 157.25, 0], "to": [0.124, 0.09, 0], "ti": [-0.021, -0.271, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 61.488, "s": [315.75, 152.25, 0], "to": [0.149, 1.931, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.19, "s": [315.75, 157.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 100.893, "s": [315.75, 152.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120.596, "s": [315.75, 157.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 140.297, "s": [315.75, 152.25, 0], "to": [0, 0, 0], "ti": [0, -0.833, 0]}, {"t": 160, "s": [315.75, 157.25, 0]}], "ix": 2}, "a": {"a": 0, "k": [13.65, 14.15, 0], "ix": 1}, "s": {"a": 0, "k": [99.99, 100.02, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": [], "tiny": 0.8}