@import "@/styles/lib_var.less";

.nodeInfoBar {
  position: relative;
  // width: 100%;
  height: 100%;
  display: flex;
  padding-top: 3px;

  .nameWrapper {
    max-height: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    min-width: 0;

    &.editing {
      flex: 1;
    }
  }

  .permissionWrapper {
    display: flex;
    align-items: center;
  }
  //顶部表名前emoji（nodeInfoBar）
  .icon {
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    margin-right: 4px;

    &.iconHover {
      cursor: pointer;
      @media (any-hover: hover) {
        &:hover {
          background: var(--shadowColor);
        }
      }
    }
  }

  .nameInput {
    // flex: 1;
    margin-right: 9px;
    border: none;
    padding: 0;
    min-width: 20px;
    background-color: transparent;
    line-height: 0;
    outline: none;
    border: 1px solid #ccc;
    line-height: 24px;
    padding-left: 4px;
    border-radius: 5px;
  }

  .nameBox {
    // flex: 1;
    display: flex;
    min-width: 0;
  }

  .name {
    height: auto;
    // line-height: 0;
    flex: 1;
  }

  .namePre {
    visibility: hidden;
    position: absolute;
  }

  .tag {
    flex-shrink: 0;
    margin: 0 6px 0 9px;
  }
}

.multiLine {
  flex-direction: column;

  .permissionWrapper {
    flex-direction: row-reverse;
    justify-content: flex-end;
    padding-bottom: 2px;
  }
}
