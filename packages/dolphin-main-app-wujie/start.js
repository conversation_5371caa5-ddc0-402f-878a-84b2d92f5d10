// start.js
const next = require('next');
const port = 3111; // 设置你想要的端口号
const dev = process.env.NODE_ENV == 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  require('http').createServer((req, res) => {
    handle(req, res);
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://localhost:${port}`);
  });
});