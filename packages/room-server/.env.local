IMAGE_PULL_POLICY=always
IMAGE_REGISTRY=docker.io

IMAGE_BACKEND_SERVER=apitable/backend-server:latest
IMAGE_GATEWAY=apitable/openresty:latest
IMAGE_INIT_DB=apitable/init-db:latest
IMAGE_ROOM_SERVER=apitable/room-server:latest
IMAGE_WEB_SERVER=apitable/web-server:latest

IMAGE_DATABUS_SERVER=apitable/databus-server:latest
IMAGE_IMAGEPROXY_SERVER=apitable/imageproxy-server:latest
IMAGE_INIT_APPDATA=apitable/init-appdata:latest

IMAGE_MINIO=minio/minio:RELEASE.2023-01-25T00-19-54Z
IMAGE_MYSQL=mysql:8.0.32
IMAGE_RABBITMQ=rabbitmq:3.11.9-management
IMAGE_REDIS=redis:7.0.8

NGINX_HTTPS_PORT=443
NGINX_HTTP_PORT=80

### SERVER
BACKEND_BASE_URL=http://*************:8081/api/v1/
DATABUS_SERVER_BASE_URL=http://127.0.0.1:8625
ROOM_GRPC_URL=127.0.0.1:3334
SOCKET_GRPC_URL=127.0.0.1:3007

### NEST CONST
OSS_CACHE_TYPE=minio
OSS_HOST=assets
OSS_TYPE=QNY1

### MINIO
MINIO_ACCESS_KEY=apitable
MINIO_SECRET_KEY=apitable@com

### MYSQL
DATABASE_TABLE_PREFIX=kz_
MYSQL_DATABASE=apitable
MYSQL_HOST=*************
MYSQL_PASSWORD=ROOTcloudbest
MYSQL_PORT=3306
MYSQL_ROOT_PASSWORD=apitable@com
MYSQL_USERNAME=root

### init-db
DB_HOST=mysql
DB_NAME=apitable
DB_PASSWORD=apitable@com
DB_PORT=3306
DB_USERNAME=root

### REDIS
REDIS_DB=1
REDIS_HOST=*************
REDIS_PASSWORD=apitable@com
REDIS_PORT=6679

### RabbitMQ
RABBITMQ_HOST=*************
RABBITMQ_PASSWORD=apitable@com
RABBITMQ_PORT=8672
RABBITMQ_USERNAME=apitable
RABBITMQ_VHOST=/

TIMEZONE=Asia/Shanghai

ENV=apitable

API_PROXY=http://backend-server:8081
BACKEND_INFO_URL=http://backend-server:8081/api/v1/client/info
PUBLIC_URL=
TEMPLATE_PATH=./static/web_build/index.html
USE_CUSTOM_PUBLIC_FILES=true

NEST_GRPC_ADDRESS=static://*************:3334

### apitable const
CALLBACK_DOMAIN=
CORS_ORIGINS=*
SERVER_DOMAIN=http://www.fintechquan.cn
SOCKET_DOMAIN=http://*************:3333/socket

SKIP_REGISTER_VALIDATE=true
TEMPLATE_SPACE=spcNTxlv8Drra

### apitable starter
SOCKET_RECONNECTION_ATTEMPTS=10
SOCKET_RECONNECTION_DELAY=500
SOCKET_TIMEOUT=5000
SOCKET_URL=http://room-server:3002

SMS_ENABLED=false

MAIL_ENABLED=false
MAIL_FROM=
MAIL_HOST=
MAIL_PASSWORD=
MAIL_PORT=
MAIL_SSL_ENABLE=true
MAIL_TYPE=smtp
MAIL_USERNAME=

OSS_CLIENT_TYPE=aws
OSS_ENABLED=true

AWS_ACCESS_KEY=apitable
AWS_ACCESS_SECRET=apitable@com
AWS_ENDPOINT=http://*************:9000
AWS_REGION=us-east-1

HUAWEICLOUD_OBS_ACCESS_KEY=apitable
HUAWEICLOUD_OBS_ENDPOINT=obs.cn-south-1.myhuaweicloud.com
HUAWEICLOUD_OBS_SECRET_KEY=apitable@com

ASSETS_BUCKET=assets
ASSETS_URL=assets
OSS_BUCKET_NAME=assets

DATA_PATH=.

USE_NATIVE_MODULE=0