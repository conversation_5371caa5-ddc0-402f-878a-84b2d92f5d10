/**
方便查找进行整体删除
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

import { Span } from '@metinseylan/nestjs-opentelemetry';
import { Injectable } from '@nestjs/common';
import type { IPermissions } from '@apitable/core';
import { DatasheetException } from '../../../shared/exception';
import type { IFetchDataOriginOptions, IAuthHeader } from '../../../shared/interfaces';
import { omit } from 'lodash';
import type { DatasheetPack, MirrorInfo } from '../../interfaces';
import { DatasheetService } from 'database/datasheet/services/datasheet.service';
import { NodeService } from 'node/services/node.service';
import { ResourceMetaRepository } from 'database/resource/repositories/resource.meta.repository';
import { RestService } from 'shared/services/rest/rest.service';

@Injectable()
export class MirrorService {
  constructor(
    private readonly nodeService: NodeService,
    private readonly datasheetService: DatasheetService,
    private readonly resourceMetaRepository: ResourceMetaRepository,
    private readonly restService: RestService,
  ) {}

  async getMirrorInfo(mirrorId: string, auth: IAuthHeader, origin: IFetchDataOriginOptions): Promise<MirrorInfo> {
    const { node } = await this.nodeService.getNodeDetailInfo(mirrorId, auth, origin);
    this.rewriteMirrorPermission(node.permissions);
    // Query info of referenced database and view
    const nodeRelInfo = await this.nodeService.getNodeRelInfo(mirrorId);
    // Check if referenced datasheet exists
    await this.nodeService.checkNodeIfExist(nodeRelInfo.datasheetId, DatasheetException.DATASHEET_NOT_EXIST);
    const meta = await this.resourceMetaRepository.selectMetaByResourceId(mirrorId);
    return {
      mirror: omit(node, ['extra']),
      sourceInfo: nodeRelInfo,
      snapshot: meta,
    };
  }

  @Span()
  async fetchDataPack(
    mirrorId: string,
    auth: IAuthHeader,
    origin: IFetchDataOriginOptions,
    recordIds?: string[],
  ): Promise<DatasheetPack> {
    // Query info of referenced database and view
    const datasheetId = await this.nodeService.getMainNodeId(mirrorId);

    // 🆕 获取基础数据包
    const dataPack = await this.datasheetService.fetchCommonDataPack('mirror', datasheetId, auth, origin, true, {
      recordIds,
      metadataException: DatasheetException.DATASHEET_NOT_EXIST,
    });

    // 🆕 查询用户个性化配置并合并到数据包中，支持初始化逻辑
    if (dataPack.snapshot?.meta?.views && dataPack.snapshot.meta.views.length > 0) {
      // 处理所有视图的配置（虽然镜像通常只有一个视图）
      for (const view of dataPack.snapshot.meta.views) {
        try {
          const viewId = view.id;
          console.log('🔍 [MirrorService] Querying user config for view:', viewId);
          
          console.log(`🔍 [MirrorService] Querying user config for mirror: ${mirrorId}, view: ${viewId}`);
          const userConfig = await this.restService.getUserMirrorSettings(auth, mirrorId, viewId);
          console.log(`🔍 [MirrorService] User config response:`, JSON.stringify(userConfig, null, 2));
          
          if (userConfig?.success && userConfig?.data?.configData) {
            // 用户已有配置，直接使用
            console.log(`🔧 [MirrorService] Found user config, merging with view. Config data:`, userConfig.data.configData);
            const parsedConfig = JSON.parse(userConfig.data.configData);
            console.log(`🔧 [MirrorService] Parsed config:`, parsedConfig);
            console.log(`🔧 [MirrorService] View before merge:`, JSON.stringify(view, null, 2));
            Object.assign(view, parsedConfig);
            console.log(`🔧 [MirrorService] View after merge:`, JSON.stringify(view, null, 2));
            console.log('✅ [MirrorService] Successfully merged existing user config for view:', viewId);
          } else if (userConfig === null) {
            // 🆕 用户没有配置，需要初始化（只在真正没有配置时才初始化）
            console.log('🚀 [MirrorService] No user config found, initializing for view:', viewId);
            await this.initializeUserMirrorConfig(auth, mirrorId, viewId, view);
            console.log('✅ [MirrorService] Successfully initialized user mirror config for view:', viewId);
          } else {
            // 其他情况（比如查询失败）- 不进行初始化，使用原始配置
            console.warn(`⚠️ [MirrorService] Failed to query user config for view ${viewId}, using base config. Response:`, userConfig);
          }
        } catch (error) {
          // 单个视图配置查询失败不影响其他视图和基础功能
          console.warn(`⚠️ [MirrorService] Failed to load/initialize user config for view ${view.id}:`, error);
        }
      }
    } else {
      console.log('ℹ️ [MirrorService] No views found in mirror data pack');
    }

    return dataPack;
  }

  /**
   * 🆕 初始化用户镜像配置
   */
  private async initializeUserMirrorConfig(auth: IAuthHeader, mirrorId: string, viewId: string, baseView: any): Promise<void> {
    try {
      // 从基础视图配置中提取需要初始化的字段
      const baseConfig = this.extractBaseConfig(baseView);
      const configData = JSON.stringify(baseConfig);
      
      console.log(`🔧 [MirrorService] Initializing config with data: ${configData.substring(0, 200)}...`);
      
      // 调用后端API保存初始配置
      await this.restService.saveUserMirrorSettings(auth, mirrorId, viewId, configData);
      
      console.log('📝 [MirrorService] Successfully saved initial mirror config to database');
    } catch (error) {
      console.error(`❌ [MirrorService] Failed to initialize user mirror config:`, error);
      // 初始化失败不影响数据加载
    }
  }

  /**
   * 🆕 从基础视图中提取需要初始化的配置
   */
  private extractBaseConfig(baseView: any): any {
    const config: any = {};
    
    // 列配置（列宽、隐藏状态等）
    if (baseView.columns) {
      config.columns = baseView.columns.map((col: any) => ({
        fieldId: col.fieldId,
        width: col.width,
        hidden: col.hidden || false,
        ...(col.statType && { statType: col.statType })
      }));
    }
    
    // 筛选配置
    if (baseView.filterInfo) {
      config.filterInfo = JSON.parse(JSON.stringify(baseView.filterInfo));
    }
    
    // 排序配置
    if (baseView.sortInfo) {
      config.sortInfo = JSON.parse(JSON.stringify(baseView.sortInfo));
    }
    
    // 分组配置
    if (baseView.groupInfo) {
      config.groupInfo = JSON.parse(JSON.stringify(baseView.groupInfo));
    }
    
    // 行高配置
    if (baseView.rowHeightLevel !== undefined) {
      config.rowHeightLevel = baseView.rowHeightLevel;
    }
    
    // 冻结列配置
    if (baseView.frozenColumnCount !== undefined) {
      config.frozenColumnCount = baseView.frozenColumnCount;
    }
    
    // 其他可能的配置
    if (baseView.displayHiddenColumnWithinMirror !== undefined) {
      config.displayHiddenColumnWithinMirror = baseView.displayHiddenColumnWithinMirror;
    }
    
    console.log(`🎯 [MirrorService] Extracted config keys: ${Object.keys(config).join(', ')}`);
    
    return config;
  }

  public rewriteMirrorPermission(permissions: IPermissions) {
    if (permissions.editable === false) {
      return;
    }
    // View operation limits
    permissions.viewCreatable = false;
    permissions.viewRemovable = false;
    permissions.viewMovable = false;
    permissions.viewRenamable = false;

    // View configuration limits
    permissions.columnHideable = false;
    permissions.viewFilterable = false;
    permissions.fieldGroupable = false;
    permissions.columnSortable = false;
    permissions.rowHighEditable = false;
    permissions.viewLayoutEditable = false;
    permissions.viewStyleEditable = false;
    permissions.viewKeyFieldEditable = false;
    permissions.viewColorOptionEditable = false;

    // Field operation limits
    permissions.fieldCreatable = false;
    permissions.fieldRenamable = false;
    permissions.fieldPropertyEditable = false;
    permissions.fieldRemovable = false;

    // Field order, width and statistics bar limits
    permissions.fieldSortable = false;
    permissions.columnWidthEditable = false;
    permissions.columnCountEditable = true;

    // Node description limits
    permissions.descriptionEditable = false;

    // Field permission settings limits
    permissions.fieldPermissionManageable = false;
  }
}
