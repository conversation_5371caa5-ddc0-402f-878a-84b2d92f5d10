# Install dependencies only when needed
FROM apitable/nodepy:16.15.0-alpine AS deps

WORKDIR /workspace-install

COPY ./pnpm-workspace.yaml ./package.json ./pnpm-lock.yaml ./

# components
COPY packages/components/package.json ./packages/components/

RUN pnpm install

# Build static files
FROM apitable/nodepy:16.15.0-alpine AS builder

WORKDIR /app

COPY --from=deps /workspace-install/node_modules ./node_modules
COPY --from=deps /workspace-install/packages/components/node_modules ./packages/components/node_modules

COPY ./pnpm-workspace.yaml ./package.json ./common-tsconfig.json ./pnpm-lock.yaml ./
COPY packages/ ./packages/

RUN pnpm run build:components:doc

FROM nginx:1.23.1
COPY --from=builder /app/packages/components/storybook-static  /usr/share/nginx/html
RUN find /usr/share/nginx/html
