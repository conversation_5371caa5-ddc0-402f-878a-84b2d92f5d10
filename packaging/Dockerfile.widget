# 使用官方的 Node.js 镜像，指定版本为 20.6.0
FROM node:20.6.0

# 设置工作目录
WORKDIR /app

# 打印 Node.js 和 npm 版本，帮助调试
RUN node -v
RUN npm -v

# 安装指定版本的 npm
RUN npm install -g npm@10.7.0


# 安装 widget-cli
RUN npm install -g @apitable/widget-cli

# 复制当前目录的所有文件到容器的 /app 目录中
COPY packages/widget-cb/widget-feishu-import ./widget-feishu-import

# 切换到特定目录并安装项目依赖
WORKDIR /app/widget-feishu-import

RUN npm install -g pnpm

RUN pnpm  install  yarn

# 安装项目依赖
RUN pnpm install
RUN pnpm install js-cookie


# 暴露应用运行的端口（假设 widget-cli 默认端口为9000，你可以根据需要修改）
EXPOSE 9000

# 启动应用
#CMD ["widget-cli", "start"]


# 保持容器运行
CMD ["sleep", "infinity"]
