
log_format json_combined escape=json '{"@timestamp":"$time_iso8601",'
              '"@source":"$server_addr",'
              '"@nginx_fields":{'
              '"remote_addr":"$remote_addr",'
              '"body_bytes_sent":"$body_bytes_sent",'
              '"request_time":"$request_time",'
              '"status":"$status",'
              '"host":"$host",'
              '"uri":"$uri",'
              '"server":"$server_name",'
              '"request_uri":"$request_uri",'
              '"request_method":"$request_method",'
              '"http_referrer":"$http_referer",'
              '"body_bytes_sent":"$body_bytes_sent",'
              '"http_x_forwarded_for":"$http_x_forwarded_for",'
              '"http_user_agent":"$http_user_agent",'
              '"upstream_response_time":"$upstream_response_time",'
              '"upstream_status":"$upstream_status",'
              '"upstream_addr":"$upstream_addr"}}';

access_log  /dev/stdout json_combined;
server_tokens off;

##cache##

proxy_buffer_size 16k;
proxy_buffers 4 64k;
proxy_busy_buffers_size 128k;
proxy_cache_path /tmp/cache levels=1:2 keys_zone=cache_one:200m inactive=1d max_size=3g;

gzip on;
gzip_buffers 16 8k;
gzip_comp_level 5;
gzip_http_version 1.1;
gzip_min_length 1024;
gzip_proxied any;
gzip_types application/javascript application/json application/x-javascript text/css text/javascript text/plain;
gzip_vary on;

include /etc/nginx/conf.d/upstream/*.conf;
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

server {

      listen       80 default;
      server_name  127.0.0.1;
      client_max_body_size    1000m;
      charset utf-8;
      error_page 404 502 503   /404;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Real-PORT $remote_port;
      proxy_set_header X-Original-URI $request_uri;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      add_header X-Powered-By 'APITable';
      

      include /etc/nginx/conf.d/server/*.conf;
}

include /etc/nginx/conf.d/vhost/*.conf;
