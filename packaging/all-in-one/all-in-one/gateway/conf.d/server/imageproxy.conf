
location /assets {
  proxy_set_header Host 'minio:9000';
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;

  proxy_connect_timeout 300;
  proxy_http_version 1.1;
  proxy_set_header Connection "";
  chunked_transfer_encoding off;
  proxy_pass   http://imageproxy/image/assets/;
  if ( $args !~* ^imageView ){
      proxy_pass   http://minio;
  }

  if ( $request_method = PUT ){
      proxy_pass http://minio;
  }
    
}

location =/assets {
    return 403;
}

