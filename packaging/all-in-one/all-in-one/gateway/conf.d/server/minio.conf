
location ~* ^/(minio) {
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;
  proxy_set_header Access-Control-Allow-Origin '*' ;
  proxy_set_header 'Access-Control-Allow-Credentials' 'true';
  proxy_connect_timeout 300;
  proxy_http_version 1.1;
  proxy_set_header Connection "";
  chunked_transfer_encoding off;
  proxy_pass   http://minio;
}


location ^~/oss {
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;

  proxy_connect_timeout 300;
  proxy_http_version 1.1;
  proxy_set_header Connection "";
  proxy_set_header Access-Control-Allow-Origin '*' ;
  proxy_set_header 'Access-Control-Allow-Credentials' 'true';
  chunked_transfer_encoding off;
  proxy_pass   http://minio;
}