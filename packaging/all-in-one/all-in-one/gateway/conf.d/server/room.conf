
location ~* ^/(actuator|nest) {
  proxy_pass   http://room;
}

location /fusion {
  proxy_pass   http://room;

  # Fusion API CORS
  add_header 'Access-Control-Allow-Origin' '*' always;
  add_header 'Access-Control-Allow-Credentials' 'true' always;
  add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, DELETE, PATCH' always;
  add_header 'Access-Control-Allow-Headers' 'Authorization,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range,X-APITable-User-Agent,X-Vika-User-Agent' always;
  if ($request_method = 'OPTIONS' ) {
    return 204;
  }
}

location /room {
  proxy_pass   http://socketRoom;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection $connection_upgrade;
  proxy_set_header X-Nginx-Proxy true;
  proxy_set_header Host $host:$server_port;
  proxy_http_version 1.1;
  proxy_connect_timeout 300s;
  proxy_read_timeout 300s;
  proxy_send_timeout 300s;
}

location /notification {
  proxy_pass   http://socket;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection $connection_upgrade;
  proxy_set_header X-Nginx-Proxy true;
  proxy_set_header Host $host:$server_port;
  proxy_http_version 1.1;
  proxy_connect_timeout 300s;
  proxy_read_timeout 300s;
  proxy_send_timeout 300s;
}

location /document {
  proxy_pass   http://document;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection $connection_upgrade;
  proxy_set_header X-Nginx-Proxy true;
  proxy_set_header Host $host:$server_port;
  proxy_http_version 1.1;
  proxy_connect_timeout 300s;
  proxy_read_timeout 300s;
  proxy_send_timeout 300s;
}
