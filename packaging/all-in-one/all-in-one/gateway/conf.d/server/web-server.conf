location / {
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;

  proxy_connect_timeout 300;
  proxy_http_version 1.1;
  proxy_set_header Connection "";
  chunked_transfer_encoding off;
  proxy_pass   http://web-server;
  add_header 'Cache-Control' 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
}

location ~* ^/(login|space|user|invite|share|template|404|workbench|org|management|notify|widget-stage|embed|custom|file) {
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;

  proxy_connect_timeout 300;
  proxy_http_version 1.1;
  proxy_set_header Connection "";
  chunked_transfer_encoding off;
  proxy_pass   http://web-server;
  add_header 'Cache-Control' 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
}

location /_next {
    proxy_pass   http://web-server;
    proxy_connect_timeout 300;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $connection_upgrade;
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /web_build {
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;
  proxy_pass   http://web-server/web_build;
  proxy_set_header Access-Control-Allow-Origin $http_host ;
  proxy_set_header 'Access-Control-Allow-Credentials' 'true';
  #add_header Access-Control-Allow-Origin *;
  proxy_redirect off;
  proxy_cache cache_one;
  proxy_cache_valid 200 302 1h;
  proxy_cache_valid 301 1d;
  proxy_cache_valid any 1m;
  expires 7d;
}

location /widget-stage {
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;
  proxy_pass   http://web-server/widget-stage;
  proxy_set_header Access-Control-Allow-Origin $http_host ;
  proxy_set_header 'Access-Control-Allow-Credentials' 'true';
  #add_header Access-Control-Allow-Origin *;
  proxy_redirect off;
  proxy_cache cache_one;
  proxy_cache_valid 200 302 1h;
  proxy_cache_valid 301 1d;
  proxy_cache_valid any 1m;
  expires 7d;
}
