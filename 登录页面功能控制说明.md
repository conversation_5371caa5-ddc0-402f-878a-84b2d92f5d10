# 登录页面功能控制说明

## 概述

本文档说明如何使用环境变量来控制登录页面的功能，包括登录方式选择和注册功能显示。

## 环境变量配置

### 1. IS_EXTERNAL_VERSION - 登录方式控制

**作用**: 控制登录页面的默认登录方式
**取值**: 
- `true`: 使用 SignIn（邮箱密码登录）
- `false` 或未设置: 使用 FeishuLogin（飞书登录）

**示例**:
```bash
# 对外版 - 使用邮箱密码登录
IS_EXTERNAL_VERSION=true

# 内部版 - 使用飞书登录  
IS_EXTERNAL_VERSION=false
```

### 2. HIDE_SIGNUP - 注册功能控制

**作用**: 控制是否显示注册相关的入口和功能
**取值**:
- `true`: 隐藏注册功能
- `false` 或未设置: 显示注册功能

**示例**:
```bash
# 隐藏注册功能
HIDE_SIGNUP=true

# 显示注册功能
HIDE_SIGNUP=false
```

## 功能实现

### 登录方式控制

在 `pc_home.tsx` 和 `mobile_home.tsx` 中，使用 `getPreferredLoginAction()` 函数来决定默认的登录方式：

```typescript
useMount(() => {
  if (router.asPath.includes('sumo')) {
    setAction(ActionType.BindAppSumo);
  } else {
    // 根据环境变量决定登录方式
    const preferredAction = getPreferredLoginAction();
    setAction(preferredAction === 'SignIn' ? ActionType.SignIn : ActionType.FeishuLogin);
  }
});
```

### 注册功能控制

在登录组件中使用 `isSignupHidden()` 函数来控制注册入口的显示：

```typescript
{!isSignupHidden() && (
  <div className={styles.switchContent}>
    <p>{t(Strings.apitable_no_account)}</p>
    <LinkButton underline={false} component="button" onClick={() => switchClick(ActionType.SignUp)} style={{ paddingRight: 0 }}>
      {t(Strings.apitable_sign_up)}
    </LinkButton>
  </div>
)}
```

## 工具函数

### getPreferredLoginAction()

**位置**: `packages/datasheet/src/pc/utils/env.ts`

**功能**: 根据 `IS_EXTERNAL_VERSION` 环境变量返回推荐的登录方式
**返回值**: `'SignIn' | 'FeishuLogin'`

### isSignupHidden()

**位置**: `packages/datasheet/src/pc/utils/env.ts`

**功能**: 根据 `HIDE_SIGNUP` 环境变量判断是否隐藏注册功能
**返回值**: `boolean`

## 环境变量定义

环境变量在以下文件中定义：

1. `packages/dolphin-main-app-wujie/src/utils/get_env.ts` - 主要环境变量定义
2. `packages/datasheet/src/get_env.ts` - 本地环境变量定义

## 使用示例

### 对外版本配置
```bash
# 对外版本 - 允许注册，使用邮箱密码登录
IS_EXTERNAL_VERSION=true
HIDE_SIGNUP=false
```

### 内部版本配置
```bash
# 内部版本 - 隐藏注册，使用飞书登录
IS_EXTERNAL_VERSION=false
HIDE_SIGNUP=true
```

### 企业版本配置
```bash
# 企业版本 - 隐藏注册，使用邮箱密码登录
IS_EXTERNAL_VERSION=true
HIDE_SIGNUP=true
```

## 注意事项

1. 环境变量的修改需要重启应用才能生效
2. 确保在部署时正确设置这些环境变量
3. 这些配置会影响所有用户的登录体验，请谨慎配置 