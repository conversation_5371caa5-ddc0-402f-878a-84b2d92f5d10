# 镜像WebSocket数据传递优化说明

## 概述

本次优化解决了镜像操作时WebSocket数据传递的问题，确保镜像和datasheet传递正确的resourceId。

## 问题描述

在镜像环境中，WebSocket传递的数据结构如下：

```json
[
  "CLIENT_ROOM_CHANGE",
  {
    "type": "CLIENT_ROOM_CHANGE",
    "roomId": "mir8WKX9Rx072riK7r",
    "changesets": [
      {
        "messageId": "GnXP1uM9kwMbvRNjksHf",
        "baseRevision": 68,
        "resourceId": "dstVyzemutuB0RElR3",  // 这里传递的是数据源ID
        "resourceType": 0,
        "operations": [
          {
            "cmd": "SetRowHeight",
            "actions": [...]
          }
        ]
      }
    ]
  }
]
```

**问题**：镜像操作时，`resourceId` 传递的是数据源表的ID（dstId），而不是镜像的ID。

## 解决方案

### 1. 操作类型判断

根据操作类型来决定传递哪个ID：

- **镜像视图操作**：传递镜像ID（mirrorId）
- **数据操作**：传递数据源ID（dstId）

### 2. 镜像视图操作类型

以下操作被认为是镜像视图操作，会传递镜像ID：

```typescript
const viewConfigCommands = [
  CollaCommandName.SetRowHeight,           // 设置行高
  CollaCommandName.ModifyViews,            // 修改视图
  CollaCommandName.SetViewFilter,          // 设置视图筛选
  CollaCommandName.SetGroup,               // 设置分组
  CollaCommandName.MoveColumn,             // 移动列
  CollaCommandName.SetColumnsProperty,     // 设置列属性
  CollaCommandName.SetSortInfo,            // 设置排序
  CollaCommandName.SetViewFrozenColumnCount, // 设置冻结列数
  CollaCommandName.SetGalleryStyle,        // 画廊样式
  CollaCommandName.SetKanbanStyle,         // 看板样式
  CollaCommandName.SetGanttStyle,          // 甘特图样式
  CollaCommandName.SetCalendarStyle,       // 日历样式
  CollaCommandName.SetOrgChartStyle,       // 组织架构图样式
  CollaCommandName.SetAutoHeadHeight,      // 自动行高
];
```

### 3. 实现逻辑

在 `execute_command_with_mirror.ts` 中：

```typescript
// 判断操作类型，决定使用哪个ID
const isMirrorOperation = changeset.operations?.some((op: any) => {
  if (!op.cmd) return false;
  
  // 使用已定义的viewConfigCommands来判断是否为镜像视图操作
  return viewConfigCommands.includes(op.cmd as CollaCommandName);
});

// 如果是镜像视图操作，使用镜像ID；否则使用数据源ID
const targetResourceId = isMirrorOperation ? mirrorId : mirror?.sourceInfo?.datasheetId;
```

## 优化效果

### 优化前
- 所有操作都传递数据源ID（dstId）
- 镜像视图配置无法正确保存

### 优化后
- **镜像视图操作**：传递镜像ID（mirrorId）
- **数据操作**：传递数据源ID（dstId）
- 镜像视图配置可以正确保存和同步

## 示例

### 镜像视图操作（传递镜像ID）
```json
{
  "resourceId": "mir8WKX9Rx072riK7r",  // 镜像ID
  "operations": [
    {
      "cmd": "SetRowHeight",
      "actions": [...]
    }
  ]
}
```

### 数据操作（传递数据源ID）
```json
{
  "resourceId": "dstVyzemutuB0RElR3",  // 数据源ID
  "operations": [
    {
      "cmd": "SetRecords",
      "actions": [...]
    }
  ]
}
```

## 注意事项

1. **向后兼容**：此修改不影响现有的datasheet操作
2. **日志记录**：所有操作都会记录详细的调试信息
3. **错误处理**：包含完整的错误恢复机制
4. **性能影响**：判断逻辑轻量，对性能影响极小

## 测试建议

1. **镜像视图配置**：测试各种视图配置的保存和同步
2. **数据操作**：确保数据操作仍然正常工作
3. **混合操作**：测试同时包含视图配置和数据操作的场景
4. **错误恢复**：测试网络异常时的恢复机制 