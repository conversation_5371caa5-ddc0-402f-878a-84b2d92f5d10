# 镜像WebSocket版本错误问题分析与解决方案

## 问题描述

### 发送的数据
```json
[
  "CLIENT_ROOM_CHANGE",
  {
    "type": "CLIENT_ROOM_CHANGE",
    "roomId": "mir8WKX9Rx072riK7r",
    "changesets": [
      {
        "messageId": "W4qN1ylgrLYZLmFm6GFf",
        "baseRevision": 69,
        "resourceId": "mir8WKX9Rx072riK7r",
        "resourceType": 0,
        "operations": [
          {
            "cmd": "SetRowHeight",
            "actions": [...]
          }
        ]
      }
    ]
  }
]
```

### 收到的错误
```json
{
  "success": false,
  "code": 4004,
  "message": "Version Error"
}
```

## 问题分析

### 1. 错误代码分析
- **错误代码**: 4004 (Version Error)
- **错误类型**: 版本不匹配错误
- **可能原因**:
  - 客户端发送的 `baseRevision` 与服务器端当前版本不匹配
  - 镜像环境下的版本管理机制与datasheet不同

### 2. 数据流分析
1. **镜像操作**: 设置行高 (SetRowHeight)
2. **resourceId**: 正确传递了镜像ID (mir8WKX9Rx072riK7r)
3. **baseRevision**: 69 (可能不正确)

### 3. 根本原因
镜像环境下的版本管理存在以下问题：
1. **版本号获取错误**: 镜像操作时使用了数据源的版本号，而不是镜像的版本号
2. **版本同步问题**: 镜像和数据源的版本号可能不同步
3. **服务器端处理**: room-server 可能没有正确处理镜像的版本管理

## 解决方案

### 前端修改 (datasheet/core)

#### 1. 修改版本获取逻辑
在 `execute_command_with_mirror.ts` 中：

```typescript
// 🔧 关键修复：镜像操作时，需要获取镜像的版本号
let targetBaseRevision = changeset.baseRevision;
if (isMirrorOperation && targetResourceId === mirrorId) {
  // 获取镜像的当前版本号
  const state = store.getState();
  const mirrorRevision = Selectors.getResourceRevision(state, mirrorId, ResourceType.Mirror);
  if (mirrorRevision !== undefined) {
    targetBaseRevision = mirrorRevision;
    console.log('🔧 镜像操作：使用镜像版本号', {
      originalBaseRevision: changeset.baseRevision,
      mirrorRevision,
      mirrorId
    });
  }
}
```

#### 2. 修改后的数据流
```json
{
  "resourceId": "mir8WKX9Rx072riK7r",  // 镜像ID
  "baseRevision": 0,                    // 镜像的当前版本号
  "operations": [{"cmd": "SetRowHeight"}]
}
```

### 后端分析 (room-server)

#### 1. 需要检查的文件
- `packages/room-server/src/database/ot/services/ot.service.ts`
- `packages/room-server/src/socket/services/room/room.service.ts`
- `packages/room-server/src/database/mirror/services/mirror.service.ts`

#### 2. 可能需要的修改

##### A. 镜像版本管理
```typescript
// 在 ot.service.ts 中添加镜像版本处理
if (resourceId.startsWith('mir')) {
  // 镜像版本管理逻辑
  const mirrorRevision = await this.getMirrorRevision(resourceId);
  if (baseRevision !== mirrorRevision) {
    // 版本不匹配处理
  }
}
```

##### B. 镜像操作处理
```typescript
// 在 room.service.ts 中添加镜像操作处理
if (message.roomId?.startsWith('mir')) {
  // 镜像操作的特殊处理逻辑
  await this.handleMirrorOperation(message);
}
```

#### 3. 建议的room-server修改

##### A. 添加镜像版本获取方法
```typescript
// 在 mirror.service.ts 中
async getMirrorRevision(mirrorId: string): Promise<number> {
  // 获取镜像的当前版本号
  const mirror = await this.getMirrorInfo(mirrorId);
  return mirror.revision || 0;
}
```

##### B. 修改OT服务处理逻辑
```typescript
// 在 ot.service.ts 中
async processChangeset(changeset: ILocalChangeset) {
  const { resourceId, baseRevision } = changeset;
  
  if (resourceId.startsWith('mir')) {
    // 镜像版本处理
    const mirrorRevision = await this.getMirrorRevision(resourceId);
    if (baseRevision !== mirrorRevision) {
      throw new Error('Mirror version mismatch');
    }
  } else {
    // 原有的datasheet版本处理
    const dbRevision = await this.getRevisionByResourceId(resourceId);
    if (baseRevision !== dbRevision) {
      // 版本转换逻辑
    }
  }
}
```

## 实施计划

### 第一阶段：前端修复 ✅
- [x] 修改镜像版本获取逻辑
- [x] 确保镜像操作使用正确的版本号
- [x] 添加详细的日志记录

### 第二阶段：后端分析
- [ ] 分析room-server的镜像版本处理逻辑
- [ ] 确认镜像版本管理机制
- [ ] 制定room-server修改方案

### 第三阶段：后端修改
- [ ] 实现镜像版本获取方法
- [ ] 修改OT服务处理逻辑
- [ ] 添加镜像操作的特殊处理

## 测试建议

### 1. 前端测试
- [ ] 测试镜像视图配置操作
- [ ] 验证版本号获取正确性
- [ ] 检查日志输出

### 2. 后端测试
- [ ] 测试镜像版本管理
- [ ] 验证版本同步机制
- [ ] 检查错误处理

### 3. 集成测试
- [ ] 端到端镜像操作测试
- [ ] 并发操作测试
- [ ] 错误恢复测试

## 注意事项

1. **向后兼容**: 确保修改不影响现有的datasheet操作
2. **版本同步**: 镜像和数据源的版本可能需要同步机制
3. **错误处理**: 添加完善的错误处理和恢复机制
4. **性能影响**: 确保修改不会显著影响性能

## 下一步行动

1. **立即实施**: 前端修改已完成，可以立即测试
2. **后端分析**: 需要深入分析room-server的镜像处理逻辑
3. **方案确认**: 等待确认后端修改方案后再实施 